package com.data.platform.datamind.server.datameta.vo.database;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:35
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

/**
 * 数据库/数据源元数据 Response VO
 */
@Schema(description = "管理后台 - 数据库/数据源元数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaDatabaseRespVO extends MetaDatabaseBaseVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    // 注意：RespVO 不包含敏感信息如密码，或者只包含加密后的信息
    // 其他字段继承自 MetaDatabaseBaseVO
}
