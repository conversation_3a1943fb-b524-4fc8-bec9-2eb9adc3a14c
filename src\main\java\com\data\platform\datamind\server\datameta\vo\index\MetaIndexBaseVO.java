package com.data.platform.datamind.server.datameta.vo.index;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 索引元数据 Base VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.vo.index
 * @description 索引元数据基础VO
 * @email <EMAIL>
 * @since 1.8
 */
@Data
public class MetaIndexBaseVO {

    @Schema(description = "关联表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "表ID不能为空")
    private Long tableId;

    @Schema(description = "关联数据库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据库ID不能为空")
    private Long dbId;

    @Schema(description = "索引名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "idx_user_name")
    @NotEmpty(message = "索引名称不能为空")
    private String indexName;

    @Schema(description = "索引类型", example = "INDEX")
    private String indexType;

    @Schema(description = "列名", requiredMode = Schema.RequiredMode.REQUIRED, example = "user_name")
    @NotEmpty(message = "列名不能为空")
    private String columnName;

    @Schema(description = "列在索引中的位置", example = "1")
    private Integer columnPosition;

    @Schema(description = "是否唯一索引", example = "false")
    private Boolean isUnique;

    @Schema(description = "是否主键", example = "false")
    private Boolean isPrimary;

    @Schema(description = "索引注释", example = "用户名索引")
    private String indexComment;

    @Schema(description = "索引基数", example = "10000")
    private Long cardinality;

    @Schema(description = "索引长度", example = "255")
    private Integer indexLength;

    @Schema(description = "排序方式", example = "ASC")
    private String sortOrder;

    @Schema(description = "表名", example = "user_info")
    private String tableName;

    @Schema(description = "表所有者", example = "root")
    private String tableOwner;
}
