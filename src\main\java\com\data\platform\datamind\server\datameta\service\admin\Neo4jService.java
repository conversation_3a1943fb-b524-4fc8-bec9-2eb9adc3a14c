package com.data.platform.datamind.server.datameta.service.admin;

import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Neo4j 基础服务接口
 * 提供Neo4j数据库的基础操作功能
 *
 * <AUTHOR> Team
 */
public interface Neo4jService {

    /**
     * 执行Cypher查询语句
     *
     * @param cypher Cypher查询语句
     * @param parameters 查询参数
     * @return 查询结果
     */
    List<Map<String, Object>> executeCypher(String cypher, Map<String, Object> parameters);

    /**
     * 执行Cypher更新语句
     *
     * @param cypher Cypher更新语句
     * @param parameters 更新参数
     * @return 影响的记录数
     */
    long executeUpdate(String cypher, Map<String, Object> parameters);

    /**
     * 创建节点
     *
     * @param label 节点标签
     * @param properties 节点属性
     * @return 创建的节点ID
     */
    Long createNode(String label, Map<String, Object> properties);

    /**
     * 创建关系
     *
     * @param sourceNodeId 源节点ID
     * @param targetNodeId 目标节点ID
     * @param relationshipType 关系类型
     * @param properties 关系属性
     * @return 创建的关系ID
     */
    Long createRelationship(Long sourceNodeId, Long targetNodeId, String relationshipType, Map<String, Object> properties);

    /**
     * 删除节点
     *
     * @param nodeId 节点ID
     */
    void deleteNode(Long nodeId);

    /**
     * 删除关系
     *
     * @param relationshipId 关系ID
     */
    void deleteRelationship(Long relationshipId);

    /**
     * 查询节点
     *
     * @param label 节点标签
     * @param properties 查询条件
     * @return 节点列表
     */
    List<Map<String, Object>> findNodes(String label, Map<String, Object> properties);

    /**
     * 查询关系
     *
     * @param relationshipType 关系类型
     * @param properties 查询条件
     * @return 关系列表
     */
    List<Map<String, Object>> findRelationships(String relationshipType, Map<String, Object> properties);

    /**
     * 清空数据库
     */
    void clearDatabase();

    /**
     * 获取数据库统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getDatabaseStats();

    /**
     * 检查数据库连接
     *
     * @return 连接状态
     */
    boolean checkConnection();

    /**
     * 批量创建节点
     *
     * @param label 节点标签
     * @param nodesList 节点属性列表
     * @return 创建的节点数量
     */
    int batchCreateNodes(String label, List<Map<String, Object>> nodesList);

    /**
     * 批量创建关系
     *
     * @param relationshipType 关系类型
     * @param relationshipsList 关系列表
     * @return 创建的关系数量
     */
    int batchCreateRelationships(String relationshipType, List<Map<String, Object>> relationshipsList);

    /**
     * 执行事务操作
     *
     * @param cypherStatements Cypher语句列表
     * @return 执行结果
     */
    List<Map<String, Object>> executeTransaction(List<String> cypherStatements);

    /**
     * 查找两个节点之间的路径
     *
     * @param sourceNodeId 源节点ID
     * @param targetNodeId 目标节点ID
     * @param maxDepth 最大深度
     * @return 路径信息
     */
    List<Map<String, Object>> findPath(Long sourceNodeId, Long targetNodeId, int maxDepth);

    /**
     * 查找节点的邻居
     *
     * @param nodeId 节点ID
     * @param direction 方向（IN/OUT/BOTH）
     * @param relationshipTypes 关系类型列表
     * @return 邻居节点列表
     */
    List<Map<String, Object>> findNeighbors(Long nodeId, String direction, List<String> relationshipTypes);
}
