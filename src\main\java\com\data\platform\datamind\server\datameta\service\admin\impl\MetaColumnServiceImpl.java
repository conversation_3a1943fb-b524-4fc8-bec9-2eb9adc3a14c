package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaColumnConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.mysql.MetaColumnMapper;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnPageReqVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnUpdateReqVO;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 列元数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MetaColumnServiceImpl implements MetaColumnService {

    @Resource
    private MetaColumnMapper metaColumnMapper;

    @Override
    public Long createColumn(MetaColumnCreateReqVO createReqVO) {
        // 校验名称在同一表下唯一性
        validateColumnNameUnique(null, createReqVO.getTableId(), createReqVO.getColumnName());
        // VO -> DO
        MetaColumnDO column = MetaColumnConvert.INSTANCE.convert(createReqVO);
        metaColumnMapper.insert(column);
        return column.getId();
    }

    @Override
    public void updateColumn(MetaColumnUpdateReqVO updateReqVO) {
        // 校验存在
//        ValidationUtils.verifyNotNull(metaColumnMapper.selectById(updateReqVO.getId()), METACOLUMN_NOT_EXISTS);
        // 校验名称在同一表下唯一性
//        validateColumnNameUnique(updateReqVO.getId(), updateReqVO.getTableId(), updateReqVO.getColumnName());

        // VO -> DO
        MetaColumnDO column = MetaColumnConvert.INSTANCE.convert(updateReqVO);
        metaColumnMapper.updateById(column);
    }

    @Override
    public void deleteColumn(Long id) {
        // 校验存在
//        ValidationUtils.verifyNotNull(metaColumnMapper.selectById(id), METACOLUMN_NOT_EXISTS);
        metaColumnMapper.deleteById(id);
    }

    @Override
    public MetaColumnDO getColumn(Long id) {
        return metaColumnMapper.selectById(id);
    }

    @Override
    public List<MetaColumnDO> getColumnList(Collection<Long> ids) {
        return metaColumnMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MetaColumnDO> getColumnPage(MetaColumnPageReqVO pageReqVO) {
//        return metaColumnMapper.selectPage(pageReqVO);
        return null;
    }

    @Override
    public List<MetaColumnDO> getColumnsByTableId(Long tableId) {
        return metaColumnMapper.selectListByTableId(tableId);
    }

    @Override
    public MetaColumnDO getColumnByTableIdAndColumnName(Long tableId, String columnName) {
        return metaColumnMapper.selectByTableIdAndColumnName(tableId, columnName);
    }

    @Override
    public Long getColumnIdByTableIdAndColumnName(Long tableId, String columnName) {
        MetaColumnDO column = getColumnByTableIdAndColumnName(tableId, columnName);
        return column != null ? column.getId() : null;
    }

    @Override
    @Transactional
    public void batchInsert(List<MetaColumnDO> columns) {
        if (columns.isEmpty()) {
            return;
        }
        metaColumnMapper.insertBatch(columns); // 使用 Mybatis-Plus 提供的 insertBatch
    }

    @Override
    @Transactional
    public void batchUpdate(List<MetaColumnDO> columns) {
        if (columns.isEmpty()) {
            return;
        }
//        metaColumnMapper.updateBatchById(columns); // 使用 Mybatis-Plus 提供的 updateBatchById
    }

    @Override
    public void deleteColumnsByTableIdAndColumnNames(Long tableId, List<String> columnNames) {
        if (columnNames.isEmpty()) {
            return;
        }
        metaColumnMapper.delete(new LambdaQueryWrapper<MetaColumnDO>()
                .eq(MetaColumnDO::getTableId, tableId)
                .in(MetaColumnDO::getColumnName, columnNames));
    }

    @Override
    public List<MetaColumnDO> getAllColumns() {
        return metaColumnMapper.selectList();
    }

    @Override
    public List<MetaColumnDO> getColumnsByDbId(Long dbId) {
        return metaColumnMapper.selectListByDbId(dbId);
    }

    private void validateColumnNameUnique(Long id, Long tableId, String columnName) {
        MetaColumnDO column = metaColumnMapper.selectByTableIdAndColumnName(tableId, columnName);
        if (column == null) {
            return;
        }
        // 如果 id 为空，说明是新增
//        if (id == null) {
//            throw exception(METACOLUMN_NAME_DUPLICATE);
//        }
//        // 如果 id 不为空，说明是修改，且名字重复的不是自己
//        if (!column.getId().equals(id)) {
//            throw exception(METACOLUMN_NAME_DUPLICATE);
//        }
    }
}
