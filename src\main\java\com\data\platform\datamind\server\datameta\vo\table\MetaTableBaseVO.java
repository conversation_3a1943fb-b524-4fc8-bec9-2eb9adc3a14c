package com.data.platform.datamind.server.datameta.vo.table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:50
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo.column
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 表元数据 Base VO
 */
@Data
public class MetaTableBaseVO {

    @Schema(description = "关联 meta_database.id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据库ID不能为空")
    private Long dbId;

    @Schema(description = "表名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "user_info")
    @NotEmpty(message = "表名称不能为空")
    private String tableName;

    @Schema(description = "表注释/业务描述", example = "用户中心的用户信息表")
    private String tableComment;

    @Schema(description = "表行数", example = "10000")
    private Long tableRows;

    @Schema(description = "表数据大小（字节）", example = "2048000")
    private Long dataLength;

    @Schema(description = "存储引擎", example = "InnoDB")
    private String engine;

    @Schema(description = "字符集", example = "utf8mb4")
    private String charset;
}
