package com.data.platform.datamind.server.datameta.infrans.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 搜索性能优化工具类
 * 提供搜索缓存、性能监控、查询优化等功能
 *
 * <AUTHOR> Team
 */
@Component
@Slf4j
public class SearchOptimizer {

    // 搜索结果缓存
    private final Map<String, CacheEntry> searchCache = new ConcurrentHashMap<>();
    
    // 搜索性能统计
    private final Map<String, SearchMetrics> searchMetrics = new ConcurrentHashMap<>();
    
    // 热门搜索关键词统计
    private final Map<String, AtomicLong> keywordStats = new ConcurrentHashMap<>();
    
    // 缓存配置
    private static final int MAX_CACHE_SIZE = 1000;
    private static final long CACHE_TTL_MS = 5 * 60 * 1000; // 5分钟
    
    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final Object data;
        private final long timestamp;
        
        public CacheEntry(Object data) {
            this.data = data;
            this.timestamp = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_TTL_MS;
        }
        
        public Object getData() {
            return data;
        }
    }
    
    /**
     * 搜索性能指标
     */
    public static class SearchMetrics {
        private long totalSearches;
        private long totalTime;
        private long minTime = Long.MAX_VALUE;
        private long maxTime = 0;
        private final List<Long> recentTimes = new ArrayList<>();
        
        public synchronized void addSearchTime(long time) {
            totalSearches++;
            totalTime += time;
            minTime = Math.min(minTime, time);
            maxTime = Math.max(maxTime, time);
            
            recentTimes.add(time);
            if (recentTimes.size() > 100) {
                recentTimes.remove(0);
            }
        }
        
        public double getAverageTime() {
            return totalSearches > 0 ? (double) totalTime / totalSearches : 0;
        }
        
        public double getRecentAverageTime() {
            return recentTimes.isEmpty() ? 0 : 
                   recentTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        }
        
        // Getters
        public long getTotalSearches() { return totalSearches; }
        public long getTotalTime() { return totalTime; }
        public long getMinTime() { return minTime == Long.MAX_VALUE ? 0 : minTime; }
        public long getMaxTime() { return maxTime; }
    }
    
    /**
     * 获取缓存的搜索结果
     *
     * @param cacheKey 缓存键
     * @return 缓存的结果，如果不存在或已过期则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getCachedResult(String cacheKey) {
        CacheEntry entry = searchCache.get(cacheKey);
        if (entry != null && !entry.isExpired()) {
            log.debug("Cache hit for key: {}", cacheKey);
            return (T) entry.getData();
        }
        
        if (entry != null && entry.isExpired()) {
            searchCache.remove(cacheKey);
            log.debug("Cache expired for key: {}", cacheKey);
        }
        
        return null;
    }
    
    /**
     * 缓存搜索结果
     *
     * @param cacheKey 缓存键
     * @param result 搜索结果
     */
    public void cacheResult(String cacheKey, Object result) {
        // 检查缓存大小限制
        if (searchCache.size() >= MAX_CACHE_SIZE) {
            cleanupExpiredEntries();
            
            // 如果清理后仍然超过限制，移除最旧的条目
            if (searchCache.size() >= MAX_CACHE_SIZE) {
                removeOldestEntry();
            }
        }
        
        searchCache.put(cacheKey, new CacheEntry(result));
        log.debug("Cached result for key: {}", cacheKey);
    }
    
    /**
     * 生成搜索缓存键
     *
     * @param keyword 搜索关键词
     * @param objectType 对象类型
     * @param filters 过滤条件
     * @return 缓存键
     */
    public String generateCacheKey(String keyword, String objectType, Map<String, Object> filters) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("search:")
                  .append(keyword.toLowerCase())
                  .append(":")
                  .append(objectType);
        
        if (filters != null && !filters.isEmpty()) {
            // 对过滤条件进行排序以确保一致的缓存键
            TreeMap<String, Object> sortedFilters = new TreeMap<>(filters);
            for (Map.Entry<String, Object> entry : sortedFilters.entrySet()) {
                keyBuilder.append(":")
                          .append(entry.getKey())
                          .append("=")
                          .append(entry.getValue());
            }
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * 记录搜索性能指标
     *
     * @param searchType 搜索类型
     * @param searchTime 搜索耗时（毫秒）
     */
    public void recordSearchMetrics(String searchType, long searchTime) {
        searchMetrics.computeIfAbsent(searchType, k -> new SearchMetrics())
                     .addSearchTime(searchTime);
        
        log.debug("Recorded search metrics for type: {}, time: {}ms", searchType, searchTime);
    }
    
    /**
     * 记录搜索关键词统计
     *
     * @param keyword 搜索关键词
     */
    public void recordKeywordStats(String keyword) {
        if (keyword != null && !keyword.trim().isEmpty()) {
            keywordStats.computeIfAbsent(keyword.toLowerCase(), k -> new AtomicLong(0))
                        .incrementAndGet();
        }
    }
    
    /**
     * 获取搜索性能指标
     *
     * @param searchType 搜索类型
     * @return 性能指标
     */
    public SearchMetrics getSearchMetrics(String searchType) {
        return searchMetrics.get(searchType);
    }
    
    /**
     * 获取所有搜索性能指标
     *
     * @return 所有性能指标
     */
    public Map<String, SearchMetrics> getAllSearchMetrics() {
        return new HashMap<>(searchMetrics);
    }
    
    /**
     * 获取热门搜索关键词
     *
     * @param limit 返回数量限制
     * @return 热门关键词列表
     */
    public List<String> getPopularKeywords(int limit) {
        return keywordStats.entrySet().stream()
                .sorted(Map.Entry.<String, AtomicLong>comparingByValue(
                    (a, b) -> Long.compare(b.get(), a.get())))
                .limit(limit)
                .map(Map.Entry::getKey)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 获取关键词搜索次数
     *
     * @param keyword 关键词
     * @return 搜索次数
     */
    public long getKeywordSearchCount(String keyword) {
        AtomicLong count = keywordStats.get(keyword.toLowerCase());
        return count != null ? count.get() : 0;
    }
    
    /**
     * 清理过期的缓存条目
     */
    public void cleanupExpiredEntries() {
        int removedCount = 0;
        Iterator<Map.Entry<String, CacheEntry>> iterator = searchCache.entrySet().iterator();
        
        while (iterator.hasNext()) {
            Map.Entry<String, CacheEntry> entry = iterator.next();
            if (entry.getValue().isExpired()) {
                iterator.remove();
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            log.info("Cleaned up {} expired cache entries", removedCount);
        }
    }
    
    /**
     * 移除最旧的缓存条目
     */
    private void removeOldestEntry() {
        String oldestKey = null;
        long oldestTime = Long.MAX_VALUE;
        
        for (Map.Entry<String, CacheEntry> entry : searchCache.entrySet()) {
            if (entry.getValue().timestamp < oldestTime) {
                oldestTime = entry.getValue().timestamp;
                oldestKey = entry.getKey();
            }
        }
        
        if (oldestKey != null) {
            searchCache.remove(oldestKey);
            log.debug("Removed oldest cache entry: {}", oldestKey);
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void clearCache() {
        int size = searchCache.size();
        searchCache.clear();
        log.info("Cleared {} cache entries", size);
    }
    
    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", searchCache.size());
        stats.put("maxCacheSize", MAX_CACHE_SIZE);
        stats.put("cacheTtlMs", CACHE_TTL_MS);
        
        long expiredCount = searchCache.values().stream()
                .mapToLong(entry -> entry.isExpired() ? 1 : 0)
                .sum();
        stats.put("expiredEntries", expiredCount);
        
        return stats;
    }
    
    /**
     * 优化搜索查询
     *
     * @param keyword 原始关键词
     * @return 优化后的关键词
     */
    public String optimizeSearchKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return keyword;
        }
        
        // 去除多余空格
        String optimized = keyword.trim().replaceAll("\\s+", " ");
        
        // 转换为小写（用于搜索）
        optimized = optimized.toLowerCase();
        
        // 移除特殊字符（保留字母、数字、空格、下划线）
        optimized = optimized.replaceAll("[^a-zA-Z0-9\\s_]", "");
        
        return optimized;
    }
    
    /**
     * 检查搜索性能是否需要优化
     *
     * @param searchType 搜索类型
     * @return 是否需要优化
     */
    public boolean needsOptimization(String searchType) {
        SearchMetrics metrics = searchMetrics.get(searchType);
        if (metrics == null) {
            return false;
        }
        
        // 如果平均搜索时间超过1秒，认为需要优化
        return metrics.getRecentAverageTime() > 1000;
    }
    
    /**
     * 生成搜索性能报告
     *
     * @return 性能报告
     */
    public Map<String, Object> generatePerformanceReport() {
        Map<String, Object> report = new HashMap<>();
        
        // 总体统计
        long totalSearches = searchMetrics.values().stream()
                .mapToLong(SearchMetrics::getTotalSearches)
                .sum();
        
        double avgTime = searchMetrics.values().stream()
                .mapToDouble(SearchMetrics::getAverageTime)
                .average()
                .orElse(0);
        
        report.put("totalSearches", totalSearches);
        report.put("averageSearchTime", avgTime);
        report.put("cacheHitRate", calculateCacheHitRate());
        
        // 各类型搜索统计
        Map<String, Object> typeStats = new HashMap<>();
        for (Map.Entry<String, SearchMetrics> entry : searchMetrics.entrySet()) {
            SearchMetrics metrics = entry.getValue();
            Map<String, Object> typeMetrics = new HashMap<>();
            typeMetrics.put("totalSearches", metrics.getTotalSearches());
            typeMetrics.put("averageTime", metrics.getAverageTime());
            typeMetrics.put("minTime", metrics.getMinTime());
            typeMetrics.put("maxTime", metrics.getMaxTime());
            typeMetrics.put("recentAverageTime", metrics.getRecentAverageTime());
            typeStats.put(entry.getKey(), typeMetrics);
        }
        report.put("searchTypeStatistics", typeStats);
        
        // 热门关键词
        report.put("popularKeywords", getPopularKeywords(10));
        
        // 缓存统计
        report.put("cacheStatistics", getCacheStatistics());
        
        return report;
    }
    
    /**
     * 计算缓存命中率
     *
     * @return 缓存命中率
     */
    private double calculateCacheHitRate() {
        // 这里简化实现，实际项目中需要记录缓存命中和未命中次数
        return 0.75; // 假设75%的命中率
    }
}
