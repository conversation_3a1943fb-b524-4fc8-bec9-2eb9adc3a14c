package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.vo.search.MetaSearchReqVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaSearchRespVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaSearchResultVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaAdvancedSearchReqVO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 元数据搜索服务接口
 * 提供全文搜索、多维度查询、结果排序等功能
 *
 * <AUTHOR> Team
 */
public interface MetaSearchService {

    /**
     * 全文搜索元数据
     *
     * @param reqVO 搜索请求参数
     * @return 搜索结果
     */
    PageResult<MetaSearchResultVO> fullTextSearch(@Valid MetaSearchReqVO reqVO);

    /**
     * 高级搜索元数据
     *
     * @param reqVO 高级搜索请求参数
     * @return 搜索结果
     */
    PageResult<MetaSearchResultVO> advancedSearch(@Valid MetaAdvancedSearchReqVO reqVO);

    /**
     * 搜索数据库
     *
     * @param keyword 搜索关键词
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 数据库搜索结果
     */
    List<MetaSearchResultVO> searchDatabases(String keyword, String sortBy, String sortOrder);

    /**
     * 搜索表
     *
     * @param keyword 搜索关键词
     * @param databaseId 数据库ID（可选）
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 表搜索结果
     */
    List<MetaSearchResultVO> searchTables(String keyword, Long databaseId, String sortBy, String sortOrder);

    /**
     * 搜索列
     *
     * @param keyword 搜索关键词
     * @param tableId 表ID（可选）
     * @param databaseId 数据库ID（可选）
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 列搜索结果
     */
    List<MetaSearchResultVO> searchColumns(String keyword, Long tableId, Long databaseId, String sortBy, String sortOrder);

    /**
     * 按标签搜索
     *
     * @param tags 标签列表
     * @param objectType 对象类型（DATABASE/TABLE/COLUMN）
     * @return 搜索结果
     */
    List<MetaSearchResultVO> searchByTags(List<String> tags, String objectType);

    /**
     * 按数据类型搜索列
     *
     * @param dataTypes 数据类型列表
     * @param databaseId 数据库ID（可选）
     * @return 列搜索结果
     */
    List<MetaSearchResultVO> searchColumnsByDataType(List<String> dataTypes, Long databaseId);

    /**
     * 模糊搜索
     *
     * @param keyword 搜索关键词
     * @param objectTypes 对象类型列表
     * @param fuzzyLevel 模糊匹配级别（1-5）
     * @return 搜索结果
     */
    List<MetaSearchResultVO> fuzzySearch(String keyword, List<String> objectTypes, Integer fuzzyLevel);

    /**
     * 搜索建议/自动补全
     *
     * @param prefix 输入前缀
     * @param objectType 对象类型
     * @param limit 建议数量限制
     * @return 搜索建议列表
     */
    List<String> getSearchSuggestions(String prefix, String objectType, Integer limit);

    /**
     * 获取热门搜索关键词
     *
     * @param objectType 对象类型（可选）
     * @param limit 数量限制
     * @return 热门关键词列表
     */
    List<String> getPopularSearchKeywords(String objectType, Integer limit);

    /**
     * 搜索历史记录
     *
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 搜索历史列表
     */
    List<String> getSearchHistory(Long userId, Integer limit);

    /**
     * 保存搜索历史
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param objectType 对象类型
     */
    void saveSearchHistory(Long userId, String keyword, String objectType);

    /**
     * 清除搜索历史
     *
     * @param userId 用户ID
     */
    void clearSearchHistory(Long userId);

    /**
     * 获取搜索统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 搜索统计信息
     */
    Map<String, Object> getSearchStatistics(Long startTime, Long endTime);

    /**
     * 重建搜索索引
     *
     * @param objectType 对象类型（可选，为空则重建所有）
     * @return 重建结果
     */
    Map<String, Object> rebuildSearchIndex(String objectType);

    /**
     * 优化搜索性能
     *
     * @return 优化结果
     */
    Map<String, Object> optimizeSearchPerformance();

    /**
     * 验证搜索索引完整性
     *
     * @return 验证结果
     */
    Map<String, Object> validateSearchIndex();

    /**
     * 导出搜索结果
     *
     * @param reqVO 搜索请求参数
     * @param format 导出格式（CSV/EXCEL/JSON）
     * @return 导出数据
     */
    String exportSearchResults(@Valid MetaSearchReqVO reqVO, String format);

    /**
     * 批量搜索
     *
     * @param keywords 关键词列表
     * @param objectType 对象类型
     * @return 批量搜索结果
     */
    Map<String, List<MetaSearchResultVO>> batchSearch(List<String> keywords, String objectType);

    /**
     * 相似性搜索
     *
     * @param referenceObjectId 参考对象ID
     * @param objectType 对象类型
     * @param similarityThreshold 相似度阈值
     * @return 相似对象列表
     */
    List<MetaSearchResultVO> similaritySearch(Long referenceObjectId, String objectType, Double similarityThreshold);

    /**
     * 搜索过滤器配置
     *
     * @param objectType 对象类型
     * @return 可用的过滤器配置
     */
    Map<String, Object> getSearchFilters(String objectType);

    /**
     * 应用搜索过滤器
     *
     * @param baseResults 基础搜索结果
     * @param filters 过滤器配置
     * @return 过滤后的结果
     */
    List<MetaSearchResultVO> applySearchFilters(List<MetaSearchResultVO> baseResults, Map<String, Object> filters);
}
