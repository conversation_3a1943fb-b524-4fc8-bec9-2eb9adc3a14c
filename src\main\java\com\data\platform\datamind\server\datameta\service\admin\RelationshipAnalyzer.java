package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipRespVO;

import java.util.List;
import java.util.Map;

/**
 * 表关系分析器接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 表关系分析器，提供表间关系分析功能
 * @email <EMAIL>
 * @since 1.8
 */
public interface RelationshipAnalyzer {

    /**
     * 分析表的关系
     * @param tableId 表ID
     * @return 表关系信息
     */
    TableRelationshipRespVO analyzeTableRelationships(Long tableId);

    /**
     * 分析数据库中所有表的关系
     * @param dbId 数据库ID
     * @return 数据库关系图
     */
    Map<String, Object> analyzeDatabaseRelationships(Long dbId);

    /**
     * 获取表的父表（被引用的表）
     * @param tableId 表ID
     * @return 父表列表
     */
    List<MetaTableDO> getParentTables(Long tableId);

    /**
     * 获取表的子表（引用该表的表）
     * @param tableId 表ID
     * @return 子表列表
     */
    List<MetaTableDO> getChildTables(Long tableId);

    /**
     * 获取表的直接关联表
     * @param tableId 表ID
     * @return 关联表列表
     */
    List<MetaTableDO> getRelatedTables(Long tableId);

    /**
     * 分析表的依赖关系
     * @param tableId 表ID
     * @return 依赖关系分析结果
     */
    Map<String, Object> analyzeDependencies(Long tableId);

    /**
     * 检测循环依赖
     * @param dbId 数据库ID
     * @return 循环依赖检测结果
     */
    List<List<String>> detectCircularDependencies(Long dbId);

    /**
     * 生成表关系图数据
     * @param dbId 数据库ID
     * @return 关系图数据（用于前端可视化）
     */
    Map<String, Object> generateRelationshipGraph(Long dbId);

    /**
     * 分析表的影响范围
     * @param tableId 表ID
     * @return 影响范围分析结果
     */
    Map<String, Object> analyzeImpactScope(Long tableId);

    /**
     * 获取表的关系路径
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 关系路径
     */
    List<String> getRelationshipPath(Long sourceTableId, Long targetTableId);

    /**
     * 分析表的关系强度
     * @param tableId 表ID
     * @return 关系强度分析结果
     */
    Map<String, Object> analyzeRelationshipStrength(Long tableId);

    /**
     * 建议表关系优化
     * @param dbId 数据库ID
     * @return 优化建议
     */
    List<String> suggestRelationshipOptimization(Long dbId);

    /**
     * 验证表关系一致性
     * @param dbId 数据库ID
     * @return 验证结果
     */
    Map<String, Object> validateRelationshipConsistency(Long dbId);

    /**
     * 获取孤立表（没有任何关系的表）
     * @param dbId 数据库ID
     * @return 孤立表列表
     */
    List<MetaTableDO> getIsolatedTables(Long dbId);

    /**
     * 分析表的关系复杂度
     * @param tableId 表ID
     * @return 复杂度分析结果
     */
    Map<String, Object> analyzeRelationshipComplexity(Long tableId);
}
