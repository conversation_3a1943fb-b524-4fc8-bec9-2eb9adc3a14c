package com.data.platform.datamind.server.datameta.vo.lineage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 血缘节点 VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "血缘节点信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LineageNodeVO {

    @Schema(description = "节点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "节点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "TABLE")
    private String nodeType;

    @Schema(description = "节点名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "user_info")
    private String nodeName;

    @Schema(description = "节点描述", example = "用户信息表")
    private String nodeDescription;

    @Schema(description = "数据库ID", example = "1")
    private Long databaseId;

    @Schema(description = "数据库名称", example = "user_center")
    private String databaseName;

    @Schema(description = "表ID（当节点类型为COLUMN时）", example = "10")
    private Long tableId;

    @Schema(description = "表名称（当节点类型为COLUMN时）", example = "user_info")
    private String tableName;

    @Schema(description = "血缘层级", example = "1")
    private Integer lineageLevel;

    @Schema(description = "血缘方向", example = "UPSTREAM")
    private String lineageDirection;

    @Schema(description = "关系类型", example = "FOREIGN_KEY")
    private String relationshipType;
}
