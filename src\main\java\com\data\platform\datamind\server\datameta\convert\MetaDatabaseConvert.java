package com.data.platform.datamind.server.datameta.convert;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:37
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datainspection.convert
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import java.util.List;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseRespVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 数据库/数据源元数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MetaDatabaseConvert {

    MetaDatabaseConvert INSTANCE = Mappers.getMapper(MetaDatabaseConvert.class);

    MetaDatabaseDO convert(MetaDatabaseCreateReqVO bean);

    MetaDatabaseDO convert(MetaDatabaseUpdateReqVO bean);

    MetaDatabaseRespVO convert(MetaDatabaseDO bean);

    List<MetaDatabaseRespVO> convertList(List<MetaDatabaseDO> list);

    PageResult<MetaDatabaseRespVO> convertPage(PageResult<MetaDatabaseDO> page);

}
