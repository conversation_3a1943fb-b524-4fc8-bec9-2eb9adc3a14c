package com.data.platform.datamind.server.datameta.vo.index;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 索引元数据 Response VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.vo.index
 * @description 索引元数据响应VO
 * @email <EMAIL>
 * @since 1.8
 */
@Schema(description = "管理后台 - 索引元数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaIndexRespVO extends MetaIndexBaseVO {

    @Schema(description = "索引ID", example = "1")
    private Long id;

    @Schema(description = "实例ID", example = "mysql-001")
    private String instanceId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "更新者", example = "admin")
    private String updater;
}
