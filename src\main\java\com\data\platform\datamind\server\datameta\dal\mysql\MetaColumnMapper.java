package com.data.platform.datamind.server.datameta.dal.mysql;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:05
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datainspection.dal.mysql
 * @description
 * @email <EMAIL>
 * @since 1.8
 */

import com.data.platform.datamind.framework.mybatis.core.mapper.BaseMapperX;
import com.data.platform.datamind.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 列元数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MetaColumnMapper extends BaseMapperX<MetaColumnDO> {

//    default PageResult<MetaColumnDO> selectPage(MetaColumnPageReqVO reqVO) {
//        return this.selectPage(reqVO, new LambdaQueryWrapperX<MetaColumnDO>()
//                .eqIfPresent(MetaColumnDO::getTableId, reqVO.getTableId())
//                .likeIfPresent(MetaColumnDO::getColumnName, reqVO.getColumnName())
//                .likeIfPresent(MetaColumnDO::getColumnComment, reqVO.getColumnComment())
//                .orderByDesc(MetaColumnDO::getId));
//    }

    default MetaColumnDO selectByTableIdAndColumnName(@Param("tableId") Long tableId, @Param("columnName") String columnName) {
        return selectOne(new LambdaQueryWrapperX<MetaColumnDO>()
                .eq(MetaColumnDO::getTableId, tableId)
                .eq(MetaColumnDO::getColumnName, columnName));
    }

    default List<MetaColumnDO> selectListByTableId(Long tableId) {
        return selectList(new LambdaQueryWrapperX<MetaColumnDO>()
                .eq(MetaColumnDO::getTableId, tableId));
    }

    default List<MetaColumnDO> selectListByDbId(Long dbId) {
        // 这是一个复杂的查询，可能需要join表或者在Service层分步实现
        // 简化起见，这里假设直接查询所有列，后续在Service层过滤或通过其他方式实现
        // 实际应用中，如果需要高效查询某个数据库下的所有列，可以优化SQL，例如：
        // SELECT mc.* FROM meta_column mc JOIN meta_table mt ON mc.table_id = mt.id WHERE mt.db_id = #{dbId}
        return selectList(new LambdaQueryWrapperX<MetaColumnDO>()
                .inSql(MetaColumnDO::getTableId, "SELECT id FROM meta_table WHERE db_id = " + dbId));
    }
}
