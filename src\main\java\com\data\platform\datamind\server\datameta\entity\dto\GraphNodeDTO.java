package com.data.platform.datamind.server.datameta.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 图谱节点数据传输对象
 *
 * <AUTHOR> Team
 */
@Schema(description = "图谱节点DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GraphNodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "节点ID", example = "1")
    private Long id;

    @Schema(description = "节点标签", example = "Database")
    private String label;

    @Schema(description = "节点名称", example = "user_center")
    private String name;

    @Schema(description = "节点类型", example = "DATABASE")
    private String type;

    @Schema(description = "节点属性")
    private Map<String, Object> properties;

    @Schema(description = "节点描述", example = "用户中心数据库")
    private String description;

    @Schema(description = "节点状态", example = "ACTIVE")
    private String status;

    @Schema(description = "创建时间戳", example = "1625097600000")
    private Long createTime;

    @Schema(description = "更新时间戳", example = "1625097600000")
    private Long updateTime;

    @Schema(description = "扩展属性")
    private Map<String, Object> metadata;
}
