package com.data.platform.datamind.server.datameta.vo.column;

import com.data.platform.datamind.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.data.platform.datamind.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 列元数据分页 Request VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:50
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo.column
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
@Schema(description = "管理后台 - 列元数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaColumnPageReqVO extends PageParam {

    @Schema(description = "表ID", example = "101")
    private Long tableId;

    @Schema(description = "列名称", example = "user_id")
    private String columnName;

    @Schema(description = "列数据类型", example = "VARCHAR")
    private String columnType;

    @Schema(description = "列注释", example = "用户ID")
    private String columnComment;

    @Schema(description = "键类型", example = "PRI")
    private String columnKey;

    @Schema(description = "是否允许为空", example = "false")
    private Boolean isNullable;

    @Schema(description = "是否主键", example = "true")
    private Boolean isPrimaryKey;

    @Schema(description = "是否外键", example = "false")
    private Boolean isForeignKey;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
