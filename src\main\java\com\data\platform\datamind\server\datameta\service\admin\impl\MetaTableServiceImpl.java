package com.data.platform.datamind.server.datameta.service.admin.impl;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:13
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin.impl
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaTableConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.dal.mysql.MetaTableMapper;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTablePageReqVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableUpdateReqVO;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static com.data.platform.datamind.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 表元数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MetaTableServiceImpl implements MetaTableService {

    @Resource
    private MetaTableMapper metaTableMapper;

    @Override
    public Long createTable(MetaTableCreateReqVO createReqVO) {
        // 校验名称在同一数据库下唯一性
//        validateTableNameUnique(null, createReqVO.getDbId(), createReqVO.getTableName());
        // VO -> DO
        MetaTableDO table = MetaTableConvert.INSTANCE.convert(createReqVO);
        metaTableMapper.insert(table);
        return table.getId();
    }

    @Override
    public void updateTable(MetaTableUpdateReqVO updateReqVO) {
        // 校验存在
//        ValidationUtils.verifyNotNull(metaTableMapper.selectById(updateReqVO.getId()), METATABLE_NOT_EXISTS);
        // 校验名称在同一数据库下唯一性
//        validateTableNameUnique(updateReqVO.getId(), updateReqVO.getDbId(), updateReqVO.getTableName());

        // VO -> DO
        MetaTableDO table = MetaTableConvert.INSTANCE.convert(updateReqVO);
        metaTableMapper.updateById(table);
    }

    @Override
    public void deleteTable(Long id) {
        // 校验存在
//        ValidationUtils.verifyNotNull(metaTableMapper.selectById(id), METATABLE_NOT_EXISTS);
        metaTableMapper.deleteById(id);
    }

    @Override
    public MetaTableDO getTable(Long id) {
        return metaTableMapper.selectById(id);
    }

    @Override
    public List<MetaTableDO> getTableList(Collection<Long> ids) {
        return metaTableMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MetaTableDO> getTablePage(MetaTablePageReqVO pageReqVO) {
        return metaTableMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MetaTableDO> getTablesByDbId(Long dbId) {
        return metaTableMapper.selectListByDbId(dbId);
    }

    @Override
    public MetaTableDO getTableByDbIdAndTableName(Long dbId, String tableName) {
        return metaTableMapper.selectByDbIdAndTableName(dbId, tableName);
    }

    @Override
    @Transactional
    public void batchInsert(List<MetaTableDO> tables) {
        if (tables.isEmpty()) {
            return;
        }
        // 逐个插入，确保兼容性
        for (MetaTableDO table : tables) {
            metaTableMapper.insert(table);
        }
    }

    @Override
    @Transactional
    public void batchUpdate(List<MetaTableDO> tables) {
        if (tables.isEmpty()) {
            return;
        }
        // 逐个更新，因为MyBatis-Plus的updateBatchById可能不支持所有数据库
        for (MetaTableDO table : tables) {
            metaTableMapper.updateById(table);
        }
    }

    @Override
    public void deleteTablesByDbIdAndTableNames(Long dbId, List<String> tableNames) {
        if (tableNames.isEmpty()) {
            return;
        }
        metaTableMapper.delete(new LambdaQueryWrapper<MetaTableDO>()
                .eq(MetaTableDO::getDbId, dbId)
                .in(MetaTableDO::getTableName, tableNames));
    }

    @Override
    public List<MetaTableDO> getAllTables() {
        return metaTableMapper.selectList();
    }

    private void validateTableNameUnique(Long id, Long dbId, String tableName) {
        MetaTableDO table = metaTableMapper.selectByDbIdAndTableName(dbId, tableName);
        if (table == null) {
            return;
        }
        // 如果 id 为空，说明是新增
//        if (id == null) {
//            throw exception(METATABLE_NAME_DUPLICATE);
//        }
//        // 如果 id 不为空，说明是修改，且名字重复的不是自己
//        if (!table.getId().equals(id)) {
//            throw exception(METATABLE_NAME_DUPLICATE);
//        }
    }
}
