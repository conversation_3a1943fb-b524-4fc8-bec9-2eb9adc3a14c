# DataMind Data-Meta 数据元数据管理服务指南

> 专业的数据库元数据管理服务，支持多种数据库的元数据采集、管理和图谱构建

## 服务概述

### 功能定位
Data-Meta服务是DataMind Cloud平台的数据元数据管理核心，负责：
- **数据库连接管理**: 支持MySQL、Oracle、PostgreSQL等多种数据库
- **元数据采集**: 自动采集表结构、字段信息、索引、约束等
- **元数据管理**: 提供完整的CRUD操作和查询功能
- **图谱构建**: 将元数据同步到Neo4j构建知识图谱
- **血缘分析**: 分析表间关系和数据血缘

### 技术架构
- **框架**: Spring Boot 2.7.18 + MyBatis Plus
- **数据库**: MySQL (主存储) + Neo4j (图数据库)
- **连接池**: HikariCP + Dynamic DataSource
- **AI集成**: Spring AI + 向量存储
- **API文档**: Swagger + Knife4j

## 快速开始

### 环境要求
- **Java**: JDK 8+
- **MySQL**: 5.7+ (存储元数据)
- **Neo4j**: 4.0+ (可选，用于图谱)
- **Redis**: 6.0+ (缓存)

### 本地启动
```bash
# 1. 进入服务目录
cd datamind-server-data-meta

# 2. 配置数据库
vim src/main/resources/application-local.yaml

# 3. 启动服务
mvn spring-boot:run -Dspring.profiles.active=local

# 4. 访问API文档
open http://localhost:8081/doc.html
```

### 核心配置
```yaml
# 数据库配置
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ************************************
          username: root
          password: your_password

# Neo4j配置 (可选)
  data:
    neo4j:
      uri: bolt://localhost:7687
      username: neo4j
      password: your_neo4j_password

# 向量存储配置
  ai:
    vectorstore:
      redis:
        initialize-schema: true
        index: metadata_index
        prefix: "metadata_segment:"
```

## 核心功能模块

### 1. 数据库管理
- **数据库注册**: 添加、编辑、删除数据库连接信息
- **连接测试**: 验证数据库连接的有效性
- **权限管理**: 控制数据库访问权限
- **状态监控**: 监控数据库连接状态

### 2. 元数据采集
- **自动采集**: 定时或手动触发元数据采集
- **增量更新**: 支持增量采集，避免重复处理
- **多数据库支持**: MySQL、Oracle、PostgreSQL、SQL Server等
- **采集监控**: 采集进度和结果监控

### 3. 表结构管理
- **表信息管理**: 表名、注释、创建时间等基本信息
- **字段管理**: 字段类型、长度、约束、注释等详细信息
- **索引管理**: 主键、外键、普通索引信息
- **关系分析**: 表间关系和依赖分析

### 4. 图谱构建
- **Neo4j同步**: 将元数据同步到Neo4j图数据库
- **关系建模**: 构建表、字段、数据库间的关系图谱
- **血缘分析**: 基于图谱进行数据血缘分析
- **可视化支持**: 提供图谱可视化查询接口

## API接口

### 管理端API (Admin)

#### 数据库管理
```bash
# 获取数据库列表
GET /admin-api/data-meta/database/page?pageNo=1&pageSize=10

# 创建数据库连接
POST /admin-api/data-meta/database/create
{
  "name": "测试数据库",
  "host": "localhost",
  "port": 3306,
  "database": "test_db",
  "username": "root",
  "password": "password",
  "databaseType": "MySQL",
  "description": "测试环境数据库"
}

# 测试数据库连接
POST /admin-api/data-meta/database/test-connection
{
  "host": "localhost",
  "port": 3306,
  "database": "test_db",
  "username": "root",
  "password": "password"
}

# 采集数据库元数据
POST /admin-api/data-meta/database/collect
{
  "host": "localhost",
  "port": 3306,
  "database": "test_db",
  "username": "root",
  "password": "password",
  "syncToGraph": true
}
```

#### 表管理
```bash
# 获取表列表
GET /admin-api/data-meta/table/page?dbId=1

# 获取表详情
GET /admin-api/data-meta/table/get?id=1

# 更新表信息
PUT /admin-api/data-meta/table/update
{
  "id": 1,
  "tableComment": "更新后的表注释",
  "description": "详细描述"
}
```

#### 字段管理
```bash
# 获取字段列表
GET /admin-api/data-meta/column/list?tableId=1

# 更新字段信息
PUT /admin-api/data-meta/column/update
{
  "id": 1,
  "columnComment": "更新后的字段注释",
  "businessName": "业务字段名"
}
```

### Web端API

#### 元数据查询
```bash
# 同步所有数据库元数据
GET /web-api/data-meta/meta/syncAllDbMeta?dbType=MySQL

# 获取数据库列表
GET /web-api/data-meta/meta/databases

# 获取表列表
GET /web-api/data-meta/meta/tables?databaseId=1

# 获取字段列表
GET /web-api/data-meta/meta/columns?tableId=1

# 搜索元数据
GET /web-api/data-meta/meta/search?keyword=user&type=table
```

#### 增强功能
```bash
# 元数据自动同步
POST /web-api/data-meta/meta/metadata/sync
{
  "databaseIds": [1, 2],
  "syncType": "INCREMENTAL",
  "syncToGraph": true
}

# 获取表血缘关系
GET /web-api/data-meta/meta/lineage/table?tableId=1

# 获取数据质量报告
GET /web-api/data-meta/meta/quality/report?databaseId=1
```

## 开发指南

### 项目结构
```
datamind-server-data-meta/
├── src/main/java/
│   └── com/data/platform/datamind/server/datameta/
│       ├── controller/
│       │   ├── admin/          # 管理端控制器
│       │   └── web/            # Web端控制器
│       ├── service/
│       │   ├── admin/          # 管理端服务
│       │   └── web/            # Web端服务
│       ├── dal/
│       │   ├── dataobject/     # 数据对象
│       │   └── mysql/          # MySQL映射器
│       └── convert/            # 对象转换器
├── src/main/resources/
│   ├── application.yaml
│   └── mapper/                 # MyBatis映射文件
└── pom.xml
```

### 核心服务类

#### 元数据采集服务
```java
@Service
public class MetaDataCollectServiceImpl implements MetaDataCollectService {
    
    @Override
    public void collectAndSyncMetaData(MetaDataCollectReqVO reqVO) throws SQLException {
        // 1. 建立数据库连接
        Connection connection = createConnection(reqVO);
        
        // 2. 采集表信息
        List<MetaTableDO> tables = collectTables(connection);
        
        // 3. 采集字段信息
        for (MetaTableDO table : tables) {
            List<MetaColumnDO> columns = collectColumns(connection, table);
            table.setColumns(columns);
        }
        
        // 4. 保存到数据库
        saveMetaData(tables);
        
        // 5. 同步到图谱 (可选)
        if (reqVO.getSyncToGraph()) {
            syncToNeo4j(tables);
        }
    }
}
```

#### 图谱同步服务
```java
@Service
public class MetaDataGraphServiceImpl implements MetaDataGraphService {
    
    @Override
    public void syncToNeo4j(List<MetaTableDO> tables) {
        for (MetaTableDO table : tables) {
            // 创建表节点
            createTableNode(table);
            
            // 创建字段节点和关系
            for (MetaColumnDO column : table.getColumns()) {
                createColumnNode(column);
                createTableColumnRelation(table, column);
            }
        }
    }
}
```

### 扩展开发

#### 添加新数据库支持
```java
@Component
public class CustomDatabaseConnector implements DatabaseConnector {
    
    @Override
    public String getDatabaseType() {
        return "CUSTOM_DB";
    }
    
    @Override
    public Connection createConnection(DatabaseConfig config) {
        // 实现自定义数据库连接逻辑
        return DriverManager.getConnection(
            buildConnectionUrl(config),
            config.getUsername(),
            config.getPassword()
        );
    }
    
    @Override
    public List<MetaTableDO> collectTables(Connection connection) {
        // 实现表结构采集逻辑
        return executeQuery(connection, getTableQuery());
    }
}
```

## 配置说明

### 数据库连接配置
```yaml
# 支持的数据库类型
datamind:
  meta:
    supported-databases:
      - MySQL
      - Oracle
      - PostgreSQL
      - SQL Server
      - MariaDB
      - DM (达梦)
      - KingBase (人大金仓)
      - OpenGauss
    
    # 采集配置
    collect:
      batch-size: 1000        # 批量处理大小
      timeout: 30000          # 超时时间(毫秒)
      retry-count: 3          # 重试次数
      parallel: true          # 是否并行采集
    
    # 图谱同步配置
    graph:
      enabled: true           # 是否启用图谱同步
      batch-size: 500         # 批量同步大小
      auto-sync: false        # 是否自动同步
```

### Neo4j图谱配置
```yaml
spring:
  data:
    neo4j:
      uri: bolt://localhost:7687
      username: neo4j
      password: your_password
      database: datamind      # 图数据库名称
```

## 部署和运维

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/datamind-server-data-meta.jar app.jar
EXPOSE 8081
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 监控指标
```bash
# 服务健康检查
curl http://localhost:8081/actuator/health

# 元数据采集统计
curl http://localhost:8081/actuator/metrics/datameta.collect.count

# 数据库连接池状态
curl http://localhost:8081/actuator/metrics/hikaricp.connections.active
```

### 故障排查

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 元数据采集失败 | 数据库连接问题 | 检查数据库配置和网络连通性 |
| Neo4j同步失败 | 图数据库连接问题 | 检查Neo4j服务状态和配置 |
| 内存溢出 | 大表采集内存不足 | 调整JVM参数和批量大小 |
| 采集超时 | 数据库响应慢 | 增加超时时间或优化查询 |
