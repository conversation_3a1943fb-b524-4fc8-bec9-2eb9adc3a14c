package com.data.platform.datamind.server.datameta.service.admin.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import com.data.platform.datamind.server.datameta.service.admin.MetaDatabaseService;
import com.data.platform.datamind.server.datameta.service.admin.MetaSearchService;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.data.platform.datamind.server.datameta.infrans.util.SearchOptimizer;
import com.data.platform.datamind.server.datameta.vo.search.MetaAdvancedSearchReqVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaSearchReqVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaSearchRespVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaSearchResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 元数据搜索服务实现类
 *
 * <AUTHOR> Team
 */
@Service
@Validated
@Slf4j
public class MetaSearchServiceImpl implements MetaSearchService {

    @Resource
    private MetaDatabaseService metaDatabaseService;

    @Resource
    private MetaTableService metaTableService;

    @Resource
    private MetaColumnService metaColumnService;

    @Resource
    private SearchOptimizer searchOptimizer;

    // 搜索历史缓存（实际项目中应使用Redis）
    private final Map<Long, List<String>> searchHistoryCache = new HashMap<>();

    // 热门搜索关键词缓存
    private final Map<String, List<String>> popularKeywordsCache = new HashMap<>();

    @Override
    public PageResult<MetaSearchResultVO> fullTextSearch(MetaSearchReqVO reqVO) {
        long startTime = System.currentTimeMillis();

        // 优化搜索关键词
        String optimizedKeyword = searchOptimizer.optimizeSearchKeyword(reqVO.getKeyword());
        reqVO.setKeyword(optimizedKeyword);

        // 生成缓存键
        Map<String, Object> filters = new HashMap<>();
        filters.put("searchType", reqVO.getSearchType());
        filters.put("databaseIds", reqVO.getDatabaseIds());
        filters.put("tableIds", reqVO.getTableIds());
        filters.put("dataTypes", reqVO.getDataTypes());
        filters.put("pageNo", reqVO.getPageNo());
        filters.put("pageSize", reqVO.getPageSize());

        String cacheKey = searchOptimizer.generateCacheKey(reqVO.getKeyword(), reqVO.getSearchType(), filters);

        // 尝试从缓存获取结果
        PageResult<MetaSearchResultVO> cachedResult = searchOptimizer.getCachedResult(cacheKey);
        if (cachedResult != null) {
            long duration = System.currentTimeMillis() - startTime;
            searchOptimizer.recordSearchMetrics("FULL_TEXT_CACHED", duration);
            searchOptimizer.recordKeywordStats(reqVO.getKeyword());
            return cachedResult;
        }

        List<MetaSearchResultVO> allResults = new ArrayList<>();
        
        // 根据搜索类型进行搜索
        if ("ALL".equals(reqVO.getSearchType()) || "DATABASE".equals(reqVO.getSearchType())) {
            allResults.addAll(searchDatabases(reqVO.getKeyword(), reqVO.getSortBy(), reqVO.getSortOrder()));
        }
        
        if ("ALL".equals(reqVO.getSearchType()) || "TABLE".equals(reqVO.getSearchType())) {
            List<Long> databaseIds = reqVO.getDatabaseIds();
            Long databaseId = (databaseIds != null && !databaseIds.isEmpty()) ? databaseIds.get(0) : null;
            allResults.addAll(searchTables(reqVO.getKeyword(), databaseId, reqVO.getSortBy(), reqVO.getSortOrder()));
        }
        
        if ("ALL".equals(reqVO.getSearchType()) || "COLUMN".equals(reqVO.getSearchType())) {
            List<Long> tableIds = reqVO.getTableIds();
            List<Long> databaseIds = reqVO.getDatabaseIds();
            Long tableId = (tableIds != null && !tableIds.isEmpty()) ? tableIds.get(0) : null;
            Long databaseId = (databaseIds != null && !databaseIds.isEmpty()) ? databaseIds.get(0) : null;
            allResults.addAll(searchColumns(reqVO.getKeyword(), tableId, databaseId, reqVO.getSortBy(), reqVO.getSortOrder()));
        }
        
        // 应用过滤器
        allResults = applyFilters(allResults, reqVO);
        
        // 排序
        allResults = sortResults(allResults, reqVO.getSortBy(), reqVO.getSortOrder());
        
        // 分页
        int start = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
        int end = Math.min(start + reqVO.getPageSize(), allResults.size());
        List<MetaSearchResultVO> pagedResults = allResults.subList(start, end);
        
        // 高亮处理
        if (reqVO.getHighlight()) {
            pagedResults = highlightResults(pagedResults, reqVO.getKeyword());
        }
        
        long duration = System.currentTimeMillis() - startTime;
        log.info("Full text search completed in {}ms, found {} results", duration, allResults.size());

        // 创建分页结果
        PageResult<MetaSearchResultVO> result = new PageResult<>(pagedResults, (long) allResults.size());

        // 缓存结果
        searchOptimizer.cacheResult(cacheKey, result);

        // 记录性能指标
        searchOptimizer.recordSearchMetrics("FULL_TEXT", duration);
        searchOptimizer.recordKeywordStats(reqVO.getKeyword());

        return result;
    }

    @Override
    public PageResult<MetaSearchResultVO> advancedSearch(MetaAdvancedSearchReqVO reqVO) {
        long startTime = System.currentTimeMillis();
        
        // 高级搜索逻辑实现
        List<MetaSearchResultVO> results = new ArrayList<>();
        
        // 处理搜索条件组
        if (reqVO.getConditionGroups() != null && !reqVO.getConditionGroups().isEmpty()) {
            results = processAdvancedSearchConditions(reqVO);
        }
        
        // 应用排序规则
        if (reqVO.getSortRules() != null && !reqVO.getSortRules().isEmpty()) {
            results = applySortRules(results, reqVO.getSortRules());
        }
        
        // 应用结果过滤器
        if (reqVO.getResultFilters() != null && !reqVO.getResultFilters().isEmpty()) {
            results = applySearchFilters(results, reqVO.getResultFilters());
        }
        
        // 分页
        int start = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
        int end = Math.min(start + reqVO.getPageSize(), results.size());
        List<MetaSearchResultVO> pagedResults = results.subList(start, end);
        
        long duration = System.currentTimeMillis() - startTime;
        log.info("Advanced search completed in {}ms, found {} results", duration, results.size());
        
        return new PageResult<>(pagedResults, (long) results.size());
    }

    @Override
    public List<MetaSearchResultVO> searchDatabases(String keyword, String sortBy, String sortOrder) {
        List<MetaDatabaseDO> databases = metaDatabaseService.getDatabaseList();
        
        return databases.stream()
                .filter(db -> matchesKeyword(db.getName(), keyword) || 
                             matchesKeyword(db.getDescription(), keyword))
                .map(this::convertDatabaseToSearchResult)
                .collect(Collectors.toList());
    }

    @Override
    public List<MetaSearchResultVO> searchTables(String keyword, Long databaseId, String sortBy, String sortOrder) {
        List<MetaTableDO> tables;
        
        if (databaseId != null) {
            tables = metaTableService.getTablesByDatabaseId(databaseId);
        } else {
            tables = metaTableService.getTableList();
        }
        
        return tables.stream()
                .filter(table -> matchesKeyword(table.getTableName(), keyword) || 
                                matchesKeyword(table.getTableComment(), keyword))
                .map(this::convertTableToSearchResult)
                .collect(Collectors.toList());
    }

    @Override
    public List<MetaSearchResultVO> searchColumns(String keyword, Long tableId, Long databaseId, String sortBy, String sortOrder) {
        List<MetaColumnDO> columns;
        
        if (tableId != null) {
            columns = metaColumnService.getColumnsByTableId(tableId);
        } else if (databaseId != null) {
            // 获取数据库下所有表的列
            List<MetaTableDO> tables = metaTableService.getTablesByDatabaseId(databaseId);
            columns = new ArrayList<>();
            for (MetaTableDO table : tables) {
                columns.addAll(metaColumnService.getColumnsByTableId(table.getId()));
            }
        } else {
            columns = metaColumnService.getColumnList();
        }
        
        return columns.stream()
                .filter(column -> matchesKeyword(column.getColumnName(), keyword) || 
                                 matchesKeyword(column.getColumnComment(), keyword))
                .map(this::convertColumnToSearchResult)
                .collect(Collectors.toList());
    }

    @Override
    public List<MetaSearchResultVO> searchByTags(List<String> tags, String objectType) {
        // 标签搜索实现（需要扩展数据模型支持标签）
        List<MetaSearchResultVO> results = new ArrayList<>();
        
        // 这里是示例实现，实际需要根据标签字段进行搜索
        log.info("Searching by tags: {}, objectType: {}", tags, objectType);
        
        return results;
    }

    @Override
    public List<MetaSearchResultVO> searchColumnsByDataType(List<String> dataTypes, Long databaseId) {
        List<MetaColumnDO> columns;
        
        if (databaseId != null) {
            List<MetaTableDO> tables = metaTableService.getTablesByDatabaseId(databaseId);
            columns = new ArrayList<>();
            for (MetaTableDO table : tables) {
                columns.addAll(metaColumnService.getColumnsByTableId(table.getId()));
            }
        } else {
            columns = metaColumnService.getColumnList();
        }
        
        return columns.stream()
                .filter(column -> dataTypes.contains(column.getColumnType()))
                .map(this::convertColumnToSearchResult)
                .collect(Collectors.toList());
    }

    @Override
    public List<MetaSearchResultVO> fuzzySearch(String keyword, List<String> objectTypes, Integer fuzzyLevel) {
        List<MetaSearchResultVO> results = new ArrayList<>();
        
        // 模糊搜索实现
        for (String objectType : objectTypes) {
            switch (objectType.toUpperCase()) {
                case "DATABASE":
                    results.addAll(fuzzySearchDatabases(keyword, fuzzyLevel));
                    break;
                case "TABLE":
                    results.addAll(fuzzySearchTables(keyword, fuzzyLevel));
                    break;
                case "COLUMN":
                    results.addAll(fuzzySearchColumns(keyword, fuzzyLevel));
                    break;
            }
        }
        
        return results;
    }

    @Override
    public List<String> getSearchSuggestions(String prefix, String objectType, Integer limit) {
        List<String> suggestions = new ArrayList<>();
        
        switch (objectType.toUpperCase()) {
            case "DATABASE":
                suggestions = metaDatabaseService.getDatabaseList().stream()
                        .map(MetaDatabaseDO::getName)
                        .filter(name -> name.toLowerCase().startsWith(prefix.toLowerCase()))
                        .limit(limit)
                        .collect(Collectors.toList());
                break;
            case "TABLE":
                suggestions = metaTableService.getTableList().stream()
                        .map(MetaTableDO::getTableName)
                        .filter(name -> name.toLowerCase().startsWith(prefix.toLowerCase()))
                        .limit(limit)
                        .collect(Collectors.toList());
                break;
            case "COLUMN":
                suggestions = metaColumnService.getColumnList().stream()
                        .map(MetaColumnDO::getColumnName)
                        .filter(name -> name.toLowerCase().startsWith(prefix.toLowerCase()))
                        .limit(limit)
                        .collect(Collectors.toList());
                break;
        }
        
        return suggestions;
    }

    @Override
    public List<String> getPopularSearchKeywords(String objectType, Integer limit) {
        // 首先尝试从搜索优化器获取真实的热门关键词
        List<String> popularKeywords = searchOptimizer.getPopularKeywords(limit);

        if (popularKeywords.isEmpty()) {
            // 如果没有统计数据，返回默认的热门关键词
            popularKeywords = Arrays.asList(
                    "user", "order", "product", "customer", "payment", "log", "config", "system"
            );
        }

        return popularKeywords.stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public List<String> getSearchHistory(Long userId, Integer limit) {
        return searchHistoryCache.getOrDefault(userId, new ArrayList<>())
                .stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public void saveSearchHistory(Long userId, String keyword, String objectType) {
        searchHistoryCache.computeIfAbsent(userId, k -> new ArrayList<>()).add(keyword);
        
        // 限制历史记录数量
        List<String> history = searchHistoryCache.get(userId);
        if (history.size() > 50) {
            history.remove(0);
        }
    }

    @Override
    public void clearSearchHistory(Long userId) {
        searchHistoryCache.remove(userId);
    }

    @Override
    public Map<String, Object> getSearchStatistics(Long startTime, Long endTime) {
        // 获取详细的搜索统计信息
        Map<String, Object> statistics = searchOptimizer.generatePerformanceReport();

        // 添加时间范围信息
        statistics.put("startTime", startTime);
        statistics.put("endTime", endTime);
        statistics.put("reportGeneratedAt", System.currentTimeMillis());

        return statistics;
    }

    @Override
    public Map<String, Object> rebuildSearchIndex(String objectType) {
        Map<String, Object> result = new HashMap<>();
        
        long startTime = System.currentTimeMillis();
        
        // 重建搜索索引的逻辑
        log.info("Rebuilding search index for objectType: {}", objectType);
        
        long duration = System.currentTimeMillis() - startTime;
        
        result.put("success", true);
        result.put("duration", duration);
        result.put("objectType", objectType);
        result.put("rebuildTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> optimizeSearchPerformance() {
        Map<String, Object> result = new HashMap<>();

        // 搜索性能优化逻辑
        log.info("Optimizing search performance");

        // 清理过期缓存
        searchOptimizer.cleanupExpiredEntries();

        // 获取性能报告
        Map<String, Object> performanceReport = searchOptimizer.generatePerformanceReport();

        List<String> optimizations = new ArrayList<>();
        optimizations.add("Cache cleanup completed");
        optimizations.add("Performance metrics analyzed");

        // 检查是否需要进一步优化
        Map<String, Object> typeStats = (Map<String, Object>) performanceReport.get("searchTypeStatistics");
        if (typeStats != null) {
            for (String searchType : typeStats.keySet()) {
                if (searchOptimizer.needsOptimization(searchType)) {
                    optimizations.add("Search type " + searchType + " needs optimization");
                }
            }
        }

        result.put("success", true);
        result.put("optimizations", optimizations);
        result.put("performanceReport", performanceReport);
        result.put("optimizeTime", LocalDateTime.now());

        return result;
    }

    @Override
    public Map<String, Object> validateSearchIndex() {
        Map<String, Object> result = new HashMap<>();
        
        // 搜索索引验证逻辑
        result.put("valid", true);
        result.put("indexCount", 1000);
        result.put("lastValidation", LocalDateTime.now());
        result.put("issues", new ArrayList<>());
        
        return result;
    }

    @Override
    public String exportSearchResults(MetaSearchReqVO reqVO, String format) {
        PageResult<MetaSearchResultVO> searchResult = fullTextSearch(reqVO);
        
        // 导出逻辑实现
        switch (format.toUpperCase()) {
            case "CSV":
                return exportToCsv(searchResult.getList());
            case "EXCEL":
                return exportToExcel(searchResult.getList());
            case "JSON":
                return exportToJson(searchResult.getList());
            default:
                throw new IllegalArgumentException("Unsupported export format: " + format);
        }
    }

    @Override
    public Map<String, List<MetaSearchResultVO>> batchSearch(List<String> keywords, String objectType) {
        Map<String, List<MetaSearchResultVO>> results = new HashMap<>();
        
        for (String keyword : keywords) {
            switch (objectType.toUpperCase()) {
                case "DATABASE":
                    results.put(keyword, searchDatabases(keyword, "relevance", "DESC"));
                    break;
                case "TABLE":
                    results.put(keyword, searchTables(keyword, null, "relevance", "DESC"));
                    break;
                case "COLUMN":
                    results.put(keyword, searchColumns(keyword, null, null, "relevance", "DESC"));
                    break;
                default:
                    List<MetaSearchResultVO> allResults = new ArrayList<>();
                    allResults.addAll(searchDatabases(keyword, "relevance", "DESC"));
                    allResults.addAll(searchTables(keyword, null, "relevance", "DESC"));
                    allResults.addAll(searchColumns(keyword, null, null, "relevance", "DESC"));
                    results.put(keyword, allResults);
                    break;
            }
        }
        
        return results;
    }

    @Override
    public List<MetaSearchResultVO> similaritySearch(Long referenceObjectId, String objectType, Double similarityThreshold) {
        // 相似性搜索实现
        List<MetaSearchResultVO> results = new ArrayList<>();
        
        log.info("Similarity search for objectId: {}, type: {}, threshold: {}", 
                referenceObjectId, objectType, similarityThreshold);
        
        return results;
    }

    @Override
    public Map<String, Object> getSearchFilters(String objectType) {
        Map<String, Object> filters = new HashMap<>();
        
        switch (objectType.toUpperCase()) {
            case "DATABASE":
                filters.put("type", Arrays.asList("MySQL", "PostgreSQL", "Oracle", "SQL Server"));
                filters.put("status", Arrays.asList("ACTIVE", "INACTIVE"));
                break;
            case "TABLE":
                filters.put("engine", Arrays.asList("InnoDB", "MyISAM"));
                filters.put("hasData", Arrays.asList("true", "false"));
                break;
            case "COLUMN":
                filters.put("dataType", Arrays.asList("VARCHAR", "INT", "BIGINT", "DECIMAL", "DATETIME"));
                filters.put("nullable", Arrays.asList("true", "false"));
                filters.put("primaryKey", Arrays.asList("true", "false"));
                break;
        }
        
        return filters;
    }

    @Override
    public List<MetaSearchResultVO> applySearchFilters(List<MetaSearchResultVO> baseResults, Map<String, Object> filters) {
        return baseResults.stream()
                .filter(result -> matchesFilters(result, filters))
                .collect(Collectors.toList());
    }

    // ==================== 私有辅助方法 ====================

    private boolean matchesKeyword(String text, String keyword) {
        if (!StringUtils.hasText(text) || !StringUtils.hasText(keyword)) {
            return false;
        }
        return text.toLowerCase().contains(keyword.toLowerCase());
    }

    private MetaSearchResultVO convertDatabaseToSearchResult(MetaDatabaseDO database) {
        return MetaSearchResultVO.builder()
                .id(database.getId())
                .objectType("DATABASE")
                .name(database.getName())
                .description(database.getDescription())
                .relevanceScore(0.8)
                .createTime(database.getCreateTime())
                .updateTime(database.getUpdateTime())
                .build();
    }

    private MetaSearchResultVO convertTableToSearchResult(MetaTableDO table) {
        return MetaSearchResultVO.builder()
                .id(table.getId())
                .objectType("TABLE")
                .name(table.getTableName())
                .description(table.getTableComment())
                .relevanceScore(0.7)
                .createTime(table.getCreateTime())
                .updateTime(table.getUpdateTime())
                .parentObject(MetaSearchResultVO.ParentObjectInfo.builder()
                        .id(table.getDbId())
                        .type("DATABASE")
                        .build())
                .build();
    }

    private MetaSearchResultVO convertColumnToSearchResult(MetaColumnDO column) {
        return MetaSearchResultVO.builder()
                .id(column.getId())
                .objectType("COLUMN")
                .name(column.getColumnName())
                .description(column.getColumnComment())
                .dataType(column.getColumnType())
                .dataLength(column.getColumnLength())
                .nullable(column.getIsNullable())
                .primaryKey(column.getIsPrimaryKey())
                .relevanceScore(0.6)
                .createTime(column.getCreateTime())
                .updateTime(column.getUpdateTime())
                .parentObject(MetaSearchResultVO.ParentObjectInfo.builder()
                        .id(column.getTableId())
                        .type("TABLE")
                        .build())
                .build();
    }

    private List<MetaSearchResultVO> applyFilters(List<MetaSearchResultVO> results, MetaSearchReqVO reqVO) {
        return results.stream()
                .filter(result -> {
                    // 应用各种过滤器
                    if (reqVO.getMinRelevanceScore() != null && 
                        result.getRelevanceScore() < reqVO.getMinRelevanceScore()) {
                        return false;
                    }
                    
                    if (reqVO.getDataTypes() != null && !reqVO.getDataTypes().isEmpty() &&
                        "COLUMN".equals(result.getObjectType()) &&
                        !reqVO.getDataTypes().contains(result.getDataType())) {
                        return false;
                    }
                    
                    return true;
                })
                .collect(Collectors.toList());
    }

    private List<MetaSearchResultVO> sortResults(List<MetaSearchResultVO> results, String sortBy, String sortOrder) {
        Comparator<MetaSearchResultVO> comparator;
        
        switch (sortBy) {
            case "name":
                comparator = Comparator.comparing(MetaSearchResultVO::getName);
                break;
            case "createTime":
                comparator = Comparator.comparing(MetaSearchResultVO::getCreateTime);
                break;
            case "updateTime":
                comparator = Comparator.comparing(MetaSearchResultVO::getUpdateTime);
                break;
            case "relevance":
            default:
                comparator = Comparator.comparing(MetaSearchResultVO::getRelevanceScore);
                break;
        }
        
        if ("DESC".equals(sortOrder)) {
            comparator = comparator.reversed();
        }
        
        return results.stream().sorted(comparator).collect(Collectors.toList());
    }

    private List<MetaSearchResultVO> highlightResults(List<MetaSearchResultVO> results, String keyword) {
        return results.stream()
                .peek(result -> {
                    if (result.getName() != null && result.getName().toLowerCase().contains(keyword.toLowerCase())) {
                        result.setDisplayName(highlightText(result.getName(), keyword));
                    }
                    if (result.getDescription() != null && result.getDescription().toLowerCase().contains(keyword.toLowerCase())) {
                        result.setDisplayDescription(highlightText(result.getDescription(), keyword));
                    }
                })
                .collect(Collectors.toList());
    }

    private String highlightText(String text, String keyword) {
        if (!StringUtils.hasText(text) || !StringUtils.hasText(keyword)) {
            return text;
        }
        
        String regex = "(?i)" + keyword;
        return text.replaceAll(regex, "<em>$0</em>");
    }

    private List<MetaSearchResultVO> processAdvancedSearchConditions(MetaAdvancedSearchReqVO reqVO) {
        // 高级搜索条件处理逻辑
        List<MetaSearchResultVO> results = new ArrayList<>();
        
        // 这里需要根据具体的条件组进行复杂的查询逻辑
        log.info("Processing advanced search conditions: {}", reqVO.getConditionGroups().size());
        
        return results;
    }

    private List<MetaSearchResultVO> applySortRules(List<MetaSearchResultVO> results, List<MetaAdvancedSearchReqVO.SortRule> sortRules) {
        // 应用多重排序规则
        Comparator<MetaSearchResultVO> comparator = null;
        
        for (MetaAdvancedSearchReqVO.SortRule rule : sortRules) {
            Comparator<MetaSearchResultVO> fieldComparator = getFieldComparator(rule.getField());
            
            if ("DESC".equals(rule.getDirection())) {
                fieldComparator = fieldComparator.reversed();
            }
            
            if (comparator == null) {
                comparator = fieldComparator;
            } else {
                comparator = comparator.thenComparing(fieldComparator);
            }
        }
        
        return results.stream().sorted(comparator).collect(Collectors.toList());
    }

    private Comparator<MetaSearchResultVO> getFieldComparator(String field) {
        switch (field) {
            case "name":
                return Comparator.comparing(MetaSearchResultVO::getName, Comparator.nullsLast(String::compareTo));
            case "createTime":
                return Comparator.comparing(MetaSearchResultVO::getCreateTime, Comparator.nullsLast(LocalDateTime::compareTo));
            case "updateTime":
                return Comparator.comparing(MetaSearchResultVO::getUpdateTime, Comparator.nullsLast(LocalDateTime::compareTo));
            case "relevance":
            default:
                return Comparator.comparing(MetaSearchResultVO::getRelevanceScore, Comparator.nullsLast(Double::compareTo));
        }
    }

    private boolean matchesFilters(MetaSearchResultVO result, Map<String, Object> filters) {
        // 过滤器匹配逻辑
        for (Map.Entry<String, Object> filter : filters.entrySet()) {
            String filterKey = filter.getKey();
            Object filterValue = filter.getValue();
            
            // 根据不同的过滤器类型进行匹配
            if (!matchesFilter(result, filterKey, filterValue)) {
                return false;
            }
        }
        
        return true;
    }

    private boolean matchesFilter(MetaSearchResultVO result, String filterKey, Object filterValue) {
        // 具体的过滤器匹配逻辑
        switch (filterKey) {
            case "objectType":
                return filterValue.equals(result.getObjectType());
            case "dataType":
                return filterValue.equals(result.getDataType());
            case "nullable":
                return filterValue.equals(result.getNullable());
            case "primaryKey":
                return filterValue.equals(result.getPrimaryKey());
            default:
                return true;
        }
    }

    private List<MetaSearchResultVO> fuzzySearchDatabases(String keyword, Integer fuzzyLevel) {
        // 模糊搜索数据库实现
        return searchDatabases(keyword, "relevance", "DESC");
    }

    private List<MetaSearchResultVO> fuzzySearchTables(String keyword, Integer fuzzyLevel) {
        // 模糊搜索表实现
        return searchTables(keyword, null, "relevance", "DESC");
    }

    private List<MetaSearchResultVO> fuzzySearchColumns(String keyword, Integer fuzzyLevel) {
        // 模糊搜索列实现
        return searchColumns(keyword, null, null, "relevance", "DESC");
    }

    private String exportToCsv(List<MetaSearchResultVO> results) {
        StringBuilder csv = new StringBuilder();
        csv.append("ID,Type,Name,Description,DataType,CreateTime\n");
        
        for (MetaSearchResultVO result : results) {
            csv.append(result.getId()).append(",")
               .append(result.getObjectType()).append(",")
               .append(result.getName()).append(",")
               .append(result.getDescription() != null ? result.getDescription() : "").append(",")
               .append(result.getDataType() != null ? result.getDataType() : "").append(",")
               .append(result.getCreateTime() != null ? result.getCreateTime() : "").append("\n");
        }
        
        return csv.toString();
    }

    private String exportToExcel(List<MetaSearchResultVO> results) {
        // Excel导出实现（需要使用POI库）
        return "Excel export not implemented yet";
    }

    private String exportToJson(List<MetaSearchResultVO> results) {
        // JSON导出实现
        try {
            return new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(results);
        } catch (Exception e) {
            log.error("Error exporting to JSON", e);
            return "{}";
        }
    }
}
