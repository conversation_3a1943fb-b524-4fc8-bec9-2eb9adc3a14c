package com.data.platform.datamind.server.datameta.entity.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

@Data
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectionInfo {

    private String host;
    private Integer port;
    private String username;
    @JSONField(serialize = false)
    private String password;
    private String database;
    private String serviceName;
    private Integer instanceId;
    private String archeryHost;
    private String dbType;
    private String currentSchema;
    private Integer id;
    private Integer dbId;

}
