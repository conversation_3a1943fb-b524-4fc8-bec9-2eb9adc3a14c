package com.data.platform.datamind.server.datameta.service.web;

import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncReqVO;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncRespVO;
import com.data.platform.datamind.server.datameta.vo.quality.DataQualityReportRespVO;

import javax.validation.Valid;

/**
 * 元数据管理增强服务接口
 * 
 * <AUTHOR>
 */
public interface MetaDataEnhancedService {

    /**
     * 元数据自动同步
     * 
     * @param reqVO 同步请求参数
     * @return 同步结果
     */
    MetaDataSyncRespVO syncMetaData(@Valid MetaDataSyncReqVO reqVO);

    /**
     * 查询表血缘关系
     * 
     * @param tableId 表ID
     * @return 血缘关系信息
     */
    TableLineageRespVO getTableLineage(Long tableId);

    /**
     * 获取数据质量报告
     * 
     * @return 数据质量报告
     */
    DataQualityReportRespVO getDataQualityReport();

    /**
     * 获取指定数据库的数据质量报告
     * 
     * @param databaseId 数据库ID
     * @return 数据质量报告
     */
    DataQualityReportRespVO getDataQualityReport(Long databaseId);
}
