package com.data.platform.datamind.server.datameta.dal.mysql;


import java.util.List;

import com.data.platform.datamind.server.datameta.entity.dto.DatabaseBasicInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.SqlInfoRunningDTO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface IDBMetaDao {
  @Select({"select c.ID as \"id\",c.DB_TYPE as \"dbType\",c.ACCESS_ADDRESS as \"accessAddress\",\n               c.INSTANCE_NAME as \"instanceName\",c.DB_NAME as \"dbName\",c.STATUS as \"status\",\n               c.INSTANCE_ID as \"instanceId\",c.DB_PORT as \"dbPort\",c.DB_USER as \"dbUser\"\n        from sql_job_info a ,sql_job_db_info b,database_basic_info c\n        where a.job_id=b.job_id\n          and b.db_id=c.id\n          and a.valid_flag='02'\n          and c.status='02'\n          and c.db_type='oracle'\n          and c.region_flag='01'\n          and a.job_id=#{0}"})
  List<DatabaseBasicInfoDTO> getJobLiyouOracleDbInfo(String paramString);
  
  @Select({"select count(*) from SQL_INFO_RUNNING where oracle_sql_id = #{0}"})
  int querySqlInfoRunningBySqlId(String paramString);
  
  @Insert({"INSERT INTO SQL_INFO_RUNNING(DB_ID,DB_TYPE,DB_USERNAME,ORACLE_SQL_ID,STATEMENT_CONTENT,CREATE_TIME,AI_REVIEW_STAT,SQL_RUNTIME,SQL_DESC)\n        values(#{dbID},#{dbType},#{dbUsername},#{oracleSqlID},#{statementContent},#{createTime},#{aiReviewStat},#{sqlRuntime},#{sqlDesc})"})
  @Options(useGeneratedKeys = true, keyColumn = "sql_info_running_ID", keyProperty = "sqlInfoRunningID")
  Integer insertSqlInfoRunning(SqlInfoRunningDTO paramSqlInfoRunningDTO);
}
