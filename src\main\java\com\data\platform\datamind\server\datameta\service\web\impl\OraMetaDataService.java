package com.data.platform.datamind.server.datameta.service.web.impl;

import com.data.platform.datamind.server.datameta.entity.bo.ConnectionInfo;
import com.data.platform.datamind.server.datameta.entity.dto.MetaColInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaIndexInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaTableInfoDTO;
import com.data.platform.datamind.server.datameta.service.web.MetaDataService;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class OraMetaDataService implements MetaDataService {
  private static final Logger log = LoggerFactory.getLogger(OraMetaDataService.class);
  
  private static final String TABLE_QUERY = "select OWNER,TABLE_NAME,NUM_ROWS,(SELECT COUNT(1) FROM ALL_TAB_PARTITIONS b  WHERE b.TABLE_OWNER = OWNER and b.table_name=a.table_name ) AS PART_NUM from all_tables a where owner not in ('SYS','SYSTEM','DBSNMP','SYSMAN')";
  
  private static final String COl_QUERY = "select OWNER,TABLE_NAME,COLUMN_NAME,DATA_TYPE,NUM_DISTINCT from  all_tab_columns where owner not in ('SYS','SYSTEM','DBSNMP','SYSMAN')";
  
  private static final String INDEX_QUERY = "select a.owner,a.TABLE_NAME,a.UNIQUENESS,b.COLUMN_NAME,b.COLUMN_POSITION,a.INDEX_NAME from all_indexes a join all_ind_columns b on a.index_name = b.index_name where  a.owner not in ('SYS','SYSTEM','DBSNMP','SYSMAN') and a.OWNER = b.INDEX_OWNER ";
  
  private final DataSource dataSource;
  
  public OraMetaDataService(DataSource dataSource) {
    this.dataSource = dataSource;
  }
  
  public Logger getLogger() {
    return log;
  }
  
  public List<MetaTableInfoDTO> getMetaTableInfoList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaTableInfoDTO> tableList = new ArrayList<>();
    try {
      PreparedStatement prepared = connection.prepareStatement("select OWNER,TABLE_NAME,NUM_ROWS,(SELECT COUNT(1) FROM ALL_TAB_PARTITIONS b  WHERE b.TABLE_OWNER = OWNER and b.table_name=a.table_name ) AS PART_NUM from all_tables a where owner not in ('SYS','SYSTEM','DBSNMP','SYSMAN')");
      ResultSet rs = prepared.executeQuery();
      while (rs.next()) {
        MetaTableInfoDTO metaTableInfoDTO = new MetaTableInfoDTO();
        metaTableInfoDTO.setTabName(rs.getString("TABLE_NAME"));
        metaTableInfoDTO.setRecordNum(Long.valueOf(rs.getLong("NUM_ROWS")));
        metaTableInfoDTO.setPartitionNum(Integer.valueOf(rs.getInt("PART_NUM")));
        metaTableInfoDTO.setTabOwner(rs.getString("OWNER"));
        metaTableInfoDTO.setInstanceId(connectionInfo.getInstanceId());
        metaTableInfoDTO.setUpdateTime(new Date());
        tableList.add(metaTableInfoDTO);
      } 
      rs.close();
      prepared.close();
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return tableList;
  }
  
  public List<MetaColInfoDTO> getMetaColInfoDTOList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaColInfoDTO> colList = new ArrayList<>();
    try {
      PreparedStatement prepared = connection.prepareStatement("select OWNER,TABLE_NAME,COLUMN_NAME,DATA_TYPE,NUM_DISTINCT from  all_tab_columns where owner not in ('SYS','SYSTEM','DBSNMP','SYSMAN')");
      prepared.setFetchSize(1000);
      ResultSet rs = prepared.executeQuery();
      while (rs.next()) {
        MetaColInfoDTO metaColInfoDTO = new MetaColInfoDTO();
        metaColInfoDTO.setInstanceId(connectionInfo.getInstanceId());
        metaColInfoDTO.setTabOwner(rs.getString("OWNER"));
        metaColInfoDTO.setTabName(rs.getString("TABLE_NAME"));
        metaColInfoDTO.setColName(rs.getString("COLUMN_NAME"));
        metaColInfoDTO.setDistinctNum(Long.valueOf(rs.getLong("NUM_DISTINCT")));
        metaColInfoDTO.setDataType(rs.getString("DATA_TYPE"));
        metaColInfoDTO.setUpdateTime(new Date());
        colList.add(metaColInfoDTO);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return colList;
  }
  
  public List<MetaIndexInfoDTO> getMetaIndexInfoDTOList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaIndexInfoDTO> indexList = new ArrayList<>();
    try {
      PreparedStatement prepared = connection.prepareStatement("select a.owner,a.TABLE_NAME,a.UNIQUENESS,b.COLUMN_NAME,b.COLUMN_POSITION,a.INDEX_NAME from all_indexes a join all_ind_columns b on a.index_name = b.index_name where  a.owner not in ('SYS','SYSTEM','DBSNMP','SYSMAN') and a.OWNER = b.INDEX_OWNER ");
      ResultSet rs = prepared.executeQuery();
      while (rs.next()) {
        MetaIndexInfoDTO dto = new MetaIndexInfoDTO();
        dto.setInstanceId(connectionInfo.getInstanceId());
        dto.setTabOwner(rs.getString("OWNER"));
        dto.setTabName(rs.getString("TABLE_NAME"));
        dto.setIndexName(rs.getString("INDEX_NAME"));
        dto.setColName(rs.getString("COLUMN_NAME"));
        dto.setIndexPosition(Integer.valueOf(rs.getInt("COLUMN_POSITION")));
        dto.setIndexType(rs.getString("UNIQUENESS"));
        dto.setUpdateTime(new Date());
        indexList.add(dto);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return indexList;
  }
  
  public Connection getDestConnection() {
    try {
      return this.dataSource.getConnection();
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
  }
  
  public String buildConnectionInfo(ConnectionInfo connectionInfo) {
    return String.format("****************************", new Object[] { connectionInfo.getHost(), connectionInfo.getPort(), connectionInfo.getServiceName() });
  }
}