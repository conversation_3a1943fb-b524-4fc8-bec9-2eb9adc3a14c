package com.data.platform.datamind.server.datameta.controller.admin;

import com.data.platform.datamind.framework.common.pojo.CommonResult;
import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaTableConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.data.platform.datamind.server.datameta.vo.table.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static com.data.platform.datamind.framework.common.pojo.CommonResult.success;

/**
 * 表元数据管理 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:20
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.controller.admin
 * @description 表元数据管理控制器，提供表信息的CRUD操作
 * @email <EMAIL>
 * @since 1.8
 */
@Tag(name = "管理后台 - 表元数据管理")
@RestController
@RequestMapping("/admin-api/data-meta/table")
@Validated
public class MetaTableController {

    @Resource
    private MetaTableService tableService;

    @PostMapping("/create")
    @Operation(summary = "创建表元数据")
    @PreAuthorize("@ss.hasPermission('meta:table:create')")
    public CommonResult<Long> createTable(@Valid @RequestBody MetaTableCreateReqVO createReqVO) {
        Long id = tableService.createTable(createReqVO);
        return success(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新表元数据")
    @PreAuthorize("@ss.hasPermission('meta:table:update')")
    public CommonResult<Boolean> updateTable(@Valid @RequestBody MetaTableUpdateReqVO updateReqVO) {
        tableService.updateTable(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除表元数据")
    @Parameter(name = "id", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:table:delete')")
    public CommonResult<Boolean> deleteTable(@RequestParam("id") Long id) {
        tableService.deleteTable(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得表元数据")
    @Parameter(name = "id", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:table:query')")
    public CommonResult<MetaTableRespVO> getTable(@RequestParam("id") Long id) {
        MetaTableDO table = tableService.getTable(id);
        return success(MetaTableConvert.INSTANCE.convert(table));
    }

    @GetMapping("/list")
    @Operation(summary = "获得表元数据列表")
    @Parameter(name = "ids", description = "表ID列表", required = true, example = "1,2")
    @PreAuthorize("@ss.hasPermission('meta:table:query')")
    public CommonResult<List<MetaTableRespVO>> getTableList(@RequestParam("ids") Collection<Long> ids) {
        List<MetaTableDO> list = tableService.getTableList(ids);
        return success(MetaTableConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得表元数据分页")
    @PreAuthorize("@ss.hasPermission('meta:table:query')")
    public CommonResult<PageResult<MetaTableRespVO>> getTablePage(@Valid MetaTablePageReqVO pageVO) {
        PageResult<MetaTableDO> pageResult = tableService.getTablePage(pageVO);
        return success(MetaTableConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-by-db")
    @Operation(summary = "根据数据库ID获取表列表")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:table:query')")
    public CommonResult<List<MetaTableRespVO>> getTablesByDbId(@RequestParam("dbId") Long dbId) {
        List<MetaTableDO> list = tableService.getTablesByDbId(dbId);
        return success(MetaTableConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/get-by-name")
    @Operation(summary = "根据数据库ID和表名获取表信息")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @Parameter(name = "tableName", description = "表名", required = true, example = "user_info")
    @PreAuthorize("@ss.hasPermission('meta:table:query')")
    public CommonResult<MetaTableRespVO> getTableByDbIdAndTableName(@RequestParam("dbId") Long dbId,
                                                                    @RequestParam("tableName") String tableName) {
        MetaTableDO table = tableService.getTableByDbIdAndTableName(dbId, tableName);
        return success(MetaTableConvert.INSTANCE.convert(table));
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建表元数据")
    @PreAuthorize("@ss.hasPermission('meta:table:create')")
    public CommonResult<Boolean> batchCreateTables(@Valid @RequestBody List<MetaTableCreateReqVO> createReqVOs) {
        List<MetaTableDO> tables = MetaTableConvert.INSTANCE.convertList(createReqVOs);
        tableService.batchInsert(tables);
        return success(true);
    }

    @PutMapping("/batch-update")
    @Operation(summary = "批量更新表元数据")
    @PreAuthorize("@ss.hasPermission('meta:table:update')")
    public CommonResult<Boolean> batchUpdateTables(@Valid @RequestBody List<MetaTableUpdateReqVO> updateReqVOs) {
        List<MetaTableDO> tables = MetaTableConvert.INSTANCE.convertUpdateList(updateReqVOs);
        tableService.batchUpdate(tables);
        return success(true);
    }
}
