package com.data.platform.datamind.server.datameta.vo.database;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:32
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;

/**
 * 数据库/数据源元数据 Base VO，所有 VO 的公共字段
 */
@Data
public class MetaDatabaseBaseVO {

    @Schema(description = "数据库或数据源名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "my_database_prod")
    @NotEmpty(message = "数据库名称不能为空")
    private String name;

    @Schema(description = "数据库类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "MySQL")
    @NotEmpty(message = "数据库类型不能为空")
    private String type;

    @Schema(description = "JDBC连接URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "*********************************************************************")
    @NotEmpty(message = "JDBC URL不能为空")
    private String jdbcUrl;

    @Schema(description = "数据库连接用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "root")
    @NotEmpty(message = "用户名不能为空")
    private String username;

    // 密码字段不放在 BaseVO 中，因为 RespVO 不应该包含

    @Schema(description = "描述", example = "生产环境的用户中心数据库")
    private String description;

    @Schema(description = "状态 (0:禁用, 1:启用)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
