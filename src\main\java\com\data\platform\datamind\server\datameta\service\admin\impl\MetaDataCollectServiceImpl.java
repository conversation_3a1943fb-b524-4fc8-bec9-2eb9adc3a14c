package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.framework.common.exception.util.ServiceExceptionUtil;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDataCollectReqVO;
import com.data.platform.datamind.server.datameta.infrans.enumeration.MetaDataErrorCodeConstants;
import com.data.platform.datamind.server.datameta.service.admin.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.sql.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 元数据采集 Service 实现类
 * 负责从外部数据库采集元数据并同步到内部存储和图谱
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MetaDataCollectServiceImpl implements MetaDataCollectService {

    @Resource
    private MetaDatabaseService metaDatabaseService;
    @Resource
    private MetaTableService metaTableService;
    @Resource
    private MetaColumnService metaColumnService;
    @Resource
    private MetaDataGraphService metaDataGraphService;

    @Override
    @Transactional(rollbackFor = Exception.class) // 保证事务一致性
    public void collectAndSyncMetaData(MetaDataCollectReqVO reqVO) throws SQLException {
        Long databaseId = reqVO.getDatabaseId();
        List<String> collectTables = reqVO.getCollectTables();

        MetaDatabaseDO dbConfig = metaDatabaseService.getDatabase(databaseId);
        if (dbConfig == null) {
            throw ServiceExceptionUtil.exception(MetaDataErrorCodeConstants.METADATABASE_NOT_EXISTS);
        }

        Connection conn = null;
        try {
            // 确保驱动已加载，对于Spring Boot应用，通常会自动加载classpath中的JDBC驱动
            // Class.forName("com.mysql.cj.jdbc.Driver"); // For MySQL 8+
            conn = DriverManager.getConnection(dbConfig.getAccessAddress(), dbConfig.getUsername(), dbConfig.getPassword());
            DatabaseMetaData metaData = conn.getMetaData();

            // 1. 采集表信息
            List<MetaTableDO> collectedTables = new ArrayList<>();
            ResultSet tablesRs = null;
            try {
                // 如果指定了要采集的表，则精确采集；否则采集所有
                String[] tableTypes = new String[]{"TABLE"};
                if (CollectionUtils.isEmpty(collectTables)) {
                    tablesRs = metaData.getTables(null, null, "%", tableTypes);
                } else {
                    for (String tableName : collectTables) {
                        ResultSet rs = metaData.getTables(null, null, tableName, tableTypes);
                        while(rs.next()){
                            // 将结果添加到主结果集，或者直接处理
                            collectedTables.add(mapResultSetToMetaTableDO(rs, databaseId));
                        }
                        rs.close();
                    }
                    // 如果指定了collectTables，tablesRs可能为空，因为我们已经在循环中处理了
                }

                if(tablesRs != null) { // 如果是全量采集
                    while (tablesRs.next()) {
                        collectedTables.add(mapResultSetToMetaTableDO(tablesRs, databaseId));
                    }
                }
            } finally {
                if (tablesRs != null) {
                    tablesRs.close();
                }
            }


            // 2. 同步表信息到 MySQL 的 meta_table 表
            List<MetaTableDO> existingTables = metaTableService.getTablesByDbId(databaseId);
            Map<String, MetaTableDO> existingTableMap = existingTables.stream()
                    .collect(Collectors.toMap(MetaTableDO::getTableName, Function.identity()));

            List<MetaTableDO> tablesToInsert = new ArrayList<>();
            List<MetaTableDO> tablesToUpdate = new ArrayList<>();
            Set<String> collectedTableNames = new HashSet<>();

            for (MetaTableDO collectedTable : collectedTables) {
                collectedTableNames.add(collectedTable.getTableName());
                if (existingTableMap.containsKey(collectedTable.getTableName())) {
                    MetaTableDO existingTable = existingTableMap.get(collectedTable.getTableName());
                    collectedTable.setId(existingTable.getId()); // 更新时带上ID
                    tablesToUpdate.add(collectedTable);
                } else {
                    tablesToInsert.add(collectedTable);
                }
            }

            // 执行批量插入和更新
            if (!tablesToInsert.isEmpty()) {
                metaTableService.batchInsert(tablesToInsert);
            }
            if (!tablesToUpdate.isEmpty()) {
                metaTableService.batchUpdate(tablesToUpdate);
            }

            // 删除MySQL中已不存在的表（如果全量采集）
            if (CollectionUtils.isEmpty(collectTables)) { // 只有全量采集时才清理
                List<String> tablesToDelete = existingTables.stream()
                        .map(MetaTableDO::getTableName)
                        .filter(name -> !collectedTableNames.contains(name))
                        .collect(Collectors.toList());
                if (!tablesToDelete.isEmpty()) {
                    metaTableService.deleteTablesByDbIdAndTableNames(databaseId, tablesToDelete);
                    log.info("[MetaDataCollectService] Deleted {} tables for dbId {}: {}", tablesToDelete.size(), databaseId, tablesToDelete);
                }
            }


            // 重新获取已持久化的表，因为新插入的表才会有ID，且可能有删除操作
            List<MetaTableDO> currentTables = metaTableService.getTablesByDbId(databaseId);
            Map<String, Long> tableNameToIdMap = currentTables.stream()
                    .collect(Collectors.toMap(MetaTableDO::getTableName, MetaTableDO::getId));
            Map<Long, String> idToTableNameMap = currentTables.stream()
                    .collect(Collectors.toMap(MetaTableDO::getId, MetaTableDO::getTableName));


            // 3. 采集列信息、主外键信息
            List<MetaColumnDO> collectedColumns = new ArrayList<>();
            // 存储每张表当前的列，方便后续设置主外键
            Map<Long, Map<String, MetaColumnDO>> tableIdToColumnsMap = new HashMap<>();

            for (MetaTableDO table : currentTables) {
                Long currentTableId = table.getId();
                Map<String, MetaColumnDO> columnsInCurrentTable = new HashMap<>();

                // 获取列信息
                ResultSet columnsRs = metaData.getColumns(null, null, table.getTableName(), null);
                try {
                    while (columnsRs.next()) {
                        MetaColumnDO columnDO = mapResultSetToMetaColumnDO(columnsRs, currentTableId);
                        columnsInCurrentTable.put(columnDO.getColumnName(), columnDO);
                        collectedColumns.add(columnDO);
                    }
                } finally {
                    columnsRs.close();
                }

                // 获取主键信息
                ResultSet pkRs = metaData.getPrimaryKeys(null, null, table.getTableName());
                try {
                    while (pkRs.next()) {
                        String pkColumnName = pkRs.getString("COLUMN_NAME");
                        MetaColumnDO columnDO = columnsInCurrentTable.get(pkColumnName);
                        if (columnDO != null) {
                            columnDO.setIsPrimaryKey(true);
                            columnDO.setColumnKey("PRI");
                        }
                    }
                } finally {
                    pkRs.close();
                }

                tableIdToColumnsMap.put(currentTableId, columnsInCurrentTable);
            }

            // 4. 处理外键信息 (需要所有列都采集完毕才能设置fkColumnId)
            for (MetaTableDO table : currentTables) {
                ResultSet fkRs = metaData.getImportedKeys(null, null, table.getTableName());
                try {
                    while (fkRs.next()) {
                        String fkColumnName = fkRs.getString("FKCOLUMN_NAME"); // 本表的外键列名
                        String pkTableName = fkRs.getString("PKTABLE_NAME");   // 引用目标表名
                        String pkColumnName = fkRs.getString("PKCOLUMN_NAME"); // 引用目标列名

                        MetaColumnDO sourceColumn = tableIdToColumnsMap.get(table.getId()).get(fkColumnName);
                        if (sourceColumn != null) {
                            Long targetTableId = tableNameToIdMap.get(pkTableName);
                            if (targetTableId != null) {
                                MetaColumnDO targetColumn = tableIdToColumnsMap.get(targetTableId).get(pkColumnName);
                                if (targetColumn != null) {
                                    sourceColumn.setIsForeignKey(true);
                                    sourceColumn.setColumnKey("MUL"); // 外键通常也是索引
                                    sourceColumn.setFkTableId(targetTableId);
                                    // 重点：这里需要目标列的ID，而不是名称。确保目标列DO在map中已存在并有ID。
                                    // 由于是全量采集后处理，此时targetColumn的ID应该已经存在或会后续分配
                                    // 这里暂时设置为null，后面批量更新时会根据名字查找
                                    // 更准确的方式是，先持久化所有非外键属性的列，再遍历设置外键并更新
                                    // 或者在service层根据tableId和columnName查询到fkColumnId
                                }
                            }
                        }
                    }
                } finally {
                    fkRs.close();
                }
            }


            // 5. 同步列信息到 MySQL 的 meta_column 表
            List<MetaColumnDO> existingColumns = metaColumnService.getColumnsByDbId(databaseId);
            Map<String, MetaColumnDO> existingColumnMap = existingColumns.stream()
                    .collect(Collectors.toMap(c -> c.getTableId() + "_" + c.getColumnName(), Function.identity()));

            List<MetaColumnDO> columnsToInsert = new ArrayList<>();
            List<MetaColumnDO> columnsToUpdate = new ArrayList<>();
            Set<String> collectedColumnKeys = new HashSet<>(); // 用于删除旧列

            // First pass: Fill IDs for existing columns, and identify new columns
            for (MetaColumnDO collectedColumn : collectedColumns) {
                String key = collectedColumn.getTableId() + "_" + collectedColumn.getColumnName();
                collectedColumnKeys.add(key);
                if (existingColumnMap.containsKey(key)) {
                    MetaColumnDO existingColumn = existingColumnMap.get(key);
                    collectedColumn.setId(existingColumn.getId()); // 更新时带上ID
                    columnsToUpdate.add(collectedColumn);
                } else {
                    columnsToInsert.add(collectedColumn);
                }
            }

            // Execute batch inserts
            if (!columnsToInsert.isEmpty()) {
                metaColumnService.batchInsert(columnsToInsert);
                // After insert, update columnsToUpdate with new IDs if they were inserted
                // For example, if an existing column was deleted in DB and re-added in source,
                // it would be in columnsToInsert, but its 'existing' counterpart would be gone
                // This is where a more robust sync strategy is needed.
                // For now, assume batchInsert populates IDs and then retrieve all current columns again
                // Or simply proceed with update and let MERGE handle in graph sync.
            }

            // Re-fetch current columns after inserts to ensure all have IDs, esp. for FKs
            List<MetaColumnDO> allCurrentColumnsAfterInsert = metaColumnService.getColumnsByDbId(databaseId);
            Map<String, Long> columnNameToIdMap = allCurrentColumnsAfterInsert.stream()
                    .collect(Collectors.toMap(c -> c.getTableId() + "_" + c.getColumnName(), MetaColumnDO::getId));

            // Second pass: Update FK_Column_Id based on newly assigned IDs
            for (MetaColumnDO column : collectedColumns) {
                if (Boolean.TRUE.equals(column.getIsForeignKey()) && column.getFkTableId() != null) {
                    // Try to get the target column's ID after all inserts have happened
                    String targetColumnKey = column.getFkTableId() + "_" + column.getFkColumnId(); // Note: fkColumnId here is still original FKColumnName, not ID.
                    // This is a flaw in the current `MetaColumnDO` design if `fk_column_id` is for name.
                    // It should store the actual ID of the referenced column.
                    // Let's assume `fkColumnId` in `MetaColumnDO` is indeed the ID of the referenced column.
                    // The problem is `DatabaseMetaData.getImportedKeys` gives PK_COLUMN_NAME.

                    // Corrected approach:
                    // After the first pass, we have updated `fkTableId` but `fkColumnId` is still null (or old ID)
                    // We need to re-query the `MetaColumnDO` to get the actual `fkColumnId`
                    MetaColumnDO actualTargetColumn = metaColumnService.getColumnByTableIdAndColumnName(column.getFkTableId(), column.getFkColumnId().toString()); // Assuming FKColumnId temporarily stores name
                    if (actualTargetColumn != null) {
                        column.setFkColumnId(actualTargetColumn.getId()); // Set the actual ID
                    } else {
                        log.warn("Could not find target column for FK: {} -> {}.{}", column.getColumnName(), idToTableNameMap.get(column.getFkTableId()), column.getFkColumnId());
                        column.setIsForeignKey(false); // If target not found, disable FK
                        column.setFkTableId(null);
                        column.setFkColumnId(null);
                    }
                }
            }
            // Now, perform batch updates including fixed FK IDs
            if (!columnsToUpdate.isEmpty()) {
                metaColumnService.batchUpdate(columnsToUpdate);
            }

            // 删除MySQL中已不存在的列（如果全量采集）
            if (CollectionUtils.isEmpty(collectTables)) { // 只有全量采集时才清理
                List<String> columnsToDeleteKeys = existingColumns.stream()
                        .map(c -> c.getTableId() + "_" + c.getColumnName())
                        .filter(key -> !collectedColumnKeys.contains(key))
                        .collect(Collectors.toList());

                // 遍历这些需要删除的key，按tableId分组，然后调用deleteColumnsByTableIdAndColumnNames
                Map<Long, List<String>> tableIdToColumnNamesToDelete = columnsToDeleteKeys.stream()
                        .map(key -> {
                            String[] parts = key.split("_");
                            return new AbstractMap.SimpleEntry<>(Long.parseLong(parts[0]), parts[1]);
                        })
                        .collect(Collectors.groupingBy(Map.Entry::getKey,
                                Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

                tableIdToColumnNamesToDelete.forEach((tId, cNames) -> {
                    metaColumnService.deleteColumnsByTableIdAndColumnNames(tId, cNames);
                    log.info("[MetaDataCollectService] Deleted {} columns for tableId {}: {}", cNames.size(), tId, cNames);
                });
            }

            // 6. 将MySQL元数据同步到Neo4j
            // 在这一步，确保所有MySQL数据都已持久化并拥有正确的ID和外键关联ID
            List<MetaDatabaseDO> allDbs = metaDatabaseService.getAllDatabases();
            List<MetaTableDO> allTables = metaTableService.getAllTables();
            List<MetaColumnDO> allColumns = metaColumnService.getAllColumns();
            metaDataGraphService.syncAllMetaDataToGraph(allDbs, allTables, allColumns);

            log.info("[MetaDataCollectService] MetaData collection and sync completed successfully for databaseId: {}", databaseId);

        } catch (SQLException e) {
            log.error("[MetaDataCollectService] MetaData collection failed for databaseId: {}", databaseId, e);
            throw ServiceExceptionUtil.exception(MetaDataErrorCodeConstants.METADATA_COLLECT_FAILED, e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    log.error("[MetaDataCollectService] Failed to close database connection for databaseId: {}", databaseId, e);
                }
            }
        }
    }

    private MetaTableDO mapResultSetToMetaTableDO(ResultSet rs, Long dbId) throws SQLException {
        String tableName = rs.getString("TABLE_NAME");
        String tableComment = rs.getString("REMARKS");
        String tableType = rs.getString("TABLE_TYPE"); // E.g., "TABLE", "VIEW"

        // 获取表行数、数据大小、引擎、字符集等信息需要额外的查询
        // 例如：使用 Statement executeQuery("SHOW TABLE STATUS LIKE '" + tableName + "'")
        // 简化起见，这里先不填充这些字段
        return MetaTableDO.builder()
                .dbId(dbId)
                .tableName(tableName)
                .tableComment(tableComment)
                .tableRows(0L) // 默认值
                .dataLength(0L) // 默认值
                .engine(null) // 默认值
                .charset(null) // 默认值
                .build();
    }

    private MetaColumnDO mapResultSetToMetaColumnDO(ResultSet rs, Long tableId) throws SQLException {
        String columnName = rs.getString("COLUMN_NAME");
        String columnType = rs.getString("TYPE_NAME");
        String dataType = rs.getString("TYPE_NAME") + "(" + rs.getInt("COLUMN_SIZE") + ")"; // 示例：VARCHAR(255)
        String columnComment = rs.getString("REMARKS");
        int isNullableInt = rs.getInt("NULLABLE");
        String columnDefault = rs.getString("COLUMN_DEF");
        String extra = rs.getString("IS_AUTOINCREMENT");

        return MetaColumnDO.builder()
                .tableId(tableId)
                .columnName(columnName)
                .columnType(columnType)
                .dataType(dataType)
                .columnComment(columnComment)
                .isNullable(isNullableInt == DatabaseMetaData.columnNullable)
                .columnDefault(columnDefault)
                .extra("YES".equalsIgnoreCase(extra) ? "auto_increment" : null)
                .isPrimaryKey(false) // 默认false，后续单独设置
                .isForeignKey(false) // 默认false，后续单独设置
                .fkTableId(null)
                .fkColumnId(null)
                .build();
    }
}
