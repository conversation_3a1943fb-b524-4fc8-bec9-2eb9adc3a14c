package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import com.data.platform.datamind.server.datameta.service.admin.MetaIndexService;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.data.platform.datamind.server.datameta.service.admin.RelationshipAnalyzer;
import com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表关系分析器实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin.impl
 * @description 表关系分析器实现，提供表间关系分析功能
 * @email <EMAIL>
 * @since 1.8
 */
@Service
@Slf4j
public class RelationshipAnalyzerImpl implements RelationshipAnalyzer {

    @Resource
    private MetaTableService metaTableService;

    @Resource
    private MetaColumnService metaColumnService;

    @Resource
    private MetaIndexService metaIndexService;

    @Override
    public TableRelationshipRespVO analyzeTableRelationships(Long tableId) {
        MetaTableDO table = metaTableService.getTable(tableId);
        if (table == null) {
            return null;
        }

        TableRelationshipRespVO result = new TableRelationshipRespVO();
        result.setTableId(tableId);
        result.setTableName(table.getTableName());
        result.setDatabaseId(table.getDbId());

        // 分析父表关系
        List<MetaTableDO> parentTables = getParentTables(tableId);
        result.setParentTables(convertToRelatedTableInfo(parentTables, "PARENT"));

        // 分析子表关系
        List<MetaTableDO> childTables = getChildTables(tableId);
        result.setChildTables(convertToRelatedTableInfo(childTables, "CHILD"));

        // 分析关联表
        List<MetaTableDO> relatedTables = getRelatedTables(tableId);
        result.setRelatedTables(convertToRelatedTableInfo(relatedTables, "RELATED"));

        // 分析外键关系
        result.setForeignKeyRelations(analyzeForeignKeyRelations(tableId));

        // 生成统计信息
        result.setStatistics(generateRelationshipStatistics(result));

        // 依赖关系分析
        result.setDependencyAnalysis(analyzeDependencies(tableId));

        // 影响范围分析
        result.setImpactAnalysis(analyzeImpactScope(tableId));

        return result;
    }

    @Override
    public Map<String, Object> analyzeDatabaseRelationships(Long dbId) {
        List<MetaTableDO> tables = metaTableService.getTablesByDbId(dbId);
        Map<String, Object> analysis = new HashMap<>();

        analysis.put("totalTables", tables.size());
        analysis.put("tablesWithRelations", tables.stream()
                .filter(table -> !getRelatedTables(table.getId()).isEmpty())
                .count());
        analysis.put("isolatedTables", getIsolatedTables(dbId).size());

        // 关系图数据
        analysis.put("relationshipGraph", generateRelationshipGraph(dbId));

        // 循环依赖检测
        analysis.put("circularDependencies", detectCircularDependencies(dbId));

        return analysis;
    }

    @Override
    public List<MetaTableDO> getParentTables(Long tableId) {
        // 通过外键关系找到父表
        List<MetaIndexDO> foreignKeys = metaIndexService.getIndexesByTableId(tableId).stream()
                .filter(idx -> "FOREIGN".equalsIgnoreCase(idx.getIndexType()) || 
                              idx.getIndexName().toLowerCase().contains("fk_"))
                .collect(Collectors.toList());

        Set<Long> parentTableIds = new HashSet<>();
        for (MetaIndexDO fk : foreignKeys) {
            // 这里需要根据外键约束找到引用的表，简化实现
            // 实际应该通过外键约束信息来确定父表
            String indexName = fk.getIndexName().toLowerCase();
            if (indexName.contains("fk_")) {
                // 简单的命名约定解析，实际应该查询约束信息
                // 假设外键命名为 fk_table_column 格式
                String[] parts = indexName.split("_");
                if (parts.length >= 3) {
                    String referencedTableName = parts[1];
                    MetaTableDO referencedTable = metaTableService.getTableByDbIdAndTableName(
                            fk.getDbId(), referencedTableName);
                    if (referencedTable != null) {
                        parentTableIds.add(referencedTable.getId());
                    }
                }
            }
        }

        return metaTableService.getTableList(parentTableIds);
    }

    @Override
    public List<MetaTableDO> getChildTables(Long tableId) {
        MetaTableDO currentTable = metaTableService.getTable(tableId);
        if (currentTable == null) {
            return new ArrayList<>();
        }

        // 查找引用当前表的子表
        List<MetaTableDO> allTables = metaTableService.getTablesByDbId(currentTable.getDbId());
        List<MetaTableDO> childTables = new ArrayList<>();

        for (MetaTableDO table : allTables) {
            if (table.getId().equals(tableId)) {
                continue;
            }

            List<MetaIndexDO> foreignKeys = metaIndexService.getIndexesByTableId(table.getId()).stream()
                    .filter(idx -> "FOREIGN".equalsIgnoreCase(idx.getIndexType()) || 
                                  idx.getIndexName().toLowerCase().contains("fk_"))
                    .collect(Collectors.toList());

            for (MetaIndexDO fk : foreignKeys) {
                String indexName = fk.getIndexName().toLowerCase();
                if (indexName.contains(currentTable.getTableName().toLowerCase())) {
                    childTables.add(table);
                    break;
                }
            }
        }

        return childTables;
    }

    @Override
    public List<MetaTableDO> getRelatedTables(Long tableId) {
        Set<MetaTableDO> relatedTables = new HashSet<>();
        relatedTables.addAll(getParentTables(tableId));
        relatedTables.addAll(getChildTables(tableId));
        return new ArrayList<>(relatedTables);
    }

    @Override
    public Map<String, Object> analyzeDependencies(Long tableId) {
        Map<String, Object> dependencies = new HashMap<>();
        
        List<MetaTableDO> parentTables = getParentTables(tableId);
        List<MetaTableDO> childTables = getChildTables(tableId);
        
        dependencies.put("directDependencies", parentTables.size());
        dependencies.put("dependents", childTables.size());
        dependencies.put("dependencyLevel", calculateDependencyLevel(tableId));
        dependencies.put("isLeafTable", childTables.isEmpty());
        dependencies.put("isRootTable", parentTables.isEmpty());
        
        return dependencies;
    }

    @Override
    public List<List<String>> detectCircularDependencies(Long dbId) {
        // 简化的循环依赖检测实现
        List<MetaTableDO> tables = metaTableService.getTablesByDbId(dbId);
        List<List<String>> cycles = new ArrayList<>();
        
        // 构建依赖图
        Map<String, Set<String>> dependencyGraph = new HashMap<>();
        for (MetaTableDO table : tables) {
            List<MetaTableDO> parents = getParentTables(table.getId());
            Set<String> parentNames = parents.stream()
                    .map(MetaTableDO::getTableName)
                    .collect(Collectors.toSet());
            dependencyGraph.put(table.getTableName(), parentNames);
        }
        
        // 使用DFS检测循环
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();
        
        for (String tableName : dependencyGraph.keySet()) {
            if (!visited.contains(tableName)) {
                List<String> cycle = detectCycleDFS(tableName, dependencyGraph, visited, recursionStack, new ArrayList<>());
                if (!cycle.isEmpty()) {
                    cycles.add(cycle);
                }
            }
        }
        
        return cycles;
    }

    @Override
    public Map<String, Object> generateRelationshipGraph(Long dbId) {
        List<MetaTableDO> tables = metaTableService.getTablesByDbId(dbId);
        Map<String, Object> graph = new HashMap<>();
        
        // 节点数据
        List<Map<String, Object>> nodes = tables.stream().map(table -> {
            Map<String, Object> node = new HashMap<>();
            node.put("id", table.getId());
            node.put("name", table.getTableName());
            node.put("comment", table.getTableComment());
            node.put("type", "table");
            return node;
        }).collect(Collectors.toList());
        
        // 边数据
        List<Map<String, Object>> edges = new ArrayList<>();
        for (MetaTableDO table : tables) {
            List<MetaTableDO> relatedTables = getRelatedTables(table.getId());
            for (MetaTableDO related : relatedTables) {
                Map<String, Object> edge = new HashMap<>();
                edge.put("source", table.getId());
                edge.put("target", related.getId());
                edge.put("type", "relationship");
                edges.add(edge);
            }
        }
        
        graph.put("nodes", nodes);
        graph.put("edges", edges);
        
        return graph;
    }

    @Override
    public Map<String, Object> analyzeImpactScope(Long tableId) {
        Map<String, Object> impact = new HashMap<>();
        
        Set<Long> impactedTables = new HashSet<>();
        collectImpactedTables(tableId, impactedTables, new HashSet<>());
        
        impact.put("directImpact", getChildTables(tableId).size());
        impact.put("totalImpact", impactedTables.size());
        impact.put("impactLevel", calculateImpactLevel(impactedTables.size()));
        
        return impact;
    }

    @Override
    public List<String> getRelationshipPath(Long sourceTableId, Long targetTableId) {
        // 简化的路径查找实现
        MetaTableDO sourceTable = metaTableService.getTable(sourceTableId);
        MetaTableDO targetTable = metaTableService.getTable(targetTableId);
        
        if (sourceTable == null || targetTable == null) {
            return new ArrayList<>();
        }
        
        // 使用BFS查找最短路径
        Queue<List<String>> queue = new LinkedList<>();
        Set<String> visited = new HashSet<>();
        
        queue.offer(Arrays.asList(sourceTable.getTableName()));
        visited.add(sourceTable.getTableName());
        
        while (!queue.isEmpty()) {
            List<String> path = queue.poll();
            String currentTable = path.get(path.size() - 1);
            
            if (currentTable.equals(targetTable.getTableName())) {
                return path;
            }
            
            // 获取当前表的关联表
            MetaTableDO current = metaTableService.getTableByDbIdAndTableName(
                    sourceTable.getDbId(), currentTable);
            if (current != null) {
                List<MetaTableDO> relatedTables = getRelatedTables(current.getId());
                for (MetaTableDO related : relatedTables) {
                    if (!visited.contains(related.getTableName())) {
                        visited.add(related.getTableName());
                        List<String> newPath = new ArrayList<>(path);
                        newPath.add(related.getTableName());
                        queue.offer(newPath);
                    }
                }
            }
        }
        
        return new ArrayList<>(); // 没有找到路径
    }

    @Override
    public Map<String, Object> analyzeRelationshipStrength(Long tableId) {
        Map<String, Object> strength = new HashMap<>();
        
        List<MetaTableDO> relatedTables = getRelatedTables(tableId);
        int totalRelations = relatedTables.size();
        
        // 计算关系强度
        if (totalRelations == 0) {
            strength.put("level", "NONE");
            strength.put("score", 0);
        } else if (totalRelations <= 2) {
            strength.put("level", "WEAK");
            strength.put("score", 1);
        } else if (totalRelations <= 5) {
            strength.put("level", "MEDIUM");
            strength.put("score", 2);
        } else {
            strength.put("level", "STRONG");
            strength.put("score", 3);
        }
        
        strength.put("relationCount", totalRelations);
        
        return strength;
    }

    @Override
    public List<String> suggestRelationshipOptimization(Long dbId) {
        List<String> suggestions = new ArrayList<>();
        
        List<MetaTableDO> isolatedTables = getIsolatedTables(dbId);
        if (!isolatedTables.isEmpty()) {
            suggestions.add("发现 " + isolatedTables.size() + " 个孤立表，建议检查是否需要建立关系");
        }
        
        List<List<String>> cycles = detectCircularDependencies(dbId);
        if (!cycles.isEmpty()) {
            suggestions.add("发现 " + cycles.size() + " 个循环依赖，建议重新设计表关系");
        }
        
        return suggestions;
    }

    @Override
    public Map<String, Object> validateRelationshipConsistency(Long dbId) {
        Map<String, Object> validation = new HashMap<>();
        
        List<List<String>> cycles = detectCircularDependencies(dbId);
        validation.put("hasCircularDependencies", !cycles.isEmpty());
        validation.put("circularDependencyCount", cycles.size());
        
        List<MetaTableDO> isolatedTables = getIsolatedTables(dbId);
        validation.put("isolatedTableCount", isolatedTables.size());
        
        validation.put("isConsistent", cycles.isEmpty());
        
        return validation;
    }

    @Override
    public List<MetaTableDO> getIsolatedTables(Long dbId) {
        List<MetaTableDO> allTables = metaTableService.getTablesByDbId(dbId);
        return allTables.stream()
                .filter(table -> getRelatedTables(table.getId()).isEmpty())
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> analyzeRelationshipComplexity(Long tableId) {
        Map<String, Object> complexity = new HashMap<>();
        
        List<MetaTableDO> relatedTables = getRelatedTables(tableId);
        int relationCount = relatedTables.size();
        
        // 计算复杂度级别
        String complexityLevel;
        if (relationCount == 0) {
            complexityLevel = "ISOLATED";
        } else if (relationCount <= 2) {
            complexityLevel = "SIMPLE";
        } else if (relationCount <= 5) {
            complexityLevel = "MEDIUM";
        } else if (relationCount <= 10) {
            complexityLevel = "COMPLEX";
        } else {
            complexityLevel = "VERY_COMPLEX";
        }
        
        complexity.put("level", complexityLevel);
        complexity.put("relationCount", relationCount);
        complexity.put("depth", calculateRelationshipDepth(tableId));
        
        return complexity;
    }

    // 辅助方法
    private List<TableRelationshipRespVO.RelatedTableInfo> convertToRelatedTableInfo(
            List<MetaTableDO> tables, String relationType) {
        return tables.stream().map(table -> {
            TableRelationshipRespVO.RelatedTableInfo info = new TableRelationshipRespVO.RelatedTableInfo();
            info.setTableId(table.getId());
            info.setTableName(table.getTableName());
            info.setTableComment(table.getTableComment());
            info.setRelationType(relationType);
            info.setRelationStrength("MEDIUM"); // 简化实现
            info.setRelationColumnCount(1); // 简化实现
            return info;
        }).collect(Collectors.toList());
    }

    private List<TableRelationshipRespVO.ForeignKeyRelation> analyzeForeignKeyRelations(Long tableId) {
        // 简化的外键关系分析
        List<MetaIndexDO> foreignKeys = metaIndexService.getIndexesByTableId(tableId).stream()
                .filter(idx -> "FOREIGN".equalsIgnoreCase(idx.getIndexType()) || 
                              idx.getIndexName().toLowerCase().contains("fk_"))
                .collect(Collectors.toList());

        return foreignKeys.stream().map(fk -> {
            TableRelationshipRespVO.ForeignKeyRelation relation = new TableRelationshipRespVO.ForeignKeyRelation();
            relation.setForeignKeyName(fk.getIndexName());
            relation.setSourceTableId(fk.getTableId());
            relation.setSourceTableName(fk.getTableName());
            relation.setSourceColumnName(fk.getColumnName());
            // 简化实现，实际应该查询约束信息
            relation.setConstraintType("CASCADE");
            return relation;
        }).collect(Collectors.toList());
    }

    private TableRelationshipRespVO.RelationshipStatistics generateRelationshipStatistics(
            TableRelationshipRespVO result) {
        TableRelationshipRespVO.RelationshipStatistics stats = new TableRelationshipRespVO.RelationshipStatistics();
        
        int totalRelations = result.getParentTables().size() + result.getChildTables().size();
        stats.setTotalRelations(totalRelations);
        stats.setParentTableCount(result.getParentTables().size());
        stats.setChildTableCount(result.getChildTables().size());
        stats.setForeignKeyCount(result.getForeignKeyRelations().size());
        
        // 计算复杂度
        if (totalRelations == 0) {
            stats.setComplexityLevel("ISOLATED");
        } else if (totalRelations <= 2) {
            stats.setComplexityLevel("SIMPLE");
        } else if (totalRelations <= 5) {
            stats.setComplexityLevel("MEDIUM");
        } else {
            stats.setComplexityLevel("COMPLEX");
        }
        
        stats.setRelationDepth(calculateRelationshipDepth(result.getTableId()));
        
        return stats;
    }

    private int calculateDependencyLevel(Long tableId) {
        // 简化的依赖级别计算
        return getParentTables(tableId).size();
    }

    private String calculateImpactLevel(int impactCount) {
        if (impactCount == 0) return "NONE";
        if (impactCount <= 2) return "LOW";
        if (impactCount <= 5) return "MEDIUM";
        return "HIGH";
    }

    private void collectImpactedTables(Long tableId, Set<Long> impactedTables, Set<Long> visited) {
        if (visited.contains(tableId)) {
            return;
        }
        visited.add(tableId);
        
        List<MetaTableDO> childTables = getChildTables(tableId);
        for (MetaTableDO child : childTables) {
            impactedTables.add(child.getId());
            collectImpactedTables(child.getId(), impactedTables, visited);
        }
    }

    private List<String> detectCycleDFS(String tableName, Map<String, Set<String>> graph,
                                       Set<String> visited, Set<String> recursionStack, List<String> path) {
        visited.add(tableName);
        recursionStack.add(tableName);
        path.add(tableName);
        
        Set<String> dependencies = graph.getOrDefault(tableName, new HashSet<>());
        for (String dependency : dependencies) {
            if (!visited.contains(dependency)) {
                List<String> cycle = detectCycleDFS(dependency, graph, visited, recursionStack, new ArrayList<>(path));
                if (!cycle.isEmpty()) {
                    return cycle;
                }
            } else if (recursionStack.contains(dependency)) {
                // 找到循环
                List<String> cycle = new ArrayList<>(path);
                cycle.add(dependency);
                return cycle;
            }
        }
        
        recursionStack.remove(tableName);
        return new ArrayList<>();
    }

    private int calculateRelationshipDepth(Long tableId) {
        // 简化的关系深度计算
        return Math.max(calculateDepth(tableId, true), calculateDepth(tableId, false));
    }

    private int calculateDepth(Long tableId, boolean upward) {
        Set<Long> visited = new HashSet<>();
        return calculateDepthRecursive(tableId, upward, visited, 0);
    }

    private int calculateDepthRecursive(Long tableId, boolean upward, Set<Long> visited, int currentDepth) {
        if (visited.contains(tableId)) {
            return currentDepth;
        }
        visited.add(tableId);
        
        List<MetaTableDO> relatedTables = upward ? getParentTables(tableId) : getChildTables(tableId);
        if (relatedTables.isEmpty()) {
            return currentDepth;
        }
        
        int maxDepth = currentDepth;
        for (MetaTableDO related : relatedTables) {
            int depth = calculateDepthRecursive(related.getId(), upward, visited, currentDepth + 1);
            maxDepth = Math.max(maxDepth, depth);
        }
        
        return maxDepth;
    }
}
