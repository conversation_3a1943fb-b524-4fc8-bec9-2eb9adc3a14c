package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.vo.database.MetaDataCollectReqVO;

import javax.validation.Valid;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:15
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 元数据采集 Service 接口
 * 负责从外部数据库采集元数据并同步到内部存储和图谱
 * @email <EMAIL>
 * @since 1.8
 */
public interface MetaDataCollectService {

    /**
     * 根据数据库配置ID采集并同步元数据
     *
     * @param reqVO 采集请求，包含数据库ID和可选的表名列表
     * @throws SQLException 如果JDBC操作失败
     */
    void collectAndSyncMetaData(@Valid MetaDataCollectReqVO reqVO) throws SQLException;
}
