package com.data.platform.datamind.server.datameta.controller.admin;

import com.data.platform.datamind.framework.common.pojo.CommonResult;
import com.data.platform.datamind.server.datameta.convert.MetaTableConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.service.admin.RelationshipAnalyzer;
import com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipRespVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.data.platform.datamind.framework.common.pojo.CommonResult.success;

/**
 * 表关系分析管理 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.controller.admin
 * @description 表关系分析管理控制器，提供表间关系分析功能
 * @email <EMAIL>
 * @since 1.8
 */
@Tag(name = "管理后台 - 表关系分析管理")
@RestController
@RequestMapping("/admin-api/data-meta/relationship")
@Validated
public class MetaRelationshipController {

    @Resource
    private RelationshipAnalyzer relationshipAnalyzer;

    @GetMapping("/analyze-table")
    @Operation(summary = "分析表的关系")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<TableRelationshipRespVO> analyzeTableRelationships(@RequestParam("tableId") Long tableId) {
        TableRelationshipRespVO result = relationshipAnalyzer.analyzeTableRelationships(tableId);
        return success(result);
    }

    @GetMapping("/analyze-database")
    @Operation(summary = "分析数据库中所有表的关系")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<Map<String, Object>> analyzeDatabaseRelationships(@RequestParam("dbId") Long dbId) {
        Map<String, Object> result = relationshipAnalyzer.analyzeDatabaseRelationships(dbId);
        return success(result);
    }

    @GetMapping("/parent-tables")
    @Operation(summary = "获取表的父表")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<List<MetaTableRespVO>> getParentTables(@RequestParam("tableId") Long tableId) {
        List<MetaTableDO> parentTables = relationshipAnalyzer.getParentTables(tableId);
        return success(MetaTableConvert.INSTANCE.convertList(parentTables));
    }

    @GetMapping("/child-tables")
    @Operation(summary = "获取表的子表")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<List<MetaTableRespVO>> getChildTables(@RequestParam("tableId") Long tableId) {
        List<MetaTableDO> childTables = relationshipAnalyzer.getChildTables(tableId);
        return success(MetaTableConvert.INSTANCE.convertList(childTables));
    }

    @GetMapping("/related-tables")
    @Operation(summary = "获取表的关联表")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<List<MetaTableRespVO>> getRelatedTables(@RequestParam("tableId") Long tableId) {
        List<MetaTableDO> relatedTables = relationshipAnalyzer.getRelatedTables(tableId);
        return success(MetaTableConvert.INSTANCE.convertList(relatedTables));
    }

    @GetMapping("/dependencies")
    @Operation(summary = "分析表的依赖关系")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<Map<String, Object>> analyzeDependencies(@RequestParam("tableId") Long tableId) {
        Map<String, Object> result = relationshipAnalyzer.analyzeDependencies(tableId);
        return success(result);
    }

    @GetMapping("/circular-dependencies")
    @Operation(summary = "检测循环依赖")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<List<List<String>>> detectCircularDependencies(@RequestParam("dbId") Long dbId) {
        List<List<String>> result = relationshipAnalyzer.detectCircularDependencies(dbId);
        return success(result);
    }

    @GetMapping("/relationship-graph")
    @Operation(summary = "生成表关系图数据")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<Map<String, Object>> generateRelationshipGraph(@RequestParam("dbId") Long dbId) {
        Map<String, Object> result = relationshipAnalyzer.generateRelationshipGraph(dbId);
        return success(result);
    }

    @GetMapping("/impact-scope")
    @Operation(summary = "分析表的影响范围")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<Map<String, Object>> analyzeImpactScope(@RequestParam("tableId") Long tableId) {
        Map<String, Object> result = relationshipAnalyzer.analyzeImpactScope(tableId);
        return success(result);
    }

    @GetMapping("/relationship-path")
    @Operation(summary = "获取表的关系路径")
    @Parameter(name = "sourceTableId", description = "源表ID", required = true, example = "1")
    @Parameter(name = "targetTableId", description = "目标表ID", required = true, example = "2")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<List<String>> getRelationshipPath(@RequestParam("sourceTableId") Long sourceTableId,
                                                          @RequestParam("targetTableId") Long targetTableId) {
        List<String> result = relationshipAnalyzer.getRelationshipPath(sourceTableId, targetTableId);
        return success(result);
    }

    @GetMapping("/relationship-strength")
    @Operation(summary = "分析表的关系强度")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<Map<String, Object>> analyzeRelationshipStrength(@RequestParam("tableId") Long tableId) {
        Map<String, Object> result = relationshipAnalyzer.analyzeRelationshipStrength(tableId);
        return success(result);
    }

    @GetMapping("/optimization-suggestions")
    @Operation(summary = "建议表关系优化")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<List<String>> suggestRelationshipOptimization(@RequestParam("dbId") Long dbId) {
        List<String> result = relationshipAnalyzer.suggestRelationshipOptimization(dbId);
        return success(result);
    }

    @GetMapping("/validate-consistency")
    @Operation(summary = "验证表关系一致性")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<Map<String, Object>> validateRelationshipConsistency(@RequestParam("dbId") Long dbId) {
        Map<String, Object> result = relationshipAnalyzer.validateRelationshipConsistency(dbId);
        return success(result);
    }

    @GetMapping("/isolated-tables")
    @Operation(summary = "获取孤立表")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<List<MetaTableRespVO>> getIsolatedTables(@RequestParam("dbId") Long dbId) {
        List<MetaTableDO> isolatedTables = relationshipAnalyzer.getIsolatedTables(dbId);
        return success(MetaTableConvert.INSTANCE.convertList(isolatedTables));
    }

    @GetMapping("/relationship-complexity")
    @Operation(summary = "分析表的关系复杂度")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:relationship:query')")
    public CommonResult<Map<String, Object>> analyzeRelationshipComplexity(@RequestParam("tableId") Long tableId) {
        Map<String, Object> result = relationshipAnalyzer.analyzeRelationshipComplexity(tableId);
        return success(result);
    }
}
