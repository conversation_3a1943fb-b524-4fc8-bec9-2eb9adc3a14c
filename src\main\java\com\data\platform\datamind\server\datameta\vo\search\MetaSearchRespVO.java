package com.data.platform.datamind.server.datameta.vo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 元数据搜索响应VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "元数据搜索响应")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetaSearchRespVO {

    @Schema(description = "搜索结果列表")
    private List<MetaSearchResultVO> results;

    @Schema(description = "总记录数", example = "100")
    private Long total;

    @Schema(description = "搜索耗时（毫秒）", example = "50")
    private Long searchDuration;

    @Schema(description = "搜索时间", example = "2023-01-01T00:00:00")
    private LocalDateTime searchTime;

    @Schema(description = "搜索关键词", example = "user")
    private String keyword;

    @Schema(description = "搜索类型", example = "FULL_TEXT")
    private String searchType;

    @Schema(description = "分面统计信息")
    private Map<String, FacetInfo> facets;

    @Schema(description = "搜索建议")
    private List<String> suggestions;

    @Schema(description = "相关搜索")
    private List<String> relatedSearches;

    @Schema(description = "搜索统计信息")
    private SearchStatistics statistics;

    @Schema(description = "是否有更多结果", example = "true")
    private Boolean hasMore;

    @Schema(description = "下一页令牌", example = "next_page_token")
    private String nextPageToken;

    /**
     * 分面信息
     */
    @Schema(description = "分面信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FacetInfo {
        @Schema(description = "分面名称", example = "objectType")
        private String name;

        @Schema(description = "分面值列表")
        private List<FacetValue> values;

        @Schema(description = "总数", example = "100")
        private Long total;
    }

    /**
     * 分面值
     */
    @Schema(description = "分面值")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FacetValue {
        @Schema(description = "值", example = "TABLE")
        private String value;

        @Schema(description = "显示名称", example = "表")
        private String displayName;

        @Schema(description = "数量", example = "50")
        private Long count;

        @Schema(description = "是否选中", example = "false")
        private Boolean selected;
    }

    /**
     * 搜索统计信息
     */
    @Schema(description = "搜索统计信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchStatistics {
        @Schema(description = "数据库数量", example = "5")
        private Long databaseCount;

        @Schema(description = "表数量", example = "50")
        private Long tableCount;

        @Schema(description = "列数量", example = "500")
        private Long columnCount;

        @Schema(description = "平均相关度分数", example = "0.75")
        private Double avgRelevanceScore;

        @Schema(description = "最高相关度分数", example = "0.95")
        private Double maxRelevanceScore;

        @Schema(description = "最低相关度分数", example = "0.55")
        private Double minRelevanceScore;

        @Schema(description = "搜索命中率", example = "0.85")
        private Double hitRate;
    }
}
