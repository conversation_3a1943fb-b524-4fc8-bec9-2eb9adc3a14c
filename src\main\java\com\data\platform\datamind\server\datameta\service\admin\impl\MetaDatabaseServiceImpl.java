package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaDatabaseConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.mysql.MetaDatabaseMapper;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabasePageReqVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseUpdateReqVO;
import com.data.platform.datamind.server.datameta.service.admin.MetaDatabaseService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static com.data.platform.datamind.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 数据库/数据源元数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MetaDatabaseServiceImpl implements MetaDatabaseService {

    @Resource
    private MetaDatabaseMapper metaDatabaseMapper;

    @Override
    public Long createDatabase(MetaDatabaseCreateReqVO createReqVO) {
        // 校验名称唯一性
        validateDatabaseNameUnique(null, createReqVO.getName());

        // VO -> DO
        MetaDatabaseDO database = MetaDatabaseConvert.INSTANCE.convert(createReqVO);
        // 密码加密
//        database.setPassword(EncryptUtils.encrypt(createReqVO.getPassword()));


        metaDatabaseMapper.insert(database);
        return database.getId();
    }

    @Override
    public void updateDatabase(MetaDatabaseUpdateReqVO updateReqVO) {
        // 校验存在
//        ValidationUtils.verifyNotNull(metaDatabaseMapper.selectById(updateReqVO.getId()), METADATABASE_NOT_EXISTS);

        // 校验名称唯一性
        validateDatabaseNameUnique(updateReqVO.getId(), updateReqVO.getName());

        // VO -> DO
        MetaDatabaseDO database = MetaDatabaseConvert.INSTANCE.convert(updateReqVO);
        // 密码更新：如果传入了新密码，则加密更新
        if (updateReqVO.getPassword() != null) {
//            database.setPassword(EncryptUtils.encrypt(updateReqVO.getPassword()));
            database.setPassword(updateReqVO.getPassword());
        } else {
            // 如果不传入密码，则不更新密码字段，防止覆盖为null
            database.setPassword(null); // MyBatis Plus updateById 默认会更新null值，这里需要特殊处理
            // 更安全的做法是构建UpdateWrapper，只更新非空字段
        }
        metaDatabaseMapper.updateById(database);
    }

    @Override
    public void deleteDatabase(Long id) {
        // 校验存在
//        ValidationUtils.verifyNotNull(metaDatabaseMapper.selectById(id), METADATABASE_NOT_EXISTS);
        // 删除
        metaDatabaseMapper.deleteById(id);
    }

    @Override
    public MetaDatabaseDO getDatabase(Long id) {
        return metaDatabaseMapper.selectById(id);
    }

    @Override
    public List<MetaDatabaseDO> getDatabaseList(List<Long> ids) {
        return metaDatabaseMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MetaDatabaseDO> getDatabasePage(MetaDatabasePageReqVO pageReqVO) {
        return metaDatabaseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MetaDatabaseDO> getAllDatabases() {
        return metaDatabaseMapper.selectList();
    }

    private void validateDatabaseNameUnique(Long id, String name) {
        MetaDatabaseDO database = metaDatabaseMapper.selectOne(MetaDatabaseDO::getName, name);
        if (database == null) {
            return;
        }
        // 如果 id 为空，说明是新增
//        if (id == null) {
//            throw ServiceExceptionUtil.exception("");
//        }
        // 如果 id 不为空，说明是修改，且名字重复的不是自己
//        if (!database.getId().equals(id)) {
//            throw exception(METADATABASE_NAME_DUPLICATE);
//        }
    }
}