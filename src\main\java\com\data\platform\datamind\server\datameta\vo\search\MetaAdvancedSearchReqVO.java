package com.data.platform.datamind.server.datameta.vo.search;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 元数据高级搜索请求VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "元数据高级搜索请求")
@Data
@EqualsAndHashCode(callSuper = true)
public class MetaAdvancedSearchReqVO extends PageParam {

    @Schema(description = "搜索条件组", example = "[]")
    private List<SearchConditionGroup> conditionGroups;

    @Schema(description = "条件组之间的逻辑关系", example = "AND")
    private String groupLogic = "AND"; // AND, OR

    @Schema(description = "排序规则", example = "[]")
    private List<SortRule> sortRules;

    @Schema(description = "结果过滤器", example = "{}")
    private Map<String, Object> resultFilters;

    @Schema(description = "聚合配置", example = "{}")
    private Map<String, Object> aggregations;

    @Schema(description = "是否返回统计信息", example = "true")
    private Boolean includeStatistics = false;

    @Schema(description = "是否返回建议", example = "true")
    private Boolean includeSuggestions = false;

    /**
     * 搜索条件组
     */
    @Schema(description = "搜索条件组")
    @Data
    public static class SearchConditionGroup {
        @Schema(description = "条件列表", example = "[]")
        private List<SearchCondition> conditions;

        @Schema(description = "条件之间的逻辑关系", example = "AND")
        private String logic = "AND"; // AND, OR
    }

    /**
     * 搜索条件
     */
    @Schema(description = "搜索条件")
    @Data
    public static class SearchCondition {
        @Schema(description = "字段名", example = "name")
        private String field;

        @Schema(description = "操作符", example = "CONTAINS")
        private String operator; // EQUALS, CONTAINS, STARTS_WITH, ENDS_WITH, REGEX, IN, NOT_IN, BETWEEN, GT, LT, GTE, LTE

        @Schema(description = "值", example = "user")
        private Object value;

        @Schema(description = "值列表（用于IN操作）", example = "[\"user\", \"admin\"]")
        private List<Object> values;

        @Schema(description = "是否忽略大小写", example = "true")
        private Boolean ignoreCase = true;

        @Schema(description = "是否启用模糊匹配", example = "false")
        private Boolean fuzzy = false;

        @Schema(description = "模糊匹配距离", example = "2")
        private Integer fuzzyDistance = 2;
    }

    /**
     * 排序规则
     */
    @Schema(description = "排序规则")
    @Data
    public static class SortRule {
        @Schema(description = "排序字段", example = "name")
        private String field;

        @Schema(description = "排序方向", example = "ASC")
        private String direction = "ASC"; // ASC, DESC

        @Schema(description = "排序优先级", example = "1")
        private Integer priority = 1;

        @Schema(description = "空值处理", example = "LAST")
        private String nullHandling = "LAST"; // FIRST, LAST
    }
}
