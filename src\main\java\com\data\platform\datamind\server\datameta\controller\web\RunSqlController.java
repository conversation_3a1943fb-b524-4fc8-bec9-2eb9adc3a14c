//package com.data.platform.datamind.server.datainspection.controller.web;
//
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//
//@RestController
//@RequestMapping({"/member/runsql/"})
//public class RunSqlController {
////    @Autowired
////    private RunSqlService runSqlService;
////
////    /**
////     * 慢sql语句信息获取
////     *
////     * @param ao
////     * @return
////     */
////    @PostMapping(value = {"/getSlowSql"}, produces = {"application/json;charset=UTF-8"})
////    public ResponseResult<RunningDTO> getSlowSql(@RequestBody QuerySqlAO ao) {
////        return this.runSqlService.getSlowSql(ao);
////    }
//}