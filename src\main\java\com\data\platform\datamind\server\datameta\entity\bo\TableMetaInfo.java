package com.data.platform.datamind.server.datameta.entity.bo;

import lombok.*;

import java.util.List;

@Data
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableMetaInfo {
    private String tableName;
    private String tableComment;

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    private Long tableRows;
    private List<ColumnInfo> columnInfoList;
    private List<IndexInfo> indexInfoList;

}
