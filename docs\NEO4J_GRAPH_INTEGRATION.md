# Neo4j 图谱集成使用指南

## 概述

DataMind Data-Meta 服务已完成 Neo4j 图谱集成功能，支持将关系型元数据同步到 Neo4j 图数据库，并提供丰富的图谱查询和血缘分析功能。

## 功能特性

### ✅ 已完成功能

1. **Neo4j 集成**
   - Neo4j 数据库连接配置
   - 连接状态检查和健康监控
   - 事务管理和错误处理

2. **图谱建模**
   - 数据库节点（Database）
   - 表节点（Table）
   - 列节点（Column）
   - 关系建模（CONTAINS_TABLE、HAS_COLUMN、REFERENCES）

3. **数据同步功能**
   - 全量同步元数据到图谱
   - 增量同步支持
   - 批量操作优化
   - 索引和约束自动创建

4. **图谱查询接口**
   - 表关系图谱查询
   - 列血缘关系分析
   - 上下游依赖查询
   - 影响分析
   - 全文搜索

## 配置说明

### Neo4j 配置

在 `application-local.yaml` 中添加 Neo4j 配置：

```yaml
spring:
  data:
    neo4j:
      uri: bolt://localhost:7687
      username: neo4j
      password: neo4j123
      database: datamind
```

### 图谱同步配置

```yaml
datamind:
  meta:
    graph:
      enabled: true           # 是否启用图谱同步
      batch-size: 500         # 批量同步大小
      auto-sync: false        # 是否自动同步
```

## API 接口

### 管理端接口

#### 1. 手动触发图谱同步
```http
POST /admin-api/data-meta/graph/sync
```

#### 2. 获取表关系图谱
```http
GET /admin-api/data-meta/graph/table/{tableId}/graph
```

#### 3. 获取列血缘关系
```http
GET /admin-api/data-meta/graph/column/{columnId}/lineage
```

#### 4. 获取表的上游依赖
```http
GET /admin-api/data-meta/graph/table/{tableId}/upstream?depth=3
```

#### 5. 获取表的下游依赖
```http
GET /admin-api/data-meta/graph/table/{tableId}/downstream?depth=3
```

#### 6. 获取数据库表关系概览
```http
GET /admin-api/data-meta/graph/database/{databaseId}/overview
```

#### 7. 搜索相关表
```http
GET /admin-api/data-meta/graph/table/search?keyword=user&limit=20
```

#### 8. 获取表影响分析
```http
GET /admin-api/data-meta/graph/table/{tableId}/impact
```

#### 9. 获取图谱状态
```http
GET /admin-api/data-meta/graph/status
```

### Web端接口

Web端提供相同的查询接口，路径前缀为 `/web-api/data-meta/graph/`，另外还提供：

#### 可视化数据接口
```http
GET /web-api/data-meta/graph/visualization/{tableId}?depth=2
```

## 核心组件

### 1. Neo4jService
- 提供 Neo4j 基础操作
- 支持 Cypher 查询和写操作
- 批量操作支持
- 连接状态检查

### 2. GraphModelBuilder
- 元数据到图谱模型转换
- 节点和关系构建
- RAG 友好的文本生成

### 3. GraphSyncService
- 图谱数据同步
- 增量同步支持
- 索引和约束管理
- 批量操作优化

### 4. GraphQueryService
- 图谱查询功能
- 血缘分析
- 关系查询
- 影响分析

## 图谱模型

### 节点类型

1. **Database（数据库）**
   - 属性：id, name, type, instanceName, description, host, port, status, regionFlag, ragText

2. **Table（表）**
   - 属性：id, dbId, tableName, tableComment, tableOwner, tableRows, dataLength, engine, charset, ragText

3. **Column（列）**
   - 属性：id, tableId, columnName, columnType, dataType, columnComment, isPrimaryKey, isForeignKey, ragText

### 关系类型

1. **CONTAINS_TABLE**：数据库包含表
2. **HAS_COLUMN**：表包含列
3. **REFERENCES**：列引用关系（外键）
4. **RELATED_TO**：表间关系

## 使用示例

### 1. 同步元数据到图谱

```java
@Resource
private MetaDataGraphService metaDataGraphService;

// 获取元数据
List<MetaDatabaseDO> databases = databaseService.getAllDatabases();
List<MetaTableDO> tables = tableService.getAllTables();
List<MetaColumnDO> columns = columnService.getAllColumns();

// 同步到图谱
metaDataGraphService.syncAllMetaDataToGraph(databases, tables, columns);
```

### 2. 查询表关系图谱

```java
// 获取表的关系图谱
Map<String, Object> graph = metaDataGraphService.getTableGraph(tableId);

// 获取节点和边
List<Map<String, Object>> nodes = (List<Map<String, Object>>) graph.get("nodes");
List<Map<String, Object>> edges = (List<Map<String, Object>>) graph.get("edges");
```

### 3. 血缘分析

```java
// 获取列的血缘关系
List<LineageNodeVO> lineage = metaDataGraphService.getColumnLineage(columnId);

// 获取上游依赖
List<TableRelationshipVO> upstream = metaDataGraphService.getUpstreamTables(tableId, 3);

// 获取下游依赖
List<TableRelationshipVO> downstream = metaDataGraphService.getDownstreamTables(tableId, 3);
```

## 测试

运行图谱服务测试：

```bash
mvn test -Dtest=GraphServiceTest
```

## 注意事项

1. **Neo4j 版本兼容性**：建议使用 Neo4j 4.0+ 版本
2. **内存配置**：大量数据同步时需要适当调整 JVM 内存
3. **批量大小**：根据数据量调整批量同步大小
4. **索引优化**：重要属性已自动创建索引
5. **事务管理**：所有写操作都在事务中执行

## 故障排查

### 常见问题

1. **连接失败**
   - 检查 Neo4j 服务是否启动
   - 验证连接配置是否正确
   - 确认网络连通性

2. **同步失败**
   - 检查数据格式是否正确
   - 验证事务是否超时
   - 查看日志错误信息

3. **查询性能问题**
   - 检查索引是否创建
   - 优化 Cypher 查询语句
   - 考虑增加缓存

### 日志配置

```yaml
logging:
  level:
    com.data.platform.datamind.server.datameta.graph: DEBUG
    org.springframework.data.neo4j: DEBUG
```

## 后续规划

1. **性能优化**：查询缓存、连接池优化
2. **可视化增强**：更丰富的图谱可视化功能
3. **AI 集成**：基于图谱的智能推荐
4. **实时同步**：支持实时数据变更同步
