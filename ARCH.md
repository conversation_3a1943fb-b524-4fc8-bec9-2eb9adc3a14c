# DataMind Data-Meta 系统架构设计文档

## 1. 架构概览

### 系统边界定义
DataMind Data-Meta是企业级数据元数据管理服务，作为DataMind Cloud平台的核心组件，负责统一管理多数据源的元数据信息，构建企业数据资产目录和知识图谱。

### 核心业务流程
1. **数据源接入** → 多数据库连接管理和认证
2. **元数据采集** → 自动化采集表结构、字段、索引等信息
3. **数据存储** → MySQL存储结构化元数据，Neo4j构建知识图谱
4. **数据服务** → 提供RESTful API和图谱查询服务
5. **数据治理** → 血缘分析、影响分析、数据质量评估

### 关键质量属性
- **性能指标**: 元数据查询响应时间 <500ms，支持1000+表的采集任务
- **可用性**: 99.5%服务可用性，支持故障自动恢复
- **扩展性**: 支持水平扩展，新数据库类型可插拔式扩展
- **安全性**: 数据源连接信息AES-256加密，基于RBAC的访问控制

## 2. 分层架构图

```mermaid
graph TB
    subgraph "接入层 - Access Layer"
        A1[API Gateway]
        A2[Load Balancer]
        A3[Authentication]
    end
    
    subgraph "服务层 - Service Layer"
        B1[Admin API Service]
        B2[Web API Service]
        B3[Metadata Collection Service]
        B4[Graph Service]
    end
    
    subgraph "业务层 - Business Layer"
        C1[Database Manager]
        C2[Metadata Collector]
        C3[Relationship Analyzer]
        C4[Lineage Analyzer]
        C5[Quality Assessor]
    end
    
    subgraph "数据层 - Data Layer"
        D1[(MySQL<br/>元数据存储)]
        D2[(Neo4j<br/>知识图谱)]
        D3[(Redis<br/>缓存)]
        D4[(Target DBs<br/>数据源)]
    end
    
    A1 --> B1
    A1 --> B2
    B1 --> C1
    B2 --> C2
    B3 --> C2
    B4 --> C3
    C1 --> D1
    C2 --> D1
    C2 --> D4
    C3 --> D2
    C4 --> D2
    C5 --> D1
    
    style A1 fill:#e1f5fe
    style B3 fill:#f3e5f5
    style C2 fill:#e8f5e8
    style D2 fill:#fff3e0
```

## 3. 核心组件设计

### 3.1 数据库连接管理器 (Database Manager)
**职责**: 管理多数据源连接，提供动态数据源切换能力
- **连接池管理**: 基于HikariCP的高性能连接池
- **动态数据源**: Spring Boot Dynamic DataSource实现
- **连接安全**: AES-256加密存储敏感信息
- **健康检查**: 定时检测连接状态，自动故障转移

### 3.2 元数据采集器 (Metadata Collector)
**职责**: 自动化采集多种数据库的元数据信息
- **采集引擎**: 基于JDBC DatabaseMetaData API
- **多数据库适配**: MySQL、Oracle、PostgreSQL、SQL Server等
- **增量同步**: 基于时间戳的增量采集机制
- **批处理**: Spring Batch支持大规模数据处理

### 3.3 关系分析器 (Relationship Analyzer)
**职责**: 分析表间关系，构建数据模型依赖图
- **外键分析**: 自动识别表间外键关系
- **命名规则**: 基于字段命名规则推断隐式关系
- **循环检测**: 检测并报告循环依赖关系
- **影响分析**: 评估表变更的影响范围

### 3.4 血缘分析器 (Lineage Analyzer)
**职责**: 基于Neo4j图数据库进行数据血缘分析
- **图谱构建**: 将元数据映射为图节点和关系
- **血缘追踪**: 追踪数据的上下游流向
- **可视化**: 提供图谱可视化查询接口
- **路径分析**: 计算任意两表间的关系路径

## 4. 技术选型决策

### 4.1 核心框架选型

| 技术领域 | 选型方案 | 选型理由 | 替代方案 |
|---------|----------|----------|----------|
| Web框架 | Spring Boot 2.7.18 | 成熟稳定，生态丰富，团队熟悉 | Spring Boot 3.x |
| 数据访问 | MyBatis Plus 3.5.10 | 简化CRUD操作，支持动态SQL | JPA/Hibernate |
| 连接池 | HikariCP | 高性能，低延迟，Spring Boot默认 | Druid, C3P0 |
| 图数据库 | Neo4j 4.0+ | 图查询性能优异，Cypher语法简洁 | ArangoDB, JanusGraph |
| 缓存 | Redis 6.0+ | 高性能，支持多种数据结构 | Memcached, Hazelcast |

### 4.2 数据库驱动选型

| 数据库类型 | 驱动版本 | 兼容性 | 特殊配置 |
|-----------|----------|--------|----------|
| MySQL | mysql-connector-java:8.0.33 | 5.7+ | useSSL=false |
| PostgreSQL | postgresql:42.6.0 | 10+ | 标准配置 |
| Oracle | ojdbc8:21.9.0.0 | 11g+ | 需要Oracle许可 |
| SQL Server | mssql-jdbc:12.2.0 | 2012+ | 集成认证支持 |

### 4.3 选型权衡分析

**Spring Boot vs Spring Boot 3.x**
- 选择2.7.18：团队熟悉度高，依赖库兼容性好，生产环境稳定
- 权衡：错过了虚拟线程等新特性，但降低了升级风险

**Neo4j vs 关系型数据库**
- 选择Neo4j：图查询性能优异，血缘分析场景匹配度高
- 权衡：增加了技术栈复杂度，但提供了强大的图分析能力

## 5. 数据架构

### 5.1 数据模型设计

```mermaid
erDiagram
    META_DATABASE ||--o{ META_TABLE : contains
    META_TABLE ||--o{ META_COLUMN : has
    META_TABLE ||--o{ META_INDEX : has
    META_TABLE }o--o{ META_TABLE : references
    
    META_DATABASE {
        bigint id PK
        string name
        string type
        string host
        int port
        string database
        string username
        string password
        json properties
        int status
        datetime create_time
        datetime update_time
    }
    
    META_TABLE {
        bigint id PK
        bigint db_id FK
        string table_name
        string table_comment
        string table_owner
        bigint table_rows
        bigint data_length
        string engine
        int partition_num
        int status
        datetime create_time
        datetime update_time
    }
    
    META_COLUMN {
        bigint id PK
        bigint table_id FK
        string column_name
        string column_comment
        string data_type
        int column_length
        int column_scale
        boolean nullable
        string default_value
        boolean is_primary_key
        boolean is_index
        int ordinal_position
        datetime create_time
        datetime update_time
    }
```

### 5.2 数据流设计

```mermaid
flowchart LR
    A[数据源] --> B[采集器]
    B --> C[数据清洗]
    C --> D[MySQL存储]
    C --> E[Neo4j同步]
    D --> F[API服务]
    E --> G[图谱查询]
    F --> H[前端展示]
    G --> H
    
    subgraph "数据处理流程"
        B --> B1[连接验证]
        B1 --> B2[元数据提取]
        B2 --> B3[增量对比]
        B3 --> C
    end
    
    subgraph "存储策略"
        D --> D1[结构化存储]
        E --> E1[图谱建模]
    end
```

### 5.3 存储策略选择

**MySQL存储策略**
- **主键策略**: 雪花算法生成分布式ID
- **分表策略**: 按数据库ID进行水平分表（预留）
- **索引策略**: 
  - 数据库名称、表名称建立联合索引
  - 字段名称、数据类型建立索引
  - 创建时间、更新时间建立索引

**Neo4j存储策略**
- **节点设计**: Database、Table、Column三类节点
- **关系设计**: CONTAINS、HAS_COLUMN、REFERENCES关系
- **索引策略**: 节点名称、ID属性建立索引
- **约束策略**: 节点唯一性约束

## 6. 安全架构

### 6.1 认证授权策略

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant AuthService
    participant DataService
    participant Database
    
    Client->>Gateway: API Request + Token
    Gateway->>AuthService: Validate Token
    AuthService-->>Gateway: User Info + Permissions
    Gateway->>DataService: Authorized Request
    DataService->>Database: Query with Tenant Filter
    Database-->>DataService: Filtered Results
    DataService-->>Gateway: Response
    Gateway-->>Client: Final Response
```

### 6.2 数据加密方案

**传输加密**
- HTTPS/TLS 1.3加密所有API通信
- 数据库连接使用SSL/TLS加密

**存储加密**
- 数据源密码使用AES-256-GCM加密
- 敏感配置信息使用Jasypt加密
- 数据库连接字符串加密存储

**密钥管理**
- 使用Spring Boot配置文件管理加密密钥
- 生产环境建议使用外部密钥管理服务（如HashiCorp Vault）

### 6.3 安全边界设计

**网络安全**
- API网关作为统一入口，隐藏内部服务
- 数据库访问限制在内网，禁止外网直接访问
- 使用防火墙规则限制端口访问

**应用安全**
- SQL注入防护：使用参数化查询
- XSS防护：输入输出过滤和编码
- CSRF防护：使用CSRF Token验证

**数据安全**
- 多租户数据隔离：基于租户ID的数据过滤
- 敏感数据脱敏：日志中屏蔽密码等敏感信息
- 审计日志：记录所有数据访问和修改操作

## 7. 部署架构

### 7.1 容器化策略

**Docker镜像构建**
```dockerfile
# 多阶段构建优化镜像大小
FROM maven:3.8.6-openjdk-8 AS builder
WORKDIR /app
COPY pom.xml .
RUN mvn dependency:go-offline
COPY src ./src
RUN mvn clean package -DskipTests

FROM openjdk:8-jre-alpine
RUN apk add --no-cache tzdata
ENV TZ=Asia/Shanghai
WORKDIR /app
COPY --from=builder /app/target/datamind-server-data-meta.jar app.jar
EXPOSE 8081
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s \
  CMD curl -f http://localhost:8081/actuator/health || exit 1
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
```

### 7.2 服务部署拓扑

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx/HAProxy]
    end

    subgraph "应用服务层"
        APP1[Data-Meta Service 1]
        APP2[Data-Meta Service 2]
        APP3[Data-Meta Service N]
    end

    subgraph "数据存储层"
        MYSQL[(MySQL Cluster)]
        NEO4J[(Neo4j Cluster)]
        REDIS[(Redis Cluster)]
    end

    subgraph "外部数据源"
        DB1[(MySQL)]
        DB2[(Oracle)]
        DB3[(PostgreSQL)]
        DB4[(SQL Server)]
    end

    LB --> APP1
    LB --> APP2
    LB --> APP3

    APP1 --> MYSQL
    APP1 --> NEO4J
    APP1 --> REDIS
    APP2 --> MYSQL
    APP2 --> NEO4J
    APP2 --> REDIS
    APP3 --> MYSQL
    APP3 --> NEO4J
    APP3 --> REDIS

    APP1 -.-> DB1
    APP1 -.-> DB2
    APP2 -.-> DB3
    APP2 -.-> DB4
```

### 7.3 环境配置管理

**配置分层管理**
```yaml
# application.yaml - 基础配置
spring:
  application:
    name: data-meta-server
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}

# application-prod.yaml - 生产环境配置
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ${DB_URL}
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
  data:
    neo4j:
      uri: ${NEO4J_URI}
      username: ${NEO4J_USERNAME}
      password: ${NEO4J_PASSWORD}
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
```

## 8. 可观测性设计

### 8.1 监控指标定义

**业务指标**
- 数据源连接成功率：`datameta.database.connection.success_rate`
- 元数据采集成功率：`datameta.collection.success_rate`
- API响应时间：`datameta.api.response_time`
- 图谱查询性能：`datameta.graph.query_time`

**技术指标**
- JVM内存使用率：`jvm.memory.used`
- 数据库连接池状态：`hikaricp.connections.active`
- Redis连接状态：`redis.connections.active`
- Neo4j连接状态：`neo4j.connections.active`

### 8.2 日志收集策略

**日志分类**
```yaml
# logback-spring.xml配置
logging:
  level:
    com.data.platform.datamind.server.datameta: INFO
    org.springframework.data.neo4j: DEBUG
    com.zaxxer.hikari: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
```

**日志类型**
- **访问日志**: 记录所有API请求和响应
- **业务日志**: 记录元数据采集、同步等关键业务操作
- **错误日志**: 记录异常和错误信息
- **审计日志**: 记录数据变更和敏感操作

### 8.3 性能追踪方案

**分布式链路追踪**
- 使用Spring Cloud Sleuth集成Zipkin
- 为每个请求生成唯一的TraceId
- 追踪跨服务调用链路

**性能分析**
- JVM性能监控：使用Micrometer + Prometheus
- 数据库性能监控：慢查询日志分析
- 缓存性能监控：Redis性能指标收集

## 9. 扩展性设计

### 9.1 水平扩展策略

**应用层扩展**
- 无状态服务设计，支持多实例部署
- 使用负载均衡器分发请求
- 基于CPU和内存使用率自动扩缩容

**数据层扩展**
- MySQL读写分离，支持多个只读副本
- Neo4j集群部署，支持读副本扩展
- Redis集群模式，支持数据分片

### 9.2 垂直扩展策略

**计算资源扩展**
- JVM堆内存动态调整
- 数据库连接池大小动态配置
- 线程池大小根据负载调整

**存储资源扩展**
- 数据库存储空间自动扩展
- 日志文件自动轮转和清理
- 缓存空间根据使用情况调整

### 9.3 容量规划指导

**性能基准测试**
```yaml
# 性能测试指标
performance_benchmarks:
  api_response_time:
    target: "<500ms"
    acceptable: "<1000ms"
  concurrent_users:
    target: "1000"
    max: "5000"
  database_connections:
    target: "100"
    max: "500"
  metadata_collection:
    tables_per_hour: "10000"
    max_table_size: "1000000 rows"
```

**容量规划公式**
- **CPU需求**: 基础2核 + (并发用户数/500) * 1核
- **内存需求**: 基础4GB + (数据库连接数 * 10MB) + (缓存大小)
- **存储需求**: 元数据大小 * 3 (包含索引和备份)
- **网络带宽**: 峰值QPS * 平均响应大小 * 2

## 10. 风险评估与缓解

### 10.1 技术风险识别

| 风险类别 | 风险描述 | 影响等级 | 概率 | 缓解措施 |
|---------|----------|----------|------|----------|
| 性能风险 | 大规模元数据采集导致系统性能下降 | 高 | 中 | 批处理优化、连接池调优、异步处理 |
| 兼容性风险 | 不同数据库版本兼容性问题 | 中 | 高 | 完善的适配器测试、版本兼容性矩阵 |
| 数据一致性风险 | MySQL与Neo4j数据同步不一致 | 高 | 中 | 事务管理、数据校验、补偿机制 |
| 安全风险 | 数据源连接信息泄露 | 高 | 低 | 加密存储、访问控制、审计日志 |

### 10.2 业务风险评估

**数据质量风险**
- **风险**: 采集的元数据不准确或不完整
- **影响**: 影响数据治理决策的准确性
- **缓解**: 多重数据验证、人工审核机制、数据质量评分

**服务可用性风险**
- **风险**: 服务故障导致元数据服务不可用
- **影响**: 影响数据分析和业务决策
- **缓解**: 高可用部署、故障自动恢复、服务降级

### 10.3 缓解措施制定

**技术缓解措施**
1. **性能优化**
   - 实施数据库查询优化
   - 使用缓存减少数据库访问
   - 异步处理大批量操作

2. **高可用保障**
   - 多实例部署避免单点故障
   - 数据库主从复制保证数据安全
   - 健康检查和自动故障转移

3. **安全加固**
   - 定期安全扫描和漏洞修复
   - 访问权限最小化原则
   - 敏感数据加密和脱敏

**运维缓解措施**
1. **监控告警**
   - 建立完善的监控指标体系
   - 设置合理的告警阈值
   - 7x24小时监控值班

2. **应急响应**
   - 制定详细的应急响应预案
   - 定期进行故障演练
   - 建立快速恢复机制

---

## 实施检查清单

- [x] 架构图清晰易懂，标注完整
- [x] 技术选型有明确理由和对比
- [x] 安全方案覆盖主要风险点
- [x] 可观测性方案具体可行
- [x] 扩展性设计考虑未来发展
- [x] 成本控制措施明确有效
- [x] 实施计划具体可执行
- [x] 风险评估全面深入

**文档版本**: v1.0
**创建日期**: 2025-07-05
**最后更新**: 2025-07-05
**架构师**: AI Assistant
**审核状态**: 待审核
