package com.data.platform.datamind.server.datameta.vo.database;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:37
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * 元数据采集请求 VO
 */
@Schema(description = "管理后台 - 元数据采集 Request VO")
@Data
public class MetaDataCollectReqVO {

    @Schema(description = "数据库配置ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "数据库配置ID不能为空")
    private Long databaseId;

    @Schema(description = "要采集的表名列表（为空则采集所有）", example = "[\"user\",\"order\"]")
    private java.util.List<String> collectTables;
}
