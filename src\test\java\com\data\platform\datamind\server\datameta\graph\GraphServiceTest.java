package com.data.platform.datamind.server.datameta.graph;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.graph.model.GraphNode;
import com.data.platform.datamind.server.datameta.graph.model.GraphRelationship;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 图谱服务测试类
 *
 * <AUTHOR> Team
 */
@SpringBootTest
public class GraphServiceTest {

    @Resource
    private GraphModelBuilder graphModelBuilder;

    @Test
    public void testBuildDatabaseNode() {
        // 创建测试数据
        MetaDatabaseDO database = MetaDatabaseDO.builder()
                .id(1L)
                .name("test_db")
                .type("MySQL")
                .instanceName("test_instance")
                .description("测试数据库")
                .host("localhost")
                .port("3306")
                .status("ACTIVE")
                .regionFlag("CN")
                .build();

        // 构建图谱节点
        GraphNode node = graphModelBuilder.buildDatabaseNode(database);

        // 验证结果
        assertNotNull(node);
        assertEquals("Database", node.getLabel());
        assertEquals(1L, node.getId());
        assertEquals("test_db", node.getName());
        assertNotNull(node.getRagText());
        assertTrue(node.getRagText().contains("数据库: test_db"));
    }

    @Test
    public void testBuildTableNode() {
        // 创建测试数据
        MetaTableDO table = MetaTableDO.builder()
                .id(1L)
                .dbId(1L)
                .tableName("user_info")
                .tableComment("用户信息表")
                .tableOwner("root")
                .tableRows(1000L)
                .dataLength(2048000L)
                .engine("InnoDB")
                .charset("utf8mb4")
                .build();

        // 构建图谱节点
        GraphNode node = graphModelBuilder.buildTableNode(table);

        // 验证结果
        assertNotNull(node);
        assertEquals("Table", node.getLabel());
        assertEquals(1L, node.getId());
        assertEquals("user_info", node.getName());
        assertNotNull(node.getRagText());
        assertTrue(node.getRagText().contains("表: user_info"));
    }

    @Test
    public void testBuildColumnNode() {
        // 创建测试数据
        MetaColumnDO column = MetaColumnDO.builder()
                .id(1L)
                .tableId(1L)
                .columnName("user_id")
                .columnType("BIGINT")
                .dataType("bigint(20)")
                .columnComment("用户ID")
                .columnKey("PRI")
                .isNullable(false)
                .isPrimaryKey(true)
                .isForeignKey(false)
                .build();

        // 构建图谱节点
        GraphNode node = graphModelBuilder.buildColumnNode(column);

        // 验证结果
        assertNotNull(node);
        assertEquals("Column", node.getLabel());
        assertEquals(1L, node.getId());
        assertEquals("user_id", node.getName());
        assertNotNull(node.getRagText());
        assertTrue(node.getRagText().contains("列: user_id"));
        assertTrue(node.getRagText().contains("主键"));
    }

    @Test
    public void testBuildRelationships() {
        // 创建测试数据
        MetaDatabaseDO database = MetaDatabaseDO.builder().id(1L).build();
        MetaTableDO table = MetaTableDO.builder().id(1L).dbId(1L).build();
        MetaColumnDO column = MetaColumnDO.builder().id(1L).tableId(1L).build();

        List<MetaDatabaseDO> databases = Arrays.asList(database);
        List<MetaTableDO> tables = Arrays.asList(table);
        List<MetaColumnDO> columns = Arrays.asList(column);

        // 构建关系
        List<GraphRelationship> relationships = graphModelBuilder.buildAllRelationships(databases, tables, columns);

        // 验证结果
        assertNotNull(relationships);
        assertEquals(2, relationships.size()); // 数据库-表关系 + 表-列关系

        // 验证数据库-表关系
        GraphRelationship dbTableRel = relationships.stream()
                .filter(r -> "CONTAINS_TABLE".equals(r.getType()))
                .findFirst()
                .orElse(null);
        assertNotNull(dbTableRel);
        assertEquals(1L, dbTableRel.getSourceNodeId());
        assertEquals(1L, dbTableRel.getTargetNodeId());

        // 验证表-列关系
        GraphRelationship tableColumnRel = relationships.stream()
                .filter(r -> "HAS_COLUMN".equals(r.getType()))
                .findFirst()
                .orElse(null);
        assertNotNull(tableColumnRel);
        assertEquals(1L, tableColumnRel.getSourceNodeId());
        assertEquals(1L, tableColumnRel.getTargetNodeId());
    }

    @Test
    public void testBuildForeignKeyRelationship() {
        // 测试外键关系构建
        GraphRelationship fkRel = graphModelBuilder.buildForeignKeyRelationship(1L, 2L);

        assertNotNull(fkRel);
        assertEquals("REFERENCES", fkRel.getType());
        assertEquals(1L, fkRel.getSourceNodeId());
        assertEquals(2L, fkRel.getTargetNodeId());
        assertEquals("FOREIGN_KEY", fkRel.getProperties().get("relationshipType"));
    }
}
