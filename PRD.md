# DataMind Data-Meta 数据元数据服务 - 产品需求文档 (PRD)

```yaml
# === PROJECT METADATA ===
project_name: "DataMind Data-Meta 数据元数据管理服务"
version: "v1.0"
created_date: "2025-01-04"
last_updated: "2025-01-04"
project_type: "microservice"
complexity_level: "medium"
estimated_duration: "6 weeks"
service_port: 8081
```

## 🎯 产品概览 (Product Overview)

### 核心价值主张
> 企业级数据元数据管理服务，提供数据库连接管理、元数据自动采集、表结构管理和知识图谱构建能力

### 目标用户画像
- **主要用户**: 数据工程师、数据分析师、数据治理专员
- **使用场景**: 
  - 多数据源连接和管理
  - 数据库元数据自动采集和同步
  - 表结构和字段信息查询
  - 数据血缘关系分析
  - 数据资产目录构建
- **用户痛点**: 
  - 数据源分散，缺乏统一管理
  - 元数据采集手工操作，效率低下
  - 数据结构变更难以及时感知
  - 数据血缘关系复杂，难以追踪

### 成功指标
- **北极星指标**: 元数据覆盖率 >90%
- **关键结果**: 
  - 数据源连接成功率 >99%
  - 元数据采集准确率 >95%
  - 血缘分析响应时间 <2秒
- **验证假设**: 
  - 自动化元数据采集能显著提升数据治理效率
  - 知识图谱能有效支持数据血缘分析

## 🔧 技术架构 (Technical Architecture)

### 技术栈选择
```json
{
  "framework": {
    "core": "Spring Boot 2.7.18",
    "data_access": "MyBatis Plus 3.5.10.1",
    "database_driver": "Dynamic DataSource"
  },
  "database": {
    "primary": "MySQL 8.0",
    "graph": "Neo4j 4.0+",
    "cache": "Redis 6.0"
  },
  "data_integration": {
    "jdbc_drivers": ["MySQL", "Oracle", "PostgreSQL", "SQL Server"],
    "connection_pool": "HikariCP",
    "metadata_extraction": "JDBC DatabaseMetaData"
  },
  "graph_processing": {
    "graph_database": "Neo4j",
    "graph_query": "Cypher",
    "visualization": "Neo4j Browser"
  }
}
```

### 架构约束
- **性能要求**: 元数据查询响应时间 <500ms, 支持100+数据源连接
- **安全要求**: 数据源连接信息加密存储，访问权限控制
- **可扩展性**: 支持新数据库类型扩展，支持大规模元数据存储
- **可用性**: 99.5%服务可用性，支持元数据增量同步

## 📦 功能需求矩阵 (Feature Requirements Matrix)

### MVP版本 (P0 - 核心功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| DM001 | 数据源连接管理 | 作为数据工程师，我希望管理数据库连接，以便统一访问数据源 | M | 3d | - |
| DM002 | 连接测试验证 | 作为数据工程师，我希望测试数据库连接，以便确保连接有效性 | S | 1d | DM001 |
| DM003 | 元数据自动采集 | 作为数据工程师，我希望自动采集元数据，以便构建数据目录 | L | 5d | DM001 |
| DM004 | 表结构管理 | 作为数据分析师，我希望查询表结构信息，以便理解数据模型 | M | 3d | DM003 |
| DM005 | 字段信息管理 | 作为数据分析师，我希望查询字段详细信息，以便准确使用数据 | M | 2d | DM004 |

### 增强版本 (P1 - 重要功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| DM006 | Neo4j图谱同步 | 作为数据治理专员，我希望构建知识图谱，以便分析数据关系 | L | 4d | DM005 |
| DM007 | 增量同步机制 | 作为数据工程师，我希望增量同步元数据，以便提高同步效率 | M | 3d | DM003 |
| DM008 | 数据血缘分析 | 作为数据分析师，我希望分析数据血缘，以便追踪数据来源 | L | 5d | DM006 |
| DM009 | 元数据搜索 | 作为用户，我希望搜索元数据，以便快速找到需要的数据 | M | 2d | DM005 |

### 完整版本 (P2 - 增值功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| DM010 | 元数据版本管理 | 作为数据治理专员，我希望管理元数据版本，以便追踪变更历史 | M | 3d | DM005 |
| DM011 | 数据质量评估 | 作为数据质量专员，我希望评估数据质量，以便改进数据管理 | L | 4d | DM005 |
| DM012 | 可视化图谱展示 | 作为用户，我希望可视化查看数据关系，以便直观理解数据结构 | M | 3d | DM008 |

**复杂度说明**: S(Simple, 1-2天) | M(Medium, 3-5天) | L(Large, 6-10天) | XL(Extra Large, >10天)

## 📋 功能详细规格 (Detailed Specifications)

### 数据源连接管理 - DM001
```yaml
feature_id: "DM001"
feature_name: "数据源连接管理"
priority: "P0"
complexity: "M"
estimated_effort: "3d"
dependencies: []

description: |
  提供多种数据库类型的连接管理功能，支持连接信息的安全存储、
  连接池管理和动态数据源切换。

technical_specs:
  supported_databases: ["MySQL", "Oracle", "PostgreSQL", "SQL Server", "MariaDB"]
  connection_pool: "HikariCP"
  encryption: "AES-256 for sensitive data"
  dynamic_datasource: "Spring Boot Dynamic DataSource"
  
  api_endpoints:
    - method: "POST"
      path: "/admin-api/data-meta/database/create"
      description: "创建数据源连接"
      request_body: |
        {
          "name": "string",
          "type": "string",
          "host": "string",
          "port": "integer",
          "database": "string",
          "username": "string",
          "password": "string",
          "properties": "object"
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "id": "long",
            "name": "string",
            "status": "string"
          }
        }
    - method: "GET"
      path: "/admin-api/data-meta/database/page"
      description: "分页查询数据源"
    - method: "PUT"
      path: "/admin-api/data-meta/database/update"
      description: "更新数据源信息"
    - method: "DELETE"
      path: "/admin-api/data-meta/database/delete"
      description: "删除数据源"

business_logic:
  - step: "连接信息验证"
    description: "验证数据库连接参数的有效性"
  - step: "敏感信息加密"
    description: "对密码等敏感信息进行AES加密存储"
  - step: "连接池配置"
    description: "配置数据库连接池参数"
  - step: "动态数据源注册"
    description: "将数据源注册到动态数据源管理器"

acceptance_criteria:
  - criterion: "支持主流数据库类型的连接"
    test_method: "兼容性测试"
  - criterion: "连接信息安全存储，密码加密"
    test_method: "安全测试"
  - criterion: "连接池正常工作，支持并发访问"
    test_method: "性能测试"
  - criterion: "动态数据源切换功能正常"
    test_method: "功能测试"

implementation_hints:
  code_generation_prompt: |
    生成数据源连接管理系统，要求：
    1. 使用Spring Boot + MyBatis Plus
    2. 集成Dynamic DataSource
    3. 支持多种数据库类型
    4. 实现连接信息加密存储
    5. 配置HikariCP连接池
    6. 添加连接状态监控
    7. 包含完整的CRUD操作
    8. 包含单元测试和集成测试
  
  key_considerations:
    - "数据库密码使用AES加密存储"
    - "连接池参数根据数据库类型优化"
    - "支持连接超时和重试机制"
    - "记录连接操作审计日志"
    - "支持连接池监控和告警"
```

### 元数据自动采集 - DM003
```yaml
feature_id: "DM003"
feature_name: "元数据自动采集"
priority: "P0"
complexity: "L"
estimated_effort: "5d"
dependencies: ["DM001"]

description: |
  基于JDBC DatabaseMetaData API自动采集数据库元数据信息，
  包括表结构、字段信息、索引、约束等，支持定时和手动触发。

technical_specs:
  metadata_extraction: "JDBC DatabaseMetaData API"
  scheduling: "Spring @Scheduled + Quartz"
  batch_processing: "Spring Batch"
  incremental_sync: "基于时间戳的增量同步"
  
  api_endpoints:
    - method: "POST"
      path: "/admin-api/data-meta/collection/start"
      description: "启动元数据采集"
      request_body: |
        {
          "databaseId": "long",
          "collectionType": "string", // FULL, INCREMENTAL
          "scheduleConfig": "object"
        }
    - method: "GET"
      path: "/admin-api/data-meta/collection/status/{taskId}"
      description: "查询采集任务状态"
    - method: "GET"
      path: "/admin-api/data-meta/tables/page"
      description: "分页查询表信息"
    - method: "GET"
      path: "/admin-api/data-meta/columns/list"
      description: "查询字段信息"

business_logic:
  - step: "数据源连接"
    description: "建立到目标数据库的连接"
  - step: "元数据提取"
    description: "使用JDBC API提取表、字段、索引等信息"
  - step: "数据清洗"
    description: "清洗和标准化元数据信息"
  - step: "增量对比"
    description: "与现有元数据对比，识别变更"
  - step: "数据存储"
    description: "将元数据信息存储到MySQL"
  - step: "状态更新"
    description: "更新采集任务状态和统计信息"

acceptance_criteria:
  - criterion: "能够采集主流数据库的完整元数据"
    test_method: "功能测试"
  - criterion: "增量同步功能正常，避免重复采集"
    test_method: "功能测试"
  - criterion: "采集任务支持定时调度"
    test_method: "集成测试"
  - criterion: "采集过程可监控，支持进度查询"
    test_method: "功能测试"
  - criterion: "采集性能满足要求，大表采集不超时"
    test_method: "性能测试"

implementation_hints:
  code_generation_prompt: |
    生成元数据自动采集系统，要求：
    1. 使用JDBC DatabaseMetaData API
    2. 支持多种数据库类型的元数据采集
    3. 实现增量同步机制
    4. 集成Spring Batch进行批处理
    5. 支持定时调度和手动触发
    6. 添加采集进度监控
    7. 实现异常处理和重试机制
    8. 包含完整的测试用例
  
  key_considerations:
    - "大表采集的性能优化"
    - "采集过程的异常处理和恢复"
    - "元数据变更的检测和通知"
    - "采集任务的并发控制"
    - "采集结果的数据质量检查"
```

## 📊 数据模型 (Data Models)

### 核心实体定义
```typescript
// 数据库实体
interface MetaDatabase {
  id: number;
  name: string;
  type: string; // MYSQL, ORACLE, POSTGRESQL等
  host: string;
  port: number;
  database: string;
  username: string;
  password: string; // 加密存储
  properties: object;
  status: number; // 0-正常 1-异常
  lastSyncTime: Date;
  createTime: Date;
  updateTime: Date;
}

// 表实体
interface MetaTable {
  id: number;
  dbId: number;
  instanceId: string;
  tableName: string;
  tableComment: string;
  tableOwner: string;
  tableRows: number;
  dataLength: number;
  engine: string;
  partitionNum: number;
  status: number;
  createTime: Date;
  updateTime: Date;
}

// 字段实体
interface MetaColumn {
  id: number;
  tableId: number;
  columnName: string;
  columnComment: string;
  dataType: string;
  columnLength: number;
  columnScale: number;
  nullable: boolean;
  defaultValue: string;
  isPrimaryKey: boolean;
  isIndex: boolean;
  ordinalPosition: number;
  createTime: Date;
  updateTime: Date;
}

// 索引实体
interface MetaIndex {
  id: number;
  tableId: number;
  indexName: string;
  indexType: string;
  isUnique: boolean;
  columns: string[];
  createTime: Date;
  updateTime: Date;
}

// 采集任务实体
interface CollectionTask {
  id: number;
  databaseId: number;
  taskName: string;
  collectionType: string; // FULL, INCREMENTAL
  status: string; // RUNNING, SUCCESS, FAILED
  startTime: Date;
  endTime: Date;
  totalTables: number;
  processedTables: number;
  errorMessage: string;
  createTime: Date;
  updateTime: Date;
}
```

### API设计规范
```yaml
# RESTful API 设计标准
api_base_url: "http://localhost:8081"
authentication: "Bearer Token (JWT)"
rate_limiting: "200 requests/minute per user"

# 统一响应格式
response_format:
  success: |
    {
      "code": 0,
      "data": {...},
      "msg": "操作成功"
    }
  error: |
    {
      "code": 500,
      "data": null,
      "msg": "操作失败"
    }

# 核心API端点
api_endpoints:
  database_management:
    - "GET /admin-api/data-meta/database/page"
    - "POST /admin-api/data-meta/database/create"
    - "PUT /admin-api/data-meta/database/update"
    - "DELETE /admin-api/data-meta/database/delete"
    - "POST /admin-api/data-meta/database/{id}/test-connection"

  metadata_collection:
    - "POST /admin-api/data-meta/collection/start"
    - "GET /admin-api/data-meta/collection/status/{taskId}"
    - "GET /admin-api/data-meta/collection/history"
    - "POST /admin-api/data-meta/collection/stop/{taskId}"

  metadata_query:
    - "GET /admin-api/data-meta/tables/page"
    - "GET /admin-api/data-meta/tables/{id}"
    - "GET /admin-api/data-meta/columns/list"
    - "GET /admin-api/data-meta/indexes/list"
    - "POST /admin-api/data-meta/search"

  graph_operations:
    - "POST /admin-api/data-meta/graph/sync"
    - "GET /admin-api/data-meta/graph/lineage"
    - "GET /admin-api/data-meta/graph/relationships"
```

## 🗓️ 实施路线图 (Implementation Roadmap)

### 迭代计划
```yaml
sprint_1:
  duration: "2 weeks"
  goal: "数据源管理和连接"
  deliverables:
    - feature_id: "DM001"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "数据源连接管理"
    - feature_id: "DM002"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "连接测试验证"
    - database_setup: "完成"
      assignee: "DBA团队"
      description: "元数据存储表结构设计"

sprint_2:
  duration: "2 weeks"
  goal: "元数据采集核心功能"
  deliverables:
    - feature_id: "DM003"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "元数据自动采集"
    - feature_id: "DM004"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "表结构管理"
    - feature_id: "DM005"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "字段信息管理"

sprint_3:
  duration: "2 weeks"
  goal: "图谱构建和增强功能"
  deliverables:
    - feature_id: "DM006"
      status: "必须完成"
      assignee: "数据团队"
      description: "Neo4j图谱同步"
    - feature_id: "DM007"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "增量同步机制"
    - feature_id: "DM008"
      status: "必须完成"
      assignee: "数据团队"
      description: "数据血缘分析"

# 里程碑检查点
milestones:
  basic_functionality:
    date: "2025-02-15"
    criteria: "完成基础的数据源管理和元数据采集"
    deliverables: ["数据源管理", "连接测试", "元数据采集", "表结构管理"]

  advanced_features:
    date: "2025-03-01"
    criteria: "完成图谱构建和血缘分析功能"
    deliverables: ["Neo4j同步", "增量同步", "血缘分析", "元数据搜索"]

  production_ready:
    date: "2025-03-15"
    criteria: "完成所有功能，通过性能和安全测试"
    deliverables: ["版本管理", "质量评估", "可视化展示", "性能优化"]
```

### 质量保证
- **测试覆盖率**: 单元测试 >80%, 集成测试 >70%
- **性能基准**: 元数据查询 <500ms, 采集任务支持1000+表
- **安全检查**: 连接信息加密、访问权限控制
- **兼容性**: 支持主流数据库类型，版本兼容性测试

## 🤖 AI协作配置 (AI Collaboration Config)

### 代码生成上下文
```yaml
project_context:
  tech_stack: "Spring Boot + MyBatis Plus + Neo4j + Dynamic DataSource"
  coding_style: "阿里巴巴Java开发规范"
  project_structure: |
    datamind-server-data-meta/
    ├── src/main/java/com/data/platform/datamind/server/datameta/
    │   ├── controller/     # REST控制器
    │   ├── service/        # 业务逻辑层
    │   ├── dal/           # 数据访问层
    │   ├── collector/     # 元数据采集器
    │   └── graph/         # 图谱操作

code_generation_templates:
  collector_prompt: |
    生成元数据采集器，要求：
    1. 使用JDBC DatabaseMetaData API
    2. 支持多种数据库类型
    3. 实现增量同步机制
    4. 包含异常处理和重试
    5. 支持采集进度监控
    6. 添加性能优化

  graph_prompt: |
    生成Neo4j图谱操作，要求：
    1. 使用Spring Data Neo4j
    2. 实现元数据到图谱的映射
    3. 支持血缘关系查询
    4. 包含Cypher查询优化
    5. 添加图谱可视化接口
```

## 💡 质量保证清单

- [x] 所有P0功能都有明确的验收标准
- [x] 技术规格可以直接用于代码生成
- [x] API设计符合RESTful规范
- [x] 数据模型定义完整
- [x] 实施计划具有可执行性
- [x] AI协作配置完整可用

## 📚 附录

### 相关文档链接
- [服务详细指南](./GUIDE.md)
- [数据库设计](../design/data-meta/database.sql)
- [API接口文档](http://localhost:8081/doc.html)

### 风险评估
- **技术风险**: 大规模元数据采集的性能问题
- **兼容性风险**: 不同数据库版本的兼容性
- **数据风险**: 元数据同步的一致性保证

---

**注意**: 本PRD文档专门针对DataMind Data-Meta元数据服务设计，作为数据治理的基础服务，需要确保元数据的准确性和完整性。
