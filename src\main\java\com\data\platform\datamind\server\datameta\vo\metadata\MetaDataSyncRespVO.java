package com.data.platform.datamind.server.datameta.vo.metadata;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 元数据同步响应 VO
 */
@Schema(description = "Web端 - 元数据同步 Response VO")
@Data
public class MetaDataSyncRespVO {

    @Schema(description = "同步任务ID", example = "sync_20241224_001")
    private String syncTaskId;

    @Schema(description = "同步状态", example = "SUCCESS")
    private SyncStatus status;

    @Schema(description = "同步开始时间")
    private LocalDateTime startTime;

    @Schema(description = "同步结束时间")
    private LocalDateTime endTime;

    @Schema(description = "同步耗时（毫秒）", example = "5000")
    private Long duration;

    @Schema(description = "同步的数据库数量", example = "3")
    private Integer databaseCount;

    @Schema(description = "同步的表数量", example = "150")
    private Integer tableCount;

    @Schema(description = "同步的列数量", example = "2500")
    private Integer columnCount;

    @Schema(description = "同步详情")
    private List<SyncDetail> details;

    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        /**
         * 同步中
         */
        RUNNING,
        /**
         * 成功
         */
        SUCCESS,
        /**
         * 失败
         */
        FAILED,
        /**
         * 部分成功
         */
        PARTIAL_SUCCESS
    }

    /**
     * 同步详情
     */
    @Data
    @Schema(description = "同步详情")
    public static class SyncDetail {
        @Schema(description = "数据库ID", example = "1")
        private Long databaseId;

        @Schema(description = "数据库名称", example = "user_center")
        private String databaseName;

        @Schema(description = "同步状态", example = "SUCCESS")
        private SyncStatus status;

        @Schema(description = "同步的表数量", example = "50")
        private Integer tableCount;

        @Schema(description = "同步的列数量", example = "800")
        private Integer columnCount;

        @Schema(description = "错误信息")
        private String errorMessage;
    }
}
