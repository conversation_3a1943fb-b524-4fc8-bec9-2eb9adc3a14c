package com.data.platform.datamind.server.datameta.vo.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据质量报告响应 VO
 */
@Schema(description = "Web端 - 数据质量报告 Response VO")
@Data
public class DataQualityReportRespVO {

    @Schema(description = "报告生成时间")
    private LocalDateTime reportTime;

    @Schema(description = "总体质量评分（0-100）", example = "85.5")
    private Double overallScore;

    @Schema(description = "质量等级", example = "GOOD")
    private QualityLevel qualityLevel;

    @Schema(description = "数据库质量统计")
    private List<DatabaseQuality> databaseQualities;

    @Schema(description = "质量问题汇总")
    private QualityIssueSummary issueSummary;

    @Schema(description = "质量趋势（最近7天）")
    private List<QualityTrend> qualityTrends;

    /**
     * 质量等级枚举
     */
    public enum QualityLevel {
        /**
         * 优秀 (90-100)
         */
        EXCELLENT,
        /**
         * 良好 (70-89)
         */
        GOOD,
        /**
         * 一般 (50-69)
         */
        FAIR,
        /**
         * 较差 (30-49)
         */
        POOR,
        /**
         * 很差 (0-29)
         */
        VERY_POOR
    }

    /**
     * 数据库质量
     */
    @Data
    @Schema(description = "数据库质量")
    public static class DatabaseQuality {
        @Schema(description = "数据库ID", example = "1")
        private Long databaseId;

        @Schema(description = "数据库名称", example = "user_center")
        private String databaseName;

        @Schema(description = "质量评分", example = "88.0")
        private Double qualityScore;

        @Schema(description = "质量等级", example = "GOOD")
        private QualityLevel qualityLevel;

        @Schema(description = "表数量", example = "50")
        private Integer tableCount;

        @Schema(description = "列数量", example = "800")
        private Integer columnCount;

        @Schema(description = "问题数量", example = "5")
        private Integer issueCount;

        @Schema(description = "质量指标详情")
        private Map<String, Object> qualityMetrics;
    }

    /**
     * 质量问题汇总
     */
    @Data
    @Schema(description = "质量问题汇总")
    public static class QualityIssueSummary {
        @Schema(description = "总问题数", example = "25")
        private Integer totalIssues;

        @Schema(description = "高优先级问题数", example = "3")
        private Integer highPriorityIssues;

        @Schema(description = "中优先级问题数", example = "8")
        private Integer mediumPriorityIssues;

        @Schema(description = "低优先级问题数", example = "14")
        private Integer lowPriorityIssues;

        @Schema(description = "问题类型分布")
        private Map<String, Integer> issueTypeDistribution;
    }

    /**
     * 质量趋势
     */
    @Data
    @Schema(description = "质量趋势")
    public static class QualityTrend {
        @Schema(description = "日期")
        private LocalDateTime date;

        @Schema(description = "质量评分", example = "85.5")
        private Double qualityScore;

        @Schema(description = "问题数量", example = "25")
        private Integer issueCount;
    }
}
