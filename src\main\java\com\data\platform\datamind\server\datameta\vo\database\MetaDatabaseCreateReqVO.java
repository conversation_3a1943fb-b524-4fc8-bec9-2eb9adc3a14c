package com.data.platform.datamind.server.datameta.vo.database;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
 * 数据库/数据源元数据创建 Request VO
 */
@Schema(description = "管理后台 - 数据库/数据源元数据创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaDatabaseCreateReqVO extends MetaDatabaseBaseVO {

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "pwd123")
    @NotEmpty(message = "密码不能为空")
    private String password; // 创建时需要密码
}
