package com.data.platform.datamind.server.datameta.vo.database;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:32
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
 * 数据库/数据源元数据更新 Request VO
 */
@Schema(description = "管理后台 - 数据库/数据源元数据更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaDatabaseUpdateReqVO extends MetaDatabaseBaseVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @Schema(description = "密码", example = "new_pwd456")
    private String password; // 更新时密码可选，如果传入则更新
}
