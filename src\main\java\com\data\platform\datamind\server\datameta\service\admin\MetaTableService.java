package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTablePageReqVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableUpdateReqVO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 表元数据 Service 接口
 *
 * <AUTHOR>
 */
public interface MetaTableService {

    Long createTable(@Valid MetaTableCreateReqVO createReqVO);

    void updateTable(@Valid MetaTableUpdateReqVO updateReqVO);

    void deleteTable(Long id);

    MetaTableDO getTable(Long id);

    List<MetaTableDO> getTableList(Collection<Long> ids);

    PageResult<MetaTableDO> getTablePage(MetaTablePageReqVO pageReqVO);

    List<MetaTableDO> getTablesByDbId(Long dbId);

    MetaTableDO getTableByDbIdAndTableName(Long dbId, String tableName);

    /**
     * 批量插入表元数据
     * @param tables 表列表
     */
    void batchInsert(List<MetaTableDO> tables);

    /**
     * 批量更新表元数据
     * @param tables 表列表
     */
    void batchUpdate(List<MetaTableDO> tables);

    /**
     * 根据数据库ID和表名删除旧的表数据 (用于同步时清理)
     * @param dbId 数据库ID
     * @param tableNames 要删除的表名列表
     */
    void deleteTablesByDbIdAndTableNames(Long dbId, List<String> tableNames);

    /**
     * 获取所有表数据（不分页）
     * @return 所有表DO列表
     */
    List<MetaTableDO> getAllTables();
}
