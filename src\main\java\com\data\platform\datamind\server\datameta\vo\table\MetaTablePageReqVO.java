package com.data.platform.datamind.server.datameta.vo.table;

import com.data.platform.datamind.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.data.platform.datamind.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 表元数据分页 Request VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:52
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo.table
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
@Schema(description = "管理后台 - 表元数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaTablePageReqVO extends PageParam {

    @Schema(description = "数据库ID", example = "1")
    private Long dbId;

    @Schema(description = "表名称", example = "user_info")
    private String tableName;

    @Schema(description = "表注释", example = "用户信息")
    private String tableComment;

    @Schema(description = "存储引擎", example = "InnoDB")
    private String engine;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
