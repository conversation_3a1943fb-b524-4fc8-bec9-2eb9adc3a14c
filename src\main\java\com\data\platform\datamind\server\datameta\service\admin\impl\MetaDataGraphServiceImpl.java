package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.framework.common.exception.util.ServiceExceptionUtil;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.infrans.enumeration.MetaDataErrorCodeConstants;
import com.data.platform.datamind.server.datameta.service.admin.MetaDataGraphService;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 元数据知识图谱 Service 实现类
 * 负责将关系型元数据同步到 Neo4j 图谱，并提供图谱查询能力
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetaDataGraphServiceImpl implements MetaDataGraphService {

    @Resource
    private Neo4jTemplate neo4jTemplate;

    @Resource
    private Neo4jClient neo4jClient; // 将 Neo4jTemplate 替换为 Neo4jClient

    @Resource
    private com.data.platform.datamind.server.datameta.graph.GraphSyncService graphSyncService;

    @Resource
    private com.data.platform.datamind.server.datameta.graph.GraphQueryService graphQueryService;

    @Override
    @Transactional(rollbackFor = Exception.class) // 确保Neo4j操作在事务中
    public void syncAllMetaDataToGraph(
            List<MetaDatabaseDO> databases,
            List<MetaTableDO> tables,
            List<MetaColumnDO> columns) {
        // 使用新的GraphSyncService进行同步
        graphSyncService.syncAllMetaDataToGraph(databases, tables, columns);
    }

    // 保留原有的同步实现作为备用
    private void syncAllMetaDataToGraphLegacy(
            List<MetaDatabaseDO> databases,
            List<MetaTableDO> tables,
            List<MetaColumnDO> columns) {
        try {
            // 1. 同步 Database 节点 - 增强RAG友好的属性
            for (MetaDatabaseDO db : databases) {
                Map<String, Object> params = new HashMap<>();
                params.put("id", db.getId());
                params.put("name", db.getName());
                params.put("type", db.getType());
                params.put("jdbcUrl", db.getAccessAddress());
                params.put("instanceName", db.getInstanceName());
                params.put("description", db.getDescription());
                params.put("host", db.getHost());
                params.put("port", db.getPort());
                params.put("regionFlag", db.getRegionFlag());
                params.put("status", db.getStatus());

                // 为RAG检索构建综合描述文本
                StringBuilder ragText = new StringBuilder();
                ragText.append("数据库名称: ").append(db.getName());
                if (db.getInstanceName() != null) {
                    ragText.append(", 实例名称: ").append(db.getInstanceName());
                }
                if (db.getDescription() != null) {
                    ragText.append(", 描述: ").append(db.getDescription());
                }
                ragText.append(", 数据库类型: ").append(db.getType());
                if (db.getRegionFlag() != null) {
                    ragText.append(", 所属区域: ").append(db.getRegionFlag());
                }
                params.put("ragText", ragText.toString());

                neo4jClient.query("MERGE (d:Database {id: $id}) " +
                                "ON CREATE SET d.name = $name, d.type = $type, d.jdbcUrl = $jdbcUrl, " +
                                "d.instanceName = $instanceName, d.description = $description, " +
                                "d.host = $host, d.port = $port, d.regionFlag = $regionFlag, " +
                                "d.status = $status, d.ragText = $ragText " +
                                "ON MATCH SET d.name = $name, d.type = $type, d.jdbcUrl = $jdbcUrl, " +
                                "d.instanceName = $instanceName, d.description = $description, " +
                                "d.host = $host, d.port = $port, d.regionFlag = $regionFlag, " +
                                "d.status = $status, d.ragText = $ragText")
                        .bindAll(params)
                        .run(); // 使用 run() 执行写操作
            }
            log.info("[MetaDataGraphService] Synced {} Database nodes.", databases.size());

            // 2. 同步 Table 节点及 CONTAINS_TABLE 关系 - 增强RAG友好的属性
            for (MetaTableDO table : tables) {
                Map<String, Object> params = new HashMap<>();
                params.put("dbId", table.getDbId());
                params.put("tableId", table.getId());
                params.put("tableName", table.getTableName());
                params.put("tableComment", table.getTableComment());
                params.put("tableOwner", table.getTableOwner());
                params.put("tableRows", table.getTableRows());
                params.put("dataLength", table.getDataLength());
                params.put("engine", table.getEngine());
                params.put("charset", table.getCharset());
                params.put("dbType", table.getDbType());

                // 为RAG检索构建综合描述文本
                StringBuilder ragText = new StringBuilder();
                ragText.append("表名: ").append(table.getTableName());
                if (table.getTableComment() != null && !table.getTableComment().trim().isEmpty()) {
                    ragText.append(", 表注释: ").append(table.getTableComment());
                }
                if (table.getTableOwner() != null) {
                    ragText.append(", 表所有者: ").append(table.getTableOwner());
                }
                if (table.getEngine() != null) {
                    ragText.append(", 存储引擎: ").append(table.getEngine());
                }
                if (table.getTableRows() != null && table.getTableRows() > 0) {
                    ragText.append(", 数据行数: ").append(table.getTableRows());
                }
                if (table.getDataLength() != null && table.getDataLength() > 0) {
                    ragText.append(", 数据大小: ").append(formatDataSize(table.getDataLength()));
                }
                params.put("ragText", ragText.toString());

                neo4jClient.query("MATCH (d:Database {id: $dbId}) " +
                                "MERGE (t:Table {id: $tableId}) " +
                                "ON CREATE SET t.name = $tableName, t.comment = $tableComment, t.dbId = $dbId, " +
                                "t.owner = $tableOwner, t.rows = $tableRows, t.dataLength = $dataLength, " +
                                "t.engine = $engine, t.charset = $charset, t.dbType = $dbType, t.ragText = $ragText " +
                                "ON MATCH SET t.name = $tableName, t.comment = $tableComment, t.dbId = $dbId, " +
                                "t.owner = $tableOwner, t.rows = $tableRows, t.dataLength = $dataLength, " +
                                "t.engine = $engine, t.charset = $charset, t.dbType = $dbType, t.ragText = $ragText " +
                                "MERGE (d)-[:CONTAINS_TABLE]->(t)")
                        .bindAll(params)
                        .run();
            }
            log.info("[MetaDataGraphService] Synced {} Table nodes and relationships.", tables.size());


            // 3. 同步 Column 节点及 HAS_COLUMN 关系 - 增强RAG友好的属性
            for (MetaColumnDO column : columns) {
                Map<String, Object> params = new HashMap<>();
                params.put("tableId", column.getTableId());
                params.put("columnId", column.getId());
                params.put("columnName", column.getColumnName());
                params.put("columnComment", column.getColumnComment());
                params.put("columnType", column.getColumnType());
                params.put("dataType", column.getDataType());
                params.put("columnKey", column.getColumnKey());
                params.put("isNullable", column.getIsNullable());
                params.put("columnDefault", column.getColumnDefault());
                params.put("extra", column.getExtra());
                params.put("isPrimaryKey", column.getIsPrimaryKey());
                params.put("isForeignKey", column.getIsForeignKey());
                params.put("tableName", column.getTableName());
                params.put("tableOwner", column.getTableOwner());
                params.put("distinctNum", column.getDistinctNum());

                // 为RAG检索构建综合描述文本
                StringBuilder ragText = new StringBuilder();
                ragText.append("列名: ").append(column.getColumnName());
                if (column.getColumnComment() != null && !column.getColumnComment().trim().isEmpty()) {
                    ragText.append(", 列注释: ").append(column.getColumnComment());
                }
                ragText.append(", 数据类型: ").append(column.getColumnType());
                if (column.getDataType() != null) {
                    ragText.append(", 完整类型: ").append(column.getDataType());
                }
                if (Boolean.TRUE.equals(column.getIsPrimaryKey())) {
                    ragText.append(", 主键");
                }
                if (Boolean.TRUE.equals(column.getIsForeignKey())) {
                    ragText.append(", 外键");
                }
                if (Boolean.FALSE.equals(column.getIsNullable())) {
                    ragText.append(", 不允许为空");
                } else {
                    ragText.append(", 允许为空");
                }
                if (column.getColumnDefault() != null) {
                    ragText.append(", 默认值: ").append(column.getColumnDefault());
                }
                if (column.getExtra() != null && !column.getExtra().trim().isEmpty()) {
                    ragText.append(", 额外属性: ").append(column.getExtra());
                }
                if (column.getTableName() != null) {
                    ragText.append(", 所属表: ").append(column.getTableName());
                }
                params.put("ragText", ragText.toString());

                neo4jClient.query("MATCH (t:Table {id: $tableId}) " +
                                "MERGE (c:Column {id: $columnId}) " +
                                "ON CREATE SET c.name = $columnName, c.comment = $columnComment, c.type = $columnType, " +
                                "c.dataType = $dataType, c.columnKey = $columnKey, c.isNullable = $isNullable, " +
                                "c.columnDefault = $columnDefault, c.extra = $extra, c.isPrimaryKey = $isPrimaryKey, " +
                                "c.isForeignKey = $isForeignKey, c.tableId = $tableId, c.tableName = $tableName, " +
                                "c.tableOwner = $tableOwner, c.distinctNum = $distinctNum, c.ragText = $ragText " +
                                "ON MATCH SET c.name = $columnName, c.comment = $columnComment, c.type = $columnType, " +
                                "c.dataType = $dataType, c.columnKey = $columnKey, c.isNullable = $isNullable, " +
                                "c.columnDefault = $columnDefault, c.extra = $extra, c.isPrimaryKey = $isPrimaryKey, " +
                                "c.isForeignKey = $isForeignKey, c.tableId = $tableId, c.tableName = $tableName, " +
                                "c.tableOwner = $tableOwner, c.distinctNum = $distinctNum, c.ragText = $ragText " +
                                "MERGE (t)-[:HAS_COLUMN]->(c)")
                        .bindAll(params)
                        .run();
            }
            log.info("[MetaDataGraphService] Synced {} Column nodes and relationships.", columns.size());


            // 4. 同步 REFERENCES (外键) 关系
            // 先清理旧的外键关系，以防MySQL中外键关系变更或删除，Neo4j未能及时更新
            neo4jClient.query("MATCH ()-[r:REFERENCES]->() DELETE r").run();
            log.info("[MetaDataGraphService] Cleared old REFERENCES relationships.");

            for (MetaColumnDO column : columns) {
                if (Boolean.TRUE.equals(column.getIsForeignKey()) && column.getFkTableId() != null && column.getFkColumnId() != null) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("sourceColumnId", column.getId());
                    params.put("targetColumnId", column.getFkColumnId());
                    neo4jClient.query("MATCH (source_col:Column {id: $sourceColumnId}), (target_col:Column {id: $targetColumnId}) " +
                                    "MERGE (source_col)-[:REFERENCES]->(target_col)")
                            .bindAll(params)
                            .run();
                }
            }
            log.info("[MetaDataGraphService] Synced foreign key (REFERENCES) relationships.");

        } catch (Exception e) {
            log.error("[MetaDataGraphService] Failed to sync meta data to Neo4j graph.", e);
            throw ServiceExceptionUtil.exception(MetaDataErrorCodeConstants.METADATA_SYNC_GRAPH_FAILED, e.getMessage());
        }
    }
    @Override
    public Map<String, Object> getTableGraph(Long tableId) {
        return graphQueryService.getTableGraph(tableId);
    }

    @Override
    public List<com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO> getColumnLineage(Long columnId) {
        return graphQueryService.getColumnLineage(columnId);
    }

    @Override
    public List<com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO> getUpstreamTables(Long tableId, int depth) {
        return graphQueryService.getUpstreamTables(tableId, depth);
    }

    @Override
    public List<com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO> getDownstreamTables(Long tableId, int depth) {
        return graphQueryService.getDownstreamTables(tableId, depth);
    }

    @Override
    public Map<String, Object> getDatabaseTableOverview(Long databaseId) {
        return graphQueryService.getDatabaseTableOverview(databaseId);
    }

    @Override
    public List<Map<String, Object>> searchTables(String keyword, int limit) {
        return graphQueryService.searchTables(keyword, limit);
    }

    @Override
    public Map<String, Object> getTableImpactAnalysis(Long tableId) {
        return graphQueryService.getTableImpactAnalysis(tableId);
    }
    // TODO: 增加图谱查询方法，例如：
    // List<GraphNode> getTableGraph(Long tableId);
    // List<ColumnLineage> getColumnLineage(Long columnId);
}