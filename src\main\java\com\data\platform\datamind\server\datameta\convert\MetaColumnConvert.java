package com.data.platform.datamind.server.datameta.convert;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnRespVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 列元数据 Convert
 *
 */
@Mapper
public interface MetaColumnConvert {

    MetaColumnConvert INSTANCE = Mappers.getMapper(MetaColumnConvert.class);

    MetaColumnDO convert(MetaColumnCreateReqVO bean);

    MetaColumnDO convert(MetaColumnUpdateReqVO bean);

    MetaColumnRespVO convert(MetaColumnDO bean);

    List<MetaColumnRespVO> convertList(List<MetaColumnDO> list);

    PageResult<MetaColumnRespVO> convertPage(PageResult<MetaColumnDO> page);

}
