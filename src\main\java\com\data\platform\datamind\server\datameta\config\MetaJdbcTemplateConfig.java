//package com.data.platform.datamind.server.datameta.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.jdbc.core.JdbcTemplate;
//
//import javax.sql.DataSource;
//
///**
// * ?????? JDBC Template ??
// * ????????SQL???????????JdbcTemplate
// * ????????? Spring Boot ??? DataSource??????????????MySQL??????
// * ????????????????????????
// */
//@Configuration
//public class MetaJdbcTemplateConfig {
//
//    @Bean
//    public JdbcTemplate metaJdbcTemplate(DataSource dataSource) {
//        return new JdbcTemplate(dataSource);
//    }
//}