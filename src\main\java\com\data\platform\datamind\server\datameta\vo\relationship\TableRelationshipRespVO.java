package com.data.platform.datamind.server.datameta.vo.relationship;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 表关系分析响应 VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.vo.relationship
 * @description 表关系分析响应VO
 * @email <EMAIL>
 * @since 1.8
 */
@Schema(description = "表关系分析响应 VO")
@Data
public class TableRelationshipRespVO {

    @Schema(description = "表ID", example = "1")
    private Long tableId;

    @Schema(description = "表名", example = "user_info")
    private String tableName;

    @Schema(description = "数据库ID", example = "1")
    private Long databaseId;

    @Schema(description = "数据库名", example = "user_center")
    private String databaseName;

    @Schema(description = "父表列表（被引用的表）")
    private List<RelatedTableInfo> parentTables;

    @Schema(description = "子表列表（引用该表的表）")
    private List<RelatedTableInfo> childTables;

    @Schema(description = "关联表列表")
    private List<RelatedTableInfo> relatedTables;

    @Schema(description = "外键关系列表")
    private List<ForeignKeyRelation> foreignKeyRelations;

    @Schema(description = "关系统计信息")
    private RelationshipStatistics statistics;

    @Schema(description = "依赖关系分析")
    private Map<String, Object> dependencyAnalysis;

    @Schema(description = "影响范围分析")
    private Map<String, Object> impactAnalysis;

    /**
     * 关联表信息
     */
    @Data
    @Schema(description = "关联表信息")
    public static class RelatedTableInfo {
        @Schema(description = "表ID", example = "2")
        private Long tableId;

        @Schema(description = "表名", example = "user_profile")
        private String tableName;

        @Schema(description = "表注释", example = "用户档案表")
        private String tableComment;

        @Schema(description = "关系类型", example = "FOREIGN_KEY")
        private String relationType;

        @Schema(description = "关系强度", example = "STRONG")
        private String relationStrength;

        @Schema(description = "关联列数", example = "1")
        private Integer relationColumnCount;
    }

    /**
     * 外键关系
     */
    @Data
    @Schema(description = "外键关系")
    public static class ForeignKeyRelation {
        @Schema(description = "外键名", example = "fk_user_profile_user_id")
        private String foreignKeyName;

        @Schema(description = "源表ID", example = "2")
        private Long sourceTableId;

        @Schema(description = "源表名", example = "user_profile")
        private String sourceTableName;

        @Schema(description = "源列名", example = "user_id")
        private String sourceColumnName;

        @Schema(description = "目标表ID", example = "1")
        private Long targetTableId;

        @Schema(description = "目标表名", example = "user_info")
        private String targetTableName;

        @Schema(description = "目标列名", example = "id")
        private String targetColumnName;

        @Schema(description = "约束类型", example = "CASCADE")
        private String constraintType;
    }

    /**
     * 关系统计信息
     */
    @Data
    @Schema(description = "关系统计信息")
    public static class RelationshipStatistics {
        @Schema(description = "总关系数", example = "5")
        private Integer totalRelations;

        @Schema(description = "父表数量", example = "2")
        private Integer parentTableCount;

        @Schema(description = "子表数量", example = "3")
        private Integer childTableCount;

        @Schema(description = "外键数量", example = "4")
        private Integer foreignKeyCount;

        @Schema(description = "关系复杂度", example = "MEDIUM")
        private String complexityLevel;

        @Schema(description = "关系深度", example = "3")
        private Integer relationDepth;
    }
}
