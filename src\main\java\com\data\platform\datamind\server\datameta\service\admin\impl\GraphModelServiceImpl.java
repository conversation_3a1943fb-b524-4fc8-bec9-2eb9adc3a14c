package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.entity.dto.GraphNodeDTO;
import com.data.platform.datamind.server.datameta.entity.dto.GraphRelationshipDTO;
import com.data.platform.datamind.server.datameta.service.admin.GraphModelService;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import com.data.platform.datamind.server.datameta.service.admin.MetaDatabaseService;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 图谱模型服务实现类
 * 负责将关系型元数据转换为图谱模型
 *
 * <AUTHOR> Team
 */
@Service
@Validated
@Slf4j
public class GraphModelServiceImpl implements GraphModelService {

    @Resource
    private MetaDatabaseService metaDatabaseService;

    @Resource
    private MetaTableService metaTableService;

    @Resource
    private MetaColumnService metaColumnService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public GraphNodeDTO buildDatabaseNode(MetaDatabaseDO database) {
        if (database == null) {
            return null;
        }

        Map<String, Object> properties = new HashMap<>();
        properties.put("name", database.getName());
        properties.put("type", database.getType());
        properties.put("accessAddress", database.getAccessAddress());
        properties.put("instanceName", database.getInstanceName());
        properties.put("status", database.getStatus());
        properties.put("description", database.getDescription());
        properties.put("host", database.getHost());
        properties.put("port", database.getPort());
        properties.put("source", database.getSource());
        properties.put("regionFlag", database.getRegionFlag());

        return GraphNodeDTO.builder()
                .id(database.getId())
                .label("Database")
                .name(database.getName())
                .type("DATABASE")
                .properties(properties)
                .description(database.getDescription())
                .status(database.getStatus())
                .createTime(database.getCreateTime() != null ? database.getCreateTime().getTime() : null)
                .updateTime(database.getUpdateTime() != null ? database.getUpdateTime().getTime() : null)
                .build();
    }

    @Override
    public GraphNodeDTO buildTableNode(MetaTableDO table) {
        if (table == null) {
            return null;
        }

        Map<String, Object> properties = new HashMap<>();
        properties.put("tableName", table.getTableName());
        properties.put("tableComment", table.getTableComment());
        properties.put("tableOwner", table.getTableOwner());
        properties.put("tableRows", table.getTableRows());
        properties.put("dataLength", table.getDataLength());
        properties.put("engine", table.getEngine());
        properties.put("dbId", table.getDbId());
        properties.put("dbType", table.getDbType());
        properties.put("instanceId", table.getInstanceId());

        return GraphNodeDTO.builder()
                .id(table.getId())
                .label("Table")
                .name(table.getTableName())
                .type("TABLE")
                .properties(properties)
                .description(table.getTableComment())
                .status("ACTIVE")
                .createTime(table.getCreateTime() != null ? table.getCreateTime().getTime() : null)
                .updateTime(table.getUpdateTime() != null ? table.getUpdateTime().getTime() : null)
                .build();
    }

    @Override
    public GraphNodeDTO buildColumnNode(MetaColumnDO column) {
        if (column == null) {
            return null;
        }

        Map<String, Object> properties = new HashMap<>();
        properties.put("columnName", column.getColumnName());
        properties.put("columnComment", column.getColumnComment());
        properties.put("columnType", column.getColumnType());
        properties.put("columnLength", column.getColumnLength());
        properties.put("columnPrecision", column.getColumnPrecision());
        properties.put("columnScale", column.getColumnScale());
        properties.put("isNullable", column.getIsNullable());
        properties.put("columnDefault", column.getColumnDefault());
        properties.put("isPrimaryKey", column.getIsPrimaryKey());
        properties.put("isAutoIncrement", column.getIsAutoIncrement());
        properties.put("ordinalPosition", column.getOrdinalPosition());
        properties.put("tableId", column.getTableId());

        return GraphNodeDTO.builder()
                .id(column.getId())
                .label("Column")
                .name(column.getColumnName())
                .type("COLUMN")
                .properties(properties)
                .description(column.getColumnComment())
                .status("ACTIVE")
                .createTime(column.getCreateTime() != null ? column.getCreateTime().getTime() : null)
                .updateTime(column.getUpdateTime() != null ? column.getUpdateTime().getTime() : null)
                .build();
    }

    @Override
    public GraphRelationshipDTO buildDatabaseTableRelationship(Long databaseId, Long tableId) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("relationshipType", "CONTAINS");

        return GraphRelationshipDTO.builder()
                .sourceNodeId(databaseId)
                .targetNodeId(tableId)
                .type("CONTAINS")
                .name("包含")
                .direction("OUT")
                .properties(properties)
                .description("数据库包含表")
                .weight(1.0)
                .status("ACTIVE")
                .createTime(System.currentTimeMillis())
                .build();
    }

    @Override
    public GraphRelationshipDTO buildTableColumnRelationship(Long tableId, Long columnId) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("relationshipType", "HAS_COLUMN");

        return GraphRelationshipDTO.builder()
                .sourceNodeId(tableId)
                .targetNodeId(columnId)
                .type("HAS_COLUMN")
                .name("包含列")
                .direction("OUT")
                .properties(properties)
                .description("表包含列")
                .weight(1.0)
                .status("ACTIVE")
                .createTime(System.currentTimeMillis())
                .build();
    }

    @Override
    public GraphRelationshipDTO buildTableRelationship(Long sourceTableId, Long targetTableId, 
                                                      String relationshipType, Map<String, Object> properties) {
        if (properties == null) {
            properties = new HashMap<>();
        }
        properties.put("relationshipType", relationshipType);

        return GraphRelationshipDTO.builder()
                .sourceNodeId(sourceTableId)
                .targetNodeId(targetTableId)
                .type(relationshipType)
                .name(getRelationshipName(relationshipType))
                .direction("OUT")
                .properties(properties)
                .description(getRelationshipDescription(relationshipType))
                .weight(1.0)
                .status("ACTIVE")
                .createTime(System.currentTimeMillis())
                .build();
    }

    @Override
    public List<GraphNodeDTO> buildDatabaseNodes(List<MetaDatabaseDO> databases) {
        if (databases == null || databases.isEmpty()) {
            return Collections.emptyList();
        }

        return databases.stream()
                .map(this::buildDatabaseNode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<GraphNodeDTO> buildTableNodes(List<MetaTableDO> tables) {
        if (tables == null || tables.isEmpty()) {
            return Collections.emptyList();
        }

        return tables.stream()
                .map(this::buildTableNode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<GraphNodeDTO> buildColumnNodes(List<MetaColumnDO> columns) {
        if (columns == null || columns.isEmpty()) {
            return Collections.emptyList();
        }

        return columns.stream()
                .map(this::buildColumnNode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> buildCompleteGraphModel(List<MetaDatabaseDO> databases, 
                                                      List<MetaTableDO> tables, 
                                                      List<MetaColumnDO> columns) {
        Map<String, Object> graphModel = new HashMap<>();
        
        // 构建节点
        List<GraphNodeDTO> allNodes = new ArrayList<>();
        allNodes.addAll(buildDatabaseNodes(databases));
        allNodes.addAll(buildTableNodes(tables));
        allNodes.addAll(buildColumnNodes(columns));
        
        // 构建关系
        List<GraphRelationshipDTO> allRelationships = new ArrayList<>();
        
        // 数据库-表关系
        if (tables != null) {
            for (MetaTableDO table : tables) {
                if (table.getDbId() != null) {
                    allRelationships.add(buildDatabaseTableRelationship(table.getDbId(), table.getId()));
                }
            }
        }
        
        // 表-列关系
        if (columns != null) {
            for (MetaColumnDO column : columns) {
                if (column.getTableId() != null) {
                    allRelationships.add(buildTableColumnRelationship(column.getTableId(), column.getId()));
                }
            }
        }
        
        graphModel.put("nodes", allNodes);
        graphModel.put("relationships", allRelationships);
        graphModel.put("nodeCount", allNodes.size());
        graphModel.put("relationshipCount", allRelationships.size());
        graphModel.put("buildTime", System.currentTimeMillis());
        
        return graphModel;
    }

    @Override
    public Map<String, Object> buildDatabaseGraphModel(Long databaseId) {
        try {
            MetaDatabaseDO database = metaDatabaseService.getDatabase(databaseId);
            if (database == null) {
                return Collections.emptyMap();
            }

            List<MetaTableDO> tables = metaTableService.getTablesByDatabaseId(databaseId);
            List<MetaColumnDO> columns = new ArrayList<>();
            
            for (MetaTableDO table : tables) {
                List<MetaColumnDO> tableColumns = metaColumnService.getColumnsByTableId(table.getId());
                columns.addAll(tableColumns);
            }

            return buildCompleteGraphModel(Collections.singletonList(database), tables, columns);
        } catch (Exception e) {
            log.error("Error building database graph model for databaseId: {}", databaseId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> buildTableGraphModel(Long tableId) {
        try {
            MetaTableDO table = metaTableService.getTable(tableId);
            if (table == null) {
                return Collections.emptyMap();
            }

            MetaDatabaseDO database = metaDatabaseService.getDatabase(table.getDbId());
            List<MetaColumnDO> columns = metaColumnService.getColumnsByTableId(tableId);

            List<MetaDatabaseDO> databases = database != null ? 
                    Collections.singletonList(database) : Collections.emptyList();

            return buildCompleteGraphModel(databases, Collections.singletonList(table), columns);
        } catch (Exception e) {
            log.error("Error building table graph model for tableId: {}", tableId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> validateGraphModel(Map<String, Object> graphModel) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        if (graphModel == null || graphModel.isEmpty()) {
            errors.add("图谱模型为空");
            result.put("valid", false);
            result.put("errors", errors);
            return result;
        }

        // 验证节点
        @SuppressWarnings("unchecked")
        List<GraphNodeDTO> nodes = (List<GraphNodeDTO>) graphModel.get("nodes");
        if (nodes == null || nodes.isEmpty()) {
            warnings.add("图谱模型中没有节点");
        } else {
            for (GraphNodeDTO node : nodes) {
                if (node.getId() == null) {
                    errors.add("发现没有ID的节点");
                }
                if (node.getLabel() == null || node.getLabel().trim().isEmpty()) {
                    errors.add("发现没有标签的节点: " + node.getId());
                }
            }
        }

        // 验证关系
        @SuppressWarnings("unchecked")
        List<GraphRelationshipDTO> relationships = (List<GraphRelationshipDTO>) graphModel.get("relationships");
        if (relationships != null) {
            Set<Long> nodeIds = nodes != null ? 
                    nodes.stream().map(GraphNodeDTO::getId).collect(Collectors.toSet()) : 
                    Collections.emptySet();

            for (GraphRelationshipDTO rel : relationships) {
                if (rel.getSourceNodeId() == null || rel.getTargetNodeId() == null) {
                    errors.add("发现源节点或目标节点ID为空的关系");
                }
                if (!nodeIds.contains(rel.getSourceNodeId())) {
                    errors.add("关系引用了不存在的源节点: " + rel.getSourceNodeId());
                }
                if (!nodeIds.contains(rel.getTargetNodeId())) {
                    errors.add("关系引用了不存在的目标节点: " + rel.getTargetNodeId());
                }
            }
        }

        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        result.put("nodeCount", nodes != null ? nodes.size() : 0);
        result.put("relationshipCount", relationships != null ? relationships.size() : 0);

        return result;
    }

    @Override
    public Map<String, Object> optimizeGraphModel(Map<String, Object> graphModel) {
        if (graphModel == null || graphModel.isEmpty()) {
            return graphModel;
        }

        Map<String, Object> optimizedModel = new HashMap<>(graphModel);

        // 去重节点
        @SuppressWarnings("unchecked")
        List<GraphNodeDTO> nodes = (List<GraphNodeDTO>) graphModel.get("nodes");
        if (nodes != null) {
            Map<Long, GraphNodeDTO> uniqueNodes = nodes.stream()
                    .filter(node -> node.getId() != null)
                    .collect(Collectors.toMap(
                            GraphNodeDTO::getId,
                            node -> node,
                            (existing, replacement) -> existing
                    ));
            optimizedModel.put("nodes", new ArrayList<>(uniqueNodes.values()));
        }

        // 去重关系
        @SuppressWarnings("unchecked")
        List<GraphRelationshipDTO> relationships = (List<GraphRelationshipDTO>) graphModel.get("relationships");
        if (relationships != null) {
            List<GraphRelationshipDTO> uniqueRelationships = relationships.stream()
                    .filter(rel -> rel.getSourceNodeId() != null && rel.getTargetNodeId() != null)
                    .distinct()
                    .collect(Collectors.toList());
            optimizedModel.put("relationships", uniqueRelationships);
        }

        optimizedModel.put("optimized", true);
        optimizedModel.put("optimizeTime", System.currentTimeMillis());

        return optimizedModel;
    }

    @Override
    public Map<String, Object> generateGraphStats(Map<String, Object> graphModel) {
        Map<String, Object> stats = new HashMap<>();

        if (graphModel == null || graphModel.isEmpty()) {
            stats.put("nodeCount", 0);
            stats.put("relationshipCount", 0);
            return stats;
        }

        @SuppressWarnings("unchecked")
        List<GraphNodeDTO> nodes = (List<GraphNodeDTO>) graphModel.get("nodes");
        @SuppressWarnings("unchecked")
        List<GraphRelationshipDTO> relationships = (List<GraphRelationshipDTO>) graphModel.get("relationships");

        stats.put("nodeCount", nodes != null ? nodes.size() : 0);
        stats.put("relationshipCount", relationships != null ? relationships.size() : 0);

        if (nodes != null) {
            Map<String, Long> nodeTypeStats = nodes.stream()
                    .collect(Collectors.groupingBy(
                            GraphNodeDTO::getType,
                            Collectors.counting()
                    ));
            stats.put("nodeTypeStats", nodeTypeStats);
        }

        if (relationships != null) {
            Map<String, Long> relationshipTypeStats = relationships.stream()
                    .collect(Collectors.groupingBy(
                            GraphRelationshipDTO::getType,
                            Collectors.counting()
                    ));
            stats.put("relationshipTypeStats", relationshipTypeStats);
        }

        stats.put("generateTime", System.currentTimeMillis());

        return stats;
    }

    @Override
    public String exportGraphModelToJson(Map<String, Object> graphModel) {
        try {
            return objectMapper.writeValueAsString(graphModel);
        } catch (JsonProcessingException e) {
            log.error("Error exporting graph model to JSON", e);
            throw new RuntimeException("导出图谱模型失败", e);
        }
    }

    @Override
    public Map<String, Object> importGraphModelFromJson(String jsonString) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> graphModel = objectMapper.readValue(jsonString, Map.class);
            return graphModel;
        } catch (JsonProcessingException e) {
            log.error("Error importing graph model from JSON", e);
            throw new RuntimeException("导入图谱模型失败", e);
        }
    }

    private String getRelationshipName(String relationshipType) {
        switch (relationshipType) {
            case "FOREIGN_KEY":
                return "外键关系";
            case "REFERENCES":
                return "引用关系";
            case "DEPENDS_ON":
                return "依赖关系";
            default:
                return relationshipType;
        }
    }

    private String getRelationshipDescription(String relationshipType) {
        switch (relationshipType) {
            case "FOREIGN_KEY":
                return "表间外键关系";
            case "REFERENCES":
                return "表间引用关系";
            case "DEPENDS_ON":
                return "表间依赖关系";
            default:
                return "表间" + relationshipType + "关系";
        }
    }
}
