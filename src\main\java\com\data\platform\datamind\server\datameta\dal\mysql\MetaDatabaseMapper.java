package com.data.platform.datamind.server.datameta.dal.mysql;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:30
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datainspection.dal.mysql
 * @description
 * @email <EMAIL>
 * @since 1.8
 */

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.framework.mybatis.core.mapper.BaseMapperX;
import com.data.platform.datamind.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabasePageReqVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据库/数据源元数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MetaDatabaseMapper extends BaseMapperX<MetaDatabaseDO> {

    default PageResult<MetaDatabaseDO> selectPage(MetaDatabasePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MetaDatabaseDO>()
                .likeIfPresent(MetaDatabaseDO::getName, reqVO.getName())
                .eqIfPresent(MetaDatabaseDO::getType, reqVO.getType())
                .orderByDesc(MetaDatabaseDO::getId));
    }
}
