package com.data.platform.datamind.server.datameta.dal.mysql;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.framework.mybatis.core.mapper.BaseMapperX;
import com.data.platform.datamind.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 索引元数据 Mapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.dal.mysql
 * @description 索引元数据映射器
 * @email <EMAIL>
 * @since 1.8
 */
@Mapper
public interface MetaIndexMapper extends BaseMapperX<MetaIndexDO> {

    default PageResult<MetaIndexDO> selectPage(MetaIndexPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MetaIndexDO>()
                .eqIfPresent(MetaIndexDO::getTableId, reqVO.getTableId())
                .eqIfPresent(MetaIndexDO::getDbId, reqVO.getDbId())
                .likeIfPresent(MetaIndexDO::getIndexName, reqVO.getIndexName())
                .eqIfPresent(MetaIndexDO::getIndexType, reqVO.getIndexType())
                .eqIfPresent(MetaIndexDO::getIsUnique, reqVO.getIsUnique())
                .eqIfPresent(MetaIndexDO::getIsPrimary, reqVO.getIsPrimary())
                .orderByDesc(MetaIndexDO::getId));
    }

    default List<MetaIndexDO> selectListByTableId(Long tableId) {
        return selectList(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getTableId, tableId)
                .orderBy(true, true, MetaIndexDO::getIndexName, MetaIndexDO::getColumnPosition));
    }

    default List<MetaIndexDO> selectListByDbId(Long dbId) {
        return selectList(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getDbId, dbId)
                .orderBy(true, true, MetaIndexDO::getTableName, MetaIndexDO::getIndexName, MetaIndexDO::getColumnPosition));
    }

    default List<MetaIndexDO> selectListByIndexName(@Param("tableId") Long tableId, @Param("indexName") String indexName) {
        return selectList(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getTableId, tableId)
                .eq(MetaIndexDO::getIndexName, indexName)
                .orderByAsc(MetaIndexDO::getColumnPosition));
    }

    default List<MetaIndexDO> selectPrimaryKeysByTableId(Long tableId) {
        return selectList(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getTableId, tableId)
                .eq(MetaIndexDO::getIsPrimary, true)
                .orderByAsc(MetaIndexDO::getColumnPosition));
    }

    default List<MetaIndexDO> selectUniqueIndexesByTableId(Long tableId) {
        return selectList(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getTableId, tableId)
                .eq(MetaIndexDO::getIsUnique, true)
                .orderBy(true, true, MetaIndexDO::getIndexName, MetaIndexDO::getColumnPosition));
    }

    default MetaIndexDO selectByTableIdAndIndexNameAndColumn(@Param("tableId") Long tableId, 
                                                            @Param("indexName") String indexName,
                                                            @Param("columnName") String columnName) {
        return selectOne(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getTableId, tableId)
                .eq(MetaIndexDO::getIndexName, indexName)
                .eq(MetaIndexDO::getColumnName, columnName));
    }

    default void deleteByTableId(Long tableId) {
        delete(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getTableId, tableId));
    }

    default void deleteByDbId(Long dbId) {
        delete(new LambdaQueryWrapperX<MetaIndexDO>()
                .eq(MetaIndexDO::getDbId, dbId));
    }
}
