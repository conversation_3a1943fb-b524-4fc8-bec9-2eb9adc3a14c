package com.data.platform.datamind.server.datameta.controller.web;

import com.data.platform.datamind.framework.common.pojo.CommonResult;
import com.data.platform.datamind.server.datameta.service.admin.MetaDataGraphService;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

import static com.data.platform.datamind.framework.common.pojo.CommonResult.success;

/**
 * Web端 - 元数据知识图谱 Controller
 *
 * <AUTHOR> Team
 */
@Tag(name = "Web端 - 元数据知识图谱")
@RestController
@RequestMapping("/web-api/data-meta/graph")
@Validated
@Slf4j
public class MetaDataGraphWebController {

    @Resource
    private MetaDataGraphService metaDataGraphService;

    @GetMapping("/table/{tableId}/graph")
    @Operation(summary = "获取表的关系图谱")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public CommonResult<Map<String, Object>> getTableGraph(@PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> graph = metaDataGraphService.getTableGraph(tableId);
        return success(graph);
    }

    @GetMapping("/column/{columnId}/lineage")
    @Operation(summary = "获取列的血缘关系")
    @Parameter(name = "columnId", description = "列ID", required = true)
    public CommonResult<List<LineageNodeVO>> getColumnLineage(@PathVariable("columnId") @NotNull @Positive Long columnId) {
        List<LineageNodeVO> lineage = metaDataGraphService.getColumnLineage(columnId);
        return success(lineage);
    }

    @GetMapping("/table/{tableId}/upstream")
    @Operation(summary = "获取表的上游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    public CommonResult<List<TableRelationshipVO>> getUpstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<TableRelationshipVO> upstream = metaDataGraphService.getUpstreamTables(tableId, depth);
        return success(upstream);
    }

    @GetMapping("/table/{tableId}/downstream")
    @Operation(summary = "获取表的下游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    public CommonResult<List<TableRelationshipVO>> getDownstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<TableRelationshipVO> downstream = metaDataGraphService.getDownstreamTables(tableId, depth);
        return success(downstream);
    }

    @GetMapping("/database/{databaseId}/overview")
    @Operation(summary = "获取数据库的表关系概览")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public CommonResult<Map<String, Object>> getDatabaseTableOverview(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> overview = metaDataGraphService.getDatabaseTableOverview(databaseId);
        return success(overview);
    }

    @GetMapping("/table/search")
    @Operation(summary = "搜索相关表")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "limit", description = "结果限制", required = false)
    public CommonResult<List<Map<String, Object>>> searchTables(
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        List<Map<String, Object>> results = metaDataGraphService.searchTables(keyword, limit);
        return success(results);
    }

    @GetMapping("/table/{tableId}/impact")
    @Operation(summary = "获取表的影响分析")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public CommonResult<Map<String, Object>> getTableImpactAnalysis(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> impact = metaDataGraphService.getTableImpactAnalysis(tableId);
        return success(impact);
    }

    @GetMapping("/status")
    @Operation(summary = "获取图谱状态信息")
    public CommonResult<Map<String, Object>> getGraphStatus() {
        // TODO: 实现图谱状态查询
        Map<String, Object> status = Map.of(
                "connected", true,
                "nodeCount", 0,
                "relationshipCount", 0,
                "lastSyncTime", System.currentTimeMillis()
        );
        return success(status);
    }

    @GetMapping("/visualization/{tableId}")
    @Operation(summary = "获取表关系可视化数据")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "关系深度", required = false)
    public CommonResult<Map<String, Object>> getTableVisualization(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "2") Integer depth) {
        
        // 获取表的关系图谱数据，用于前端可视化
        Map<String, Object> graph = metaDataGraphService.getTableGraph(tableId);
        
        // 可以在这里添加更多可视化相关的数据处理
        Map<String, Object> visualization = Map.of(
                "graph", graph,
                "depth", depth,
                "centerTableId", tableId
        );
        
        return success(visualization);
    }
}
