package com.data.platform.datamind.server.datameta.service.web.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.data.platform.datamind.server.datameta.config.ArcheryConfig;
import com.data.platform.datamind.server.datameta.dal.mysql.DatabaseBasicInfoDao;
import com.data.platform.datamind.server.datameta.entity.ao.SyncAO;
import com.data.platform.datamind.server.datameta.entity.dto.DatabaseBasicInfoDTO;
import com.data.platform.datamind.server.datameta.vo.ArcheryInstanceVO;
import com.data.platform.datamind.server.datameta.vo.InstanceVO;
import com.data.platform.datamind.server.datameta.vo.SyncVO;
import com.data.platform.datamind.server.datameta.service.web.MetaDataService;
import com.data.platform.datamind.server.datameta.entity.bo.ConnectionInfo;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class SyncDBMetaInfoService {
    private static final Logger log = LoggerFactory.getLogger(SyncDBMetaInfoService.class);

    private final ArcheryConfig archeryConfig;

    private final DatabaseBasicInfoDao metaDao;

    private final Map<String, MetaDataService> metaMap;

    private final ExecutorService EXECUTOR;

    public SyncDBMetaInfoService(ArcheryConfig archeryConfig, DatabaseBasicInfoDao metaDao, Map<String, MetaDataService> metaMap, @Value("${sync.thread_num}") Integer threadNum) {
        this.archeryConfig = archeryConfig;
        this.metaDao = metaDao;
        this.metaMap = metaMap;
        this.EXECUTOR = Executors.newFixedThreadPool(threadNum.intValue(), r -> new Thread(r, "META-SYNC"));
    }

    public SyncVO syncAllDBMetaInfo(String dbType) {
//        List<DatabaseBasicInfoDTO> instanceList = this.metaDao.selectList((Wrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) ((LambdaQueryWrapper) (new LambdaQueryWrapper())
//                .eq(DatabaseBasicInfoDTO::getApiflag, "01")).eq(DatabaseBasicInfoDTO::getInstType, "master"))
//                .notIn(DatabaseBasicInfoDTO::getAccessAddress, new Object[]{"visual"})).orderByAsc(DatabaseBasicInfoDTO::getId));
//        log.info("{}", Integer.valueOf(instanceList.size()));
//        Set<String> archeryUrlList = new HashSet<>();
//        Map<String, Integer> map = new HashMap<>();
//        for (DatabaseBasicInfoDTO basicInfoDTO : instanceList) {
//            archeryUrlList.add(basicInfoDTO.getAccessAddress());
//            map.put(basicInfoDTO.getAccessAddress() + new Integer(basicInfoDTO.getInstanceId()), basicInfoDTO.getId());
//        }
//        for (String archeryUrl : archeryUrlList) {
//            log.info("{}", archeryUrl);
//            List<ArcheryInstanceVO> instanceVOList = null;
//            try {
//                instanceVOList = getInstanceListByApi(archeryUrl, null);
//            } catch (Exception e) {
//                log.error("" + archeryUrl, e);
//                continue;
//            }
//            log.info("{}", Integer.valueOf(instanceVOList.size()));
//            if (instanceVOList.isEmpty())
//                continue;
//            instanceVOList = (List<ArcheryInstanceVO>) instanceVOList.stream().filter(instance -> map.containsKey(archeryUrl + instance.getId())).collect(Collectors.toList());
//            if (dbType != null)
//                instanceVOList = (List<ArcheryInstanceVO>) instanceVOList.stream().filter(vo -> dbType.equals(vo.getDbType())).collect(Collectors.toList());
//            log.info("instance {} ", Integer.valueOf(instanceVOList.size()));
//            syncInstanceList(instanceVOList, archeryUrl, map);
//        }
        return new SyncVO();
    }

    private void updateStatus(Integer id, String status, String msg) {
        DatabaseBasicInfoDTO entity = new DatabaseBasicInfoDTO();
        entity.setId(id);
        entity.setSyncFlag(status);
        if (msg != null) {
            if (msg.length() > 128)
                msg = msg.substring(0, 128);
            entity.setSyncMsg(msg);
        }
        entity.setSyncTime(new Date());
        this.metaDao.updateById(entity);
    }

    private void syncInstanceList(List<ArcheryInstanceVO> instanceVOList, String archeryUrl, Map<String, Integer> filerMap) {
        List<ConnectionInfo> connectionList = wrapConnectionInfo(instanceVOList, archeryUrl);
        for (ConnectionInfo connectionInfo : connectionList) {
            String dbType = connectionInfo.getDbType();
            MetaDataService metaDataService = getMetaDataService(dbType);
            if (metaDataService == null) {
                log.warn("{}", dbType);
                continue;
            }
            if (filerMap == null) {
                try {
                    syncInstance(connectionInfo, metaDataService);
                } catch (Exception e) {
                    log.error("", e);
                }
                continue;
            }
            Integer configId = filerMap.get(connectionInfo.getArcheryHost() + connectionInfo.getInstanceId());
            if (configId == null)
                log.info("{}", JSON.toJSONString(connectionInfo));
            this.EXECUTOR.submit(() -> {
                try {
                    updateStatus(configId, "00", null);
                    syncInstance(connectionInfo, metaDataService);
                    updateStatus(configId, "01", null);
                } catch (Exception e) {
                    updateStatus(configId, "02", e.getMessage());
                }
            });
        }
    }

    private void syncInstance(ConnectionInfo connectionInfo, MetaDataService metaDataService) {
        if ("pgsql".equals(connectionInfo.getDbType())) {
            List<String> dbUserList = getDbUserList(connectionInfo.getArcheryHost(), connectionInfo.getInstanceId());
            Set<String> syncSet = new HashSet<>();
            for (String dbUser : dbUserList) {
                connectionInfo.setCurrentSchema(dbUser);
                if ("polardb_admin".equals(dbUser) || "template0".equals(dbUser) || "template1".equals(dbUser)) {
                    log.info("{}", dbUser);
                    continue;
                }
                syncSet.add(dbUser);
                metaDataService.syncAllTableMetaInfo(connectionInfo, (syncSet.size() == 1));
                log.info("{}{}", Integer.valueOf(syncSet.size()), syncSet);
            }
        } else {
            metaDataService.syncAllTableMetaInfo(connectionInfo, true);
        }
    }

    private List<ConnectionInfo> wrapConnectionInfo(List<ArcheryInstanceVO> instanceByType, String archeryUrl) {
        List<ConnectionInfo> connectionInfoList = new ArrayList<>();
        for (ArcheryInstanceVO instanceVO : instanceByType) {
            ConnectionInfo connectionInfo = new ConnectionInfo();
            connectionInfo.setArcheryHost(archeryUrl);
            connectionInfo.setDbType(instanceVO.getDbType());
            connectionInfo.setHost(instanceVO.getHost());
            connectionInfo.setPort(instanceVO.getPort());
            connectionInfo.setUsername(instanceVO.getUser());
            connectionInfo.setPassword(instanceVO.getPassword());
            connectionInfo.setDatabase(instanceVO.getDbName());
            connectionInfo.setServiceName(instanceVO.getServiceName());
            connectionInfo.setInstanceId(instanceVO.getId());
            connectionInfoList.add(connectionInfo);
        }
        return connectionInfoList;
    }

    public MetaDataService getMetaDataService(String dbType) {
        MetaDataService metaDataService = null;
        if ("oracle".equals(dbType))
            metaDataService = this.metaMap.get("oraMetaDataService");
        if ("mysql".equals(dbType) || "rds".equals(dbType))
            metaDataService = this.metaMap.get("mysqlMetaDataService");
        if ("pgsql".equals(dbType))
            metaDataService = this.metaMap.get("postgresqlMetaDataService");
        return metaDataService;
    }

    public List<ArcheryInstanceVO> getInstanceListByApi(String host, Integer id) {
        RestTemplate restTemplate = getRestTemplate(host);
        String apiPath = "/api/v1/instance/";
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(host + apiPath).queryParam("size", new Object[]{Integer.valueOf(10000)});
        if (id != null)
            builder.queryParam("id", new Object[]{id});
        String url = builder.toUriString();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class, new Object[0]);
        String body = (String) response.getBody();
        return JSON.parseObject(body).getJSONArray("results").toJavaList(ArcheryInstanceVO.class);
    }

    public List<String> getDbUserList(String accessAddress, Integer instanceId) {
        Map<String, Object> data = new HashMap<>();
        data.put("instance_id", instanceId);
        data.put("resource_type", "database");
        String url = "/api/v1/instance/resource/";
        log.info("{},{}", accessAddress, instanceId);
        RestTemplate restTemplate = getRestTemplate(accessAddress);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<String> entity = new HttpEntity(JSON.toJSONString(data), (MultiValueMap) headers);
        ResponseEntity<String> response = restTemplate.postForEntity(accessAddress + url, entity, String.class, new Object[0]);
        String body = (String) response.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        JSONArray result = jsonObject.getJSONArray("result");
        List<String> dbUsers = result.toJavaList(String.class);
        log.info("{}", JSON.toJSONString(dbUsers));
        return dbUsers;
    }

    private RestTemplate getRestTemplate(String host) {
        String authToken = ArcheryAuthService.getAuthToken(host, this.archeryConfig.getUsername(), this.archeryConfig.getPassword());
        RestTemplate restTemplate = new RestTemplate();
        SimpleClientHttpRequestFactory simpleClientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        simpleClientHttpRequestFactory.setConnectTimeout(3000);
        simpleClientHttpRequestFactory.setReadTimeout(10000);
        restTemplate.setRequestFactory((ClientHttpRequestFactory) simpleClientHttpRequestFactory);
//        restTemplate.setInterceptors(Collections.singletonList(new HeaderInterceptor(authToken)));
        return restTemplate;
    }

    public void syncInstance(SyncAO syncAO) {
        List<Integer> ids = syncAO.getIds();
        List<ArcheryInstanceVO> instanceVOList = getInstanceListByApi(syncAO.getHost(), null);
        List<ArcheryInstanceVO> list = (List<ArcheryInstanceVO>) instanceVOList.stream().filter(vo -> ids.contains(vo.getId())).collect(Collectors.toList());
        log.info("{}{}", syncAO.getHost(), Integer.valueOf(list.size()));
        syncInstanceList(list, syncAO.getHost(), null);
    }

    public ConnectionInfo getConnection(InstanceVO vo) throws Exception {
        String archeryUrl = vo.getArcheryUrl();
        Integer id = vo.getId();
        if (archeryUrl == null || "".equals(archeryUrl)) {
            log.info("archeryUrl");
            throw new Exception("archeryUrl");
        }
        log.info("{}", JSON.toJSONString(vo));
        List<ArcheryInstanceVO> instanceVOList = getInstanceListByApi(archeryUrl, id);
        if (CollectionUtils.isEmpty(instanceVOList)) {
            log.info("{}", JSON.toJSONString(vo));
            throw new Exception("" + JSON.toJSONString(vo) + "");
        }
        log.info("{}", Integer.valueOf(instanceVOList.size()));
        List<ConnectionInfo> connectionList = wrapConnectionInfo2(instanceVOList);
        return connectionList.get(0);
    }

    private List<ConnectionInfo> wrapConnectionInfo2(List<ArcheryInstanceVO> instanceList) {
        List<ConnectionInfo> connectionInfoList = new ArrayList<>();
        for (ArcheryInstanceVO instanceVO : instanceList) {
            ConnectionInfo connectionInfo = new ConnectionInfo();
            connectionInfo.setHost(instanceVO.getHost());
            connectionInfo.setPort(instanceVO.getPort());
            connectionInfo.setUsername(instanceVO.getUser());
            connectionInfo.setPassword(instanceVO.getPassword());
            connectionInfo.setDatabase(instanceVO.getDbName());
            connectionInfo.setServiceName(instanceVO.getServiceName());
            connectionInfo.setDbType(instanceVO.getDbType());
            connectionInfo.setId(instanceVO.getId());
            connectionInfoList.add(connectionInfo);
        }
        return connectionInfoList;
    }
}