package com.data.platform.datamind.server.datameta.vo.index;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 索引元数据更新 Request VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.vo.index
 * @description 索引元数据更新请求VO
 * @email <EMAIL>
 * @since 1.8
 */
@Schema(description = "管理后台 - 索引元数据更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaIndexUpdateReqVO extends MetaIndexBaseVO {

    @Schema(description = "索引ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "索引ID不能为空")
    private Long id;

    @Schema(description = "实例ID", example = "mysql-001")
    private String instanceId;
}
