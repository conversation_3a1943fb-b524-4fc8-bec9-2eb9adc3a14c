package com.data.platform.datamind.server.datameta.entity.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class MetaIndexInfoDTO {
    private String colName;
    private Integer instanceId;
    private String tabName;
    private String indexName;
    private String tabOwner;

    private Integer indexPosition;

    private String indexType;

    private Date updateTime;

    private Long distinctNum;
}

