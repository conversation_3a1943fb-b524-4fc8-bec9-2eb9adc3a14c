package com.data.platform.datamind.server.datameta.service.admin;

import java.util.List;
import java.util.Map;

/**
 * 影响分析服务接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/6
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 影响分析服务，提供数据变更影响分析功能
 * @since 1.8
 */
public interface ImpactAnalyzerService {

    /**
     * 分析表变更的影响范围
     *
     * @param tableId 表ID
     * @param changeType 变更类型（DROP/ALTER/RENAME等）
     * @return 影响分析结果
     */
    Map<String, Object> analyzeTableImpact(Long tableId, String changeType);

    /**
     * 分析列变更的影响范围
     *
     * @param columnId 列ID
     * @param changeType 变更类型（DROP/ALTER/RENAME等）
     * @return 影响分析结果
     */
    Map<String, Object> analyzeColumnImpact(Long columnId, String changeType);

    /**
     * 分析数据库变更的影响范围
     *
     * @param databaseId 数据库ID
     * @param changeType 变更类型
     * @return 影响分析结果
     */
    Map<String, Object> analyzeDatabaseImpact(Long databaseId, String changeType);

    /**
     * 分析索引变更的影响范围
     *
     * @param indexId 索引ID
     * @param changeType 变更类型
     * @return 影响分析结果
     */
    Map<String, Object> analyzeIndexImpact(Long indexId, String changeType);

    /**
     * 分析表结构变更的影响
     *
     * @param tableId 表ID
     * @param structureChanges 结构变更详情
     * @return 影响分析结果
     */
    Map<String, Object> analyzeStructureChangeImpact(Long tableId, Map<String, Object> structureChanges);

    /**
     * 分析数据类型变更的影响
     *
     * @param columnId 列ID
     * @param oldDataType 原数据类型
     * @param newDataType 新数据类型
     * @return 影响分析结果
     */
    Map<String, Object> analyzeDataTypeChangeImpact(Long columnId, String oldDataType, String newDataType);

    /**
     * 分析约束变更的影响
     *
     * @param tableId 表ID
     * @param constraintChanges 约束变更详情
     * @return 影响分析结果
     */
    Map<String, Object> analyzeConstraintChangeImpact(Long tableId, Map<String, Object> constraintChanges);

    /**
     * 分析权限变更的影响
     *
     * @param objectId 对象ID
     * @param objectType 对象类型（TABLE/COLUMN/DATABASE）
     * @param permissionChanges 权限变更详情
     * @return 影响分析结果
     */
    Map<String, Object> analyzePermissionChangeImpact(Long objectId, String objectType, Map<String, Object> permissionChanges);

    /**
     * 批量分析影响范围
     *
     * @param changes 变更列表
     * @return 批量影响分析结果
     */
    Map<String, Object> batchAnalyzeImpact(List<Map<String, Object>> changes);

    /**
     * 分析级联影响
     *
     * @param rootObjectId 根对象ID
     * @param rootObjectType 根对象类型
     * @param changeType 变更类型
     * @param maxDepth 最大分析深度
     * @return 级联影响分析结果
     */
    Map<String, Object> analyzeCascadingImpact(Long rootObjectId, String rootObjectType, String changeType, Integer maxDepth);

    /**
     * 分析性能影响
     *
     * @param tableId 表ID
     * @param changeType 变更类型
     * @return 性能影响分析结果
     */
    Map<String, Object> analyzePerformanceImpact(Long tableId, String changeType);

    /**
     * 分析业务影响
     *
     * @param tableId 表ID
     * @param changeType 变更类型
     * @return 业务影响分析结果
     */
    Map<String, Object> analyzeBusinessImpact(Long tableId, String changeType);

    /**
     * 分析应用程序影响
     *
     * @param tableId 表ID
     * @param changeType 变更类型
     * @return 应用程序影响分析结果
     */
    Map<String, Object> analyzeApplicationImpact(Long tableId, String changeType);

    /**
     * 分析数据质量影响
     *
     * @param tableId 表ID
     * @param changeType 变更类型
     * @return 数据质量影响分析结果
     */
    Map<String, Object> analyzeDataQualityImpact(Long tableId, String changeType);

    /**
     * 生成影响分析报告
     *
     * @param analysisResults 分析结果列表
     * @param reportFormat 报告格式
     * @return 影响分析报告
     */
    String generateImpactReport(List<Map<String, Object>> analysisResults, String reportFormat);

    /**
     * 评估变更风险等级
     *
     * @param impactAnalysis 影响分析结果
     * @return 风险等级评估
     */
    Map<String, Object> assessChangeRiskLevel(Map<String, Object> impactAnalysis);

    /**
     * 提供变更建议
     *
     * @param impactAnalysis 影响分析结果
     * @return 变更建议
     */
    List<String> provideChangeRecommendations(Map<String, Object> impactAnalysis);

    /**
     * 模拟变更影响
     *
     * @param changeScenario 变更场景
     * @return 模拟影响结果
     */
    Map<String, Object> simulateChangeImpact(Map<String, Object> changeScenario);

    /**
     * 分析回滚影响
     *
     * @param originalChange 原始变更
     * @return 回滚影响分析结果
     */
    Map<String, Object> analyzeRollbackImpact(Map<String, Object> originalChange);

    /**
     * 分析时间窗口影响
     *
     * @param tableId 表ID
     * @param changeType 变更类型
     * @param timeWindow 时间窗口
     * @return 时间窗口影响分析
     */
    Map<String, Object> analyzeTimeWindowImpact(Long tableId, String changeType, Map<String, Object> timeWindow);

    /**
     * 分析依赖链影响
     *
     * @param tableId 表ID
     * @param changeType 变更类型
     * @return 依赖链影响分析结果
     */
    Map<String, Object> analyzeDependencyChainImpact(Long tableId, String changeType);

    /**
     * 分析并发影响
     *
     * @param changes 并发变更列表
     * @return 并发影响分析结果
     */
    Map<String, Object> analyzeConcurrentImpact(List<Map<String, Object>> changes);

    /**
     * 分析历史影响模式
     *
     * @param tableId 表ID
     * @param changeType 变更类型
     * @param historicalPeriod 历史周期
     * @return 历史影响模式分析
     */
    Map<String, Object> analyzeHistoricalImpactPatterns(Long tableId, String changeType, String historicalPeriod);

    /**
     * 验证影响分析的准确性
     *
     * @param impactAnalysis 影响分析结果
     * @param actualImpact 实际影响
     * @return 准确性验证结果
     */
    Map<String, Object> validateImpactAnalysisAccuracy(Map<String, Object> impactAnalysis, Map<String, Object> actualImpact);

    /**
     * 优化影响分析算法
     *
     * @param feedbackData 反馈数据
     * @return 优化结果
     */
    Map<String, Object> optimizeImpactAnalysisAlgorithm(List<Map<String, Object>> feedbackData);

    /**
     * 导出影响分析数据
     *
     * @param analysisId 分析ID
     * @param exportFormat 导出格式
     * @return 导出数据
     */
    String exportImpactAnalysisData(String analysisId, String exportFormat);

    /**
     * 获取影响分析统计信息
     *
     * @param timeRange 时间范围
     * @return 统计信息
     */
    Map<String, Object> getImpactAnalysisStatistics(Map<String, Object> timeRange);

    /**
     * 监控影响分析性能
     *
     * @return 性能监控数据
     */
    Map<String, Object> monitorImpactAnalysisPerformance();
}
