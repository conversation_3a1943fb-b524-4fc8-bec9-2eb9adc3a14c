package com.data.platform.datamind.server.datameta.service.web.impl;

import com.alibaba.fastjson.JSON;
import java.util.Collections;

import com.data.platform.datamind.server.datameta.entity.ao.ArcherAuthAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Component
public class ArcheryAuthService {
  private static final Logger log = LoggerFactory.getLogger(ArcheryAuthService.class);
  
  public static String getAuthToken(String host, String userName, String password) {
    try {
      RestTemplate restTemplate = new RestTemplate();
      HttpHeaders httpHeaders = new HttpHeaders();
      httpHeaders.put("Content-Type", Collections.singletonList("application/json;charset=UTF-8"));
      HttpEntity<String> entity = new HttpEntity(JSON.toJSONString(new ArcherAuthAO(userName, password)), (MultiValueMap)httpHeaders);
      log.info("{} {}", host, userName);
      ResponseEntity<String> response = restTemplate.exchange(host + "/api/auth/token/", HttpMethod.POST, entity, String.class, new Object[0]);
      String responseStr = (String)response.getBody();
      String token = JSON.parseObject(responseStr).getString("access");
      log.info("{} token{}", Integer.valueOf(response.getStatusCodeValue()), token);
      return token;
    } catch (Exception e) {
      log.error("archery", e);
      throw new RuntimeException("archery");
    } 
  }
}
