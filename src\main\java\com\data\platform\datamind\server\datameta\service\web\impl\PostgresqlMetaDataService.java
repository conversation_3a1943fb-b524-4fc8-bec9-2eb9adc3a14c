package com.data.platform.datamind.server.datameta.service.web.impl;

import com.data.platform.datamind.server.datameta.entity.bo.ConnectionInfo;
import com.data.platform.datamind.server.datameta.entity.dto.MetaColInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaIndexInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaTableInfoDTO;
import com.data.platform.datamind.server.datameta.service.web.MetaDataService;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class PostgresqlMetaDataService implements MetaDataService {
  private static final Logger log = LoggerFactory.getLogger(PostgresqlMetaDataService.class);
  
  private static final String TABLE_QUERY = "select n.nspname as schemaname ,\n        c.relname ,\n        st.n_live_tup\nfrom pg_class c\njoin pg_namespace n on c.relnamespace = n.oid\nleft join pg_partitioned_table pt on c.oid=pt.partrelid\nleft join pg_stat_all_tables st on st.relid=c.oid\nwhere c.relkind='r'\nand not c.relispartition\nand n.nspname not in ('pg_catalog','pg_toast','information_schema','sys')";
  
  private static final String COL_QUERY = "select schemaname,relname,attname,typname,ROUND(dis) AS dis from(\nselect st.schemaname,c.relname,a.attname,t.typname,\n(select CASE WHEN ps.n_distinct = -1 THEN c.reltuples WHEN ps.n_distinct < 0 THEN (-ps.n_distinct)*c.reltuples ELSE ps.n_distinct END from pg_stats ps where ps.schemaname=st.schemaname and c.relname=ps.tablename and a.attname=ps.attname) dis \nfrom pg_class c\njoin pg_stat_all_tables st on st.relid=c.oid\njoin pg_attribute a on c.oid = a.attrelid\njoin pg_type t on a.atttypid = t.oid\nwhere c.relkind= 'r' and not c.relispartition\nand st.schemaname not in ('pg_catalog','pg_toast','information_schema','sys')\nand a.attnum>0 \nand not a.attisdropped)tmp";
  
  private static final String INDEX_QUERY = "SELECT\n    current_database() AS database_name,\n    n.nspname AS schema_name,\n    c.relname AS table_name,\n    i.relname AS index_name,\n    a.attname AS index_column_name,\n    am.amname AS index_type,\n    ROW_NUMBER() OVER (PARTITION BY i.oid ORDER BY ic.ordinality) AS column_order,\n    idx.indisunique AS is_unique\nFROM\n    pg_index idx\nJOIN pg_class i ON i.oid = idx.indexrelid\nJOIN pg_class c ON c.oid = idx.indrelid\nJOIN pg_namespace n ON n.oid = c.relnamespace\nJOIN pg_am am ON am.oid = i.relam\nCROSS JOIN LATERAL unnest(idx.indkey) WITH ORDINALITY AS ic(col_attnum, ordinality)\nJOIN pg_attribute a ON a.attnum = ic.col_attnum AND a.attrelid = c.oid\nWHERE\n    n.nspname NOT IN ('information_schema')\n    AND n.nspname !~ '^pg_toast'\n    AND c.relkind='r'\n    AND i.relkind='i';";
  
  private final DataSource dataSource;
  
  public PostgresqlMetaDataService(DataSource dataSource) {
    this.dataSource = dataSource;
  }
  
  public Logger getLogger() {
    return log;
  }
  
  public String buildConnectionInfo(ConnectionInfo connectionInfo) {
    String database = connectionInfo.getCurrentSchema();
    if (database == null || database.isEmpty())
      database = "postgres"; 
    return String.format("jdbc:postgresql://%s:%s/%s", new Object[] { connectionInfo.getHost(), connectionInfo.getPort(), database });
  }
  
  public List<MetaTableInfoDTO> getMetaTableInfoList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaTableInfoDTO> list = new ArrayList<>();
    try (PreparedStatement ps = connection.prepareStatement("select n.nspname as schemaname ,\n        c.relname ,\n        st.n_live_tup\nfrom pg_class c\njoin pg_namespace n on c.relnamespace = n.oid\nleft join pg_partitioned_table pt on c.oid=pt.partrelid\nleft join pg_stat_all_tables st on st.relid=c.oid\nwhere c.relkind='r'\nand not c.relispartition\nand n.nspname not in ('pg_catalog','pg_toast','information_schema','sys')")) {
      ResultSet rs = ps.executeQuery();
      while (rs.next()) {
        MetaTableInfoDTO metaTableInfoDTO = new MetaTableInfoDTO();
        metaTableInfoDTO.setInstanceId(connectionInfo.getInstanceId());
        metaTableInfoDTO.setTabOwner(rs.getString("schemaname"));
        metaTableInfoDTO.setTabName(rs.getString("relname"));
        metaTableInfoDTO.setRecordNum(Long.valueOf(rs.getLong("n_live_tup")));
        metaTableInfoDTO.setUpdateTime(new Date());
        list.add(metaTableInfoDTO);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return list;
  }
  
  public List<MetaColInfoDTO> getMetaColInfoDTOList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaColInfoDTO> list = new ArrayList<>();
    try (PreparedStatement ps = connection.prepareStatement("select schemaname,relname,attname,typname,ROUND(dis) AS dis from(\nselect st.schemaname,c.relname,a.attname,t.typname,\n(select CASE WHEN ps.n_distinct = -1 THEN c.reltuples WHEN ps.n_distinct < 0 THEN (-ps.n_distinct)*c.reltuples ELSE ps.n_distinct END from pg_stats ps where ps.schemaname=st.schemaname and c.relname=ps.tablename and a.attname=ps.attname) dis \nfrom pg_class c\njoin pg_stat_all_tables st on st.relid=c.oid\njoin pg_attribute a on c.oid = a.attrelid\njoin pg_type t on a.atttypid = t.oid\nwhere c.relkind= 'r' and not c.relispartition\nand st.schemaname not in ('pg_catalog','pg_toast','information_schema','sys')\nand a.attnum>0 \nand not a.attisdropped)tmp")) {
      ps.setFetchSize(1000);
      ResultSet rs = ps.executeQuery();
      while (rs.next()) {
        MetaColInfoDTO dto = new MetaColInfoDTO();
        dto.setInstanceId(connectionInfo.getInstanceId());
        dto.setTabOwner(rs.getString("schemaname"));
        dto.setTabName(rs.getString("relname"));
        dto.setColName(rs.getString("attname"));
        dto.setDataType(rs.getString("typname"));
        dto.setDistinctNum(Long.valueOf(rs.getLong("dis")));
        dto.setUpdateTime(new Date());
        list.add(dto);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return list;
  }
  
  public List<MetaIndexInfoDTO> getMetaIndexInfoDTOList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaIndexInfoDTO> list = new ArrayList<>();
    try (PreparedStatement ps = connection.prepareStatement("SELECT\n    current_database() AS database_name,\n    n.nspname AS schema_name,\n    c.relname AS table_name,\n    i.relname AS index_name,\n    a.attname AS index_column_name,\n    am.amname AS index_type,\n    ROW_NUMBER() OVER (PARTITION BY i.oid ORDER BY ic.ordinality) AS column_order,\n    idx.indisunique AS is_unique\nFROM\n    pg_index idx\nJOIN pg_class i ON i.oid = idx.indexrelid\nJOIN pg_class c ON c.oid = idx.indrelid\nJOIN pg_namespace n ON n.oid = c.relnamespace\nJOIN pg_am am ON am.oid = i.relam\nCROSS JOIN LATERAL unnest(idx.indkey) WITH ORDINALITY AS ic(col_attnum, ordinality)\nJOIN pg_attribute a ON a.attnum = ic.col_attnum AND a.attrelid = c.oid\nWHERE\n    n.nspname NOT IN ('information_schema')\n    AND n.nspname !~ '^pg_toast'\n    AND c.relkind='r'\n    AND i.relkind='i';")) {
      ResultSet rs = ps.executeQuery();
      while (rs.next()) {
        MetaIndexInfoDTO dto = new MetaIndexInfoDTO();
        dto.setInstanceId(connectionInfo.getInstanceId());
        dto.setTabOwner(rs.getString("schema_name"));
        dto.setTabName(rs.getString("table_name"));
        dto.setIndexName(rs.getString("index_name"));
        dto.setColName(rs.getString("index_column_name"));
        dto.setIndexPosition(Integer.valueOf(rs.getInt("column_order")));
        dto.setIndexType(rs.getString("index_type"));
        dto.setUpdateTime(new Date());
        list.add(dto);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return list;
  }
  
  public Connection getDestConnection() {
    try {
      return this.dataSource.getConnection();
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
  }
}