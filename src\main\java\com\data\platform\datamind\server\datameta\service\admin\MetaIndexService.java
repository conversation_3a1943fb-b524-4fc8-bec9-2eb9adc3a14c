package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexPageReqVO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexUpdateReqVO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 索引元数据 Service 接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 索引元数据服务接口
 * @email <EMAIL>
 * @since 1.8
 */
public interface MetaIndexService {

    /**
     * 创建索引元数据
     * @param createReqVO 创建请求VO
     * @return 索引ID
     */
    Long createIndex(@Valid MetaIndexCreateReqVO createReqVO);

    /**
     * 更新索引元数据
     * @param updateReqVO 更新请求VO
     */
    void updateIndex(@Valid MetaIndexUpdateReqVO updateReqVO);

    /**
     * 删除索引元数据
     * @param id 索引ID
     */
    void deleteIndex(Long id);

    /**
     * 获得索引元数据
     * @param id 索引ID
     * @return 索引DO
     */
    MetaIndexDO getIndex(Long id);

    /**
     * 获得索引元数据列表
     * @param ids 索引ID列表
     * @return 索引DO列表
     */
    List<MetaIndexDO> getIndexList(Collection<Long> ids);

    /**
     * 获得索引元数据分页
     * @param pageReqVO 分页请求VO
     * @return 分页结果
     */
    PageResult<MetaIndexDO> getIndexPage(MetaIndexPageReqVO pageReqVO);

    /**
     * 根据表ID获取索引列表
     * @param tableId 表ID
     * @return 索引DO列表
     */
    List<MetaIndexDO> getIndexesByTableId(Long tableId);

    /**
     * 根据数据库ID获取索引列表
     * @param dbId 数据库ID
     * @return 索引DO列表
     */
    List<MetaIndexDO> getIndexesByDbId(Long dbId);

    /**
     * 根据表ID和索引名获取索引列表
     * @param tableId 表ID
     * @param indexName 索引名
     * @return 索引DO列表
     */
    List<MetaIndexDO> getIndexesByTableIdAndIndexName(Long tableId, String indexName);

    /**
     * 获取表的主键索引
     * @param tableId 表ID
     * @return 主键索引列表
     */
    List<MetaIndexDO> getPrimaryKeysByTableId(Long tableId);

    /**
     * 获取表的唯一索引
     * @param tableId 表ID
     * @return 唯一索引列表
     */
    List<MetaIndexDO> getUniqueIndexesByTableId(Long tableId);

    /**
     * 批量插入索引元数据
     * @param indexes 索引列表
     */
    void batchInsert(List<MetaIndexDO> indexes);

    /**
     * 批量更新索引元数据
     * @param indexes 索引列表
     */
    void batchUpdate(List<MetaIndexDO> indexes);

    /**
     * 根据表ID删除索引
     * @param tableId 表ID
     */
    void deleteIndexesByTableId(Long tableId);

    /**
     * 根据数据库ID删除索引
     * @param dbId 数据库ID
     */
    void deleteIndexesByDbId(Long dbId);

    /**
     * 获取所有索引数据（不分页）
     * @return 所有索引DO列表
     */
    List<MetaIndexDO> getAllIndexes();
}
