package com.data.platform.datamind.server.datameta.vo.lineage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 血缘关系 VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "血缘关系信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LineageRelationVO {

    @Schema(description = "源节点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long sourceNodeId;

    @Schema(description = "目标节点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long targetNodeId;

    @Schema(description = "关系类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "REFERENCES")
    private String relationType;

    @Schema(description = "关系描述", example = "外键引用")
    private String relationDescription;

    @Schema(description = "关系强度", example = "1.0")
    private Double relationStrength;

    @Schema(description = "创建时间", example = "1640995200000")
    private Long createTime;
}
