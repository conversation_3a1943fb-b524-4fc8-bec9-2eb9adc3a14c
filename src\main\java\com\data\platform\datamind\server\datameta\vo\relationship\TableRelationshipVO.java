package com.data.platform.datamind.server.datameta.vo.relationship;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表关系 VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "表关系信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableRelationshipVO {

    @Schema(description = "表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long tableId;

    @Schema(description = "表名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "user_info")
    private String tableName;

    @Schema(description = "表注释", example = "用户信息表")
    private String tableComment;

    @Schema(description = "数据库ID", example = "1")
    private Long databaseId;

    @Schema(description = "数据库名称", example = "user_center")
    private String databaseName;

    @Schema(description = "关系类型", example = "UPSTREAM")
    private String relationshipType;

    @Schema(description = "关系数量", example = "3")
    private Integer relationshipCount;

    @Schema(description = "关系强度", example = "0.8")
    private Double relationshipStrength;

    @Schema(description = "关系描述", example = "通过外键关联")
    private String relationshipDescription;

    @Schema(description = "表行数", example = "10000")
    private Long tableRows;

    @Schema(description = "表大小（字节）", example = "2048000")
    private Long dataLength;
}
