# DataMind Data-Meta 业务微服务基础规范约束文档

## 📋 文档概述

### 文档目的
本文档基于DataMind Data-Meta元数据管理服务的现有代码风格和架构模式，制定业务微服务开发的基础规范约束，确保代码质量、架构一致性和团队协作效率。

### 适用范围
- DataMind Cloud平台所有业务微服务
- 基于Spring Boot + MyBatis Plus技术栈的服务开发
- 数据元数据管理相关的业务功能模块

### 技术栈基础
- **框架版本**: Spring Boot 2.7.18 + MyBatis Plus
- **数据库**: MySQL 5.7+ (主存储) + Neo4j 4.0+ (图数据库)
- **连接池**: HikariCP + Dynamic DataSource
- **文档工具**: Swagger + Knife4j
- **对象映射**: MapStruct
- **构建工具**: Maven

## 🏗️ 项目架构规范

### 标准项目结构
```
datamind-server-{service-name}/
├── src/main/java/
│   └── com/data/platform/datamind/server/{service}/
│       ├── controller/
│       │   ├── admin/              # 管理端控制器
│       │   └── web/                # Web端控制器
│       ├── service/
│       │   ├── admin/              # 管理端服务层
│       │   │   └── impl/           # 服务实现类
│       │   └── web/                # Web端服务层
│       │       └── impl/           # 服务实现类
│       ├── dal/
│       │   ├── dataobject/         # 数据对象(DO)
│       │   └── mysql/              # MyBatis映射器
│       ├── convert/                # 对象转换器
│       ├── vo/                     # 视图对象
│       │   ├── {entity}/           # 按实体分组
│       │   │   ├── {Entity}BaseVO.java
│       │   │   ├── {Entity}CreateReqVO.java
│       │   │   ├── {Entity}UpdateReqVO.java
│       │   │   ├── {Entity}RespVO.java
│       │   │   └── {Entity}PageReqVO.java
│       ├── entity/                 # 业务实体
│       │   ├── dto/                # 数据传输对象
│       │   ├── bo/                 # 业务对象
│       │   └── ao/                 # 应用对象
│       ├── infrans/                # 基础设施层
│       │   ├── constant/           # 常量定义
│       │   ├── enumeration/        # 枚举定义
│       │   └── util/               # 工具类
│       ├── config/                 # 配置类
│       └── api/                    # 对外API接口
├── src/main/resources/
│   ├── application.yaml            # 主配置文件
│   ├── application-local.yaml     # 本地环境配置
│   └── mapper/                     # MyBatis映射文件
└── pom.xml                         # Maven配置
```

### DDD分层架构约束

#### Interface层 (接口层)
- **Controller**: 处理HTTP请求，参数校验，调用Application层
- **VO**: 视图对象，用于接口参数传递和响应
- **Convert**: 对象转换器，VO与DO之间的转换

#### Application层 (应用层)
- **Service**: 业务服务接口定义
- **ServiceImpl**: 业务服务实现，事务控制，业务逻辑编排

#### Domain层 (领域层)
- **DO**: 数据对象，对应数据库表结构
- **Entity**: 业务实体，包含业务逻辑
- **Enum**: 业务枚举定义
- **Constant**: 业务常量定义

#### Infrastructure层 (基础设施层)
- **Mapper**: MyBatis数据访问接口
- **Config**: 配置类和Bean定义
- **Util**: 工具类和辅助方法

## 📝 编码规范约束

### 包命名规范
```java
// 基础包结构 - 全小写，点分隔
com.data.platform.datamind.server.{service}

// 具体示例
com.data.platform.datamind.server.datameta.controller.admin
com.data.platform.datamind.server.datameta.service.admin.impl
com.data.platform.datamind.server.datameta.dal.dataobject
```

### 类命名规范
```java
// 1. 数据对象 - 实体名 + DO
public class MetaDatabaseDO extends BaseDO {}
public class MetaTableDO extends BaseDO {}

// 2. 视图对象 - 实体名 + 功能 + VO
public class MetaDatabaseCreateReqVO extends MetaDatabaseBaseVO {}
public class MetaDatabaseUpdateReqVO extends MetaDatabaseBaseVO {}
public class MetaDatabaseRespVO {}
public class MetaDatabasePageReqVO extends PageParam {}

// 3. 控制器 - 实体名 + Controller
public class MetaDatabaseController {}
public class MetaTableController {}

// 4. 服务类 - 实体名 + Service/ServiceImpl
public interface MetaDatabaseService {}
public class MetaDatabaseServiceImpl implements MetaDatabaseService {}

// 5. 映射器 - 实体名 + Mapper
public interface MetaDatabaseMapper extends BaseMapperX<MetaDatabaseDO> {}

// 6. 转换器 - 实体名 + Convert
public interface MetaDatabaseConvert {}
```

### 方法命名规范
```java
// CRUD操作标准命名
public Long createDatabase(MetaDatabaseCreateReqVO createReqVO) {}
public void updateDatabase(MetaDatabaseUpdateReqVO updateReqVO) {}
public void deleteDatabase(Long id) {}
public MetaDatabaseDO getDatabase(Long id) {}
public List<MetaDatabaseDO> getDatabaseList(Collection<Long> ids) {}
public PageResult<MetaDatabaseDO> getDatabasePage(MetaDatabasePageReqVO pageReqVO) {}

// 业务方法命名 - 动词 + 名词
public void validateDatabaseNameUnique(Long id, String name) {}
public void syncDatabaseMetadata(Long databaseId) {}
public List<MetaTableDO> getTablesByDatabaseId(Long databaseId) {}
```

## 🎯 注解使用规范

### Controller层注解标准
```java
@Tag(name = "管理后台 - 数据库元数据管理")
@RestController
@RequestMapping("/admin-api/data-meta/database")
@Validated
public class MetaDatabaseController {
    
    @PostMapping("/create")
    @Operation(summary = "创建数据库元数据")
    @PreAuthorize("@ss.hasPermission('meta:database:create')")
    public CommonResult<Long> createDatabase(@Valid @RequestBody MetaDatabaseCreateReqVO createReqVO) {
        Long id = databaseService.createDatabase(createReqVO);
        return success(id);
    }
    
    @GetMapping("/get")
    @Operation(summary = "获得数据库元数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('meta:database:query')")
    public CommonResult<MetaDatabaseRespVO> getDatabase(@RequestParam("id") Long id) {
        MetaDatabaseDO database = databaseService.getDatabase(id);
        return success(MetaDatabaseConvert.INSTANCE.convert(database));
    }
}
```

### Service层注解标准
```java
@Service
@Validated
public class MetaDatabaseServiceImpl implements MetaDatabaseService {
    
    @Resource
    private MetaDatabaseMapper databaseMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createDatabase(MetaDatabaseCreateReqVO createReqVO) {
        // 校验名称唯一性
        validateDatabaseNameUnique(null, createReqVO.getName());
        
        // VO -> DO
        MetaDatabaseDO database = MetaDatabaseConvert.INSTANCE.convert(createReqVO);
        databaseMapper.insert(database);
        return database.getId();
    }
}
```

### DO对象注解标准
```java
@TableName("S_META_DATABASE")
@KeySequence("meta_database_seq") // 用于 Oracle、PostgreSQL 等的序列
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetaDatabaseDO extends BaseDO implements Serializable, Cloneable {
    
    @TableId
    private Long id;
    
    @Schema(description = "数据库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "user_center")
    private String name;
    
    @Schema(description = "数据库类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "MySQL")
    private String type;
}
```

## 🔧 数据访问层规范

### Mapper接口标准
```java
@Mapper
public interface MetaDatabaseMapper extends BaseMapperX<MetaDatabaseDO> {
    
    default PageResult<MetaDatabaseDO> selectPage(MetaDatabasePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MetaDatabaseDO>()
                .likeIfPresent(MetaDatabaseDO::getName, reqVO.getName())
                .eqIfPresent(MetaDatabaseDO::getType, reqVO.getType())
                .orderByDesc(MetaDatabaseDO::getId));
    }
    
    default MetaDatabaseDO selectByName(String name) {
        return selectOne(MetaDatabaseDO::getName, name);
    }
}
```

### 数据库表设计规范
```sql
-- 表命名：S_{模块}_{功能}
CREATE TABLE `S_META_DATABASE` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '数据库名称',
    `type` varchar(50) NOT NULL COMMENT '数据库类型',
    `access_address` varchar(500) NOT NULL COMMENT '访问地址',
    `username` varchar(100) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '密码',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 (0:禁用, 1:启用)',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name_tenant` (`name`, `tenant_id`, `deleted`)
) ENGINE=InnoDB COMMENT='数据库元数据表';
```

## 🎨 VO对象设计规范

### BaseVO设计模式
```java
@Data
public class MetaDatabaseBaseVO {
    
    @Schema(description = "数据库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "user_center")
    @NotEmpty(message = "数据库名称不能为空")
    private String name;
    
    @Schema(description = "数据库类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "MySQL")
    @NotEmpty(message = "数据库类型不能为空")
    private String type;
    
    @Schema(description = "访问地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "********************************")
    @NotEmpty(message = "访问地址不能为空")
    private String accessAddress;
    
    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "root")
    @NotEmpty(message = "用户名不能为空")
    private String username;
    
    @Schema(description = "描述", example = "生产环境用户中心数据库")
    private String description;
    
    @Schema(description = "状态 (0:禁用, 1:启用)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
```

### 分页查询VO规范
```java
@Schema(description = "管理后台 - 数据库元数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaDatabasePageReqVO extends PageParam {
    
    @Schema(description = "数据库名称", example = "user_center")
    private String name;
    
    @Schema(description = "数据库类型", example = "MySQL")
    private String type;
    
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
```

## 🔄 对象转换器规范

### MapStruct转换器标准
```java
@Mapper
public interface MetaDatabaseConvert {
    
    MetaDatabaseConvert INSTANCE = Mappers.getMapper(MetaDatabaseConvert.class);
    
    MetaDatabaseDO convert(MetaDatabaseCreateReqVO bean);
    
    MetaDatabaseDO convert(MetaDatabaseUpdateReqVO bean);
    
    MetaDatabaseRespVO convert(MetaDatabaseDO bean);
    
    List<MetaDatabaseRespVO> convertList(List<MetaDatabaseDO> list);
    
    PageResult<MetaDatabaseRespVO> convertPage(PageResult<MetaDatabaseDO> page);
}
```

## ⚠️ 异常处理规范

### 错误码定义标准
```java
public interface MetaDataErrorCodeConstants extends GlobalErrorCodeConstants {
    
    // 数据库相关错误码 - 1000001xxx
    ErrorCode METADATABASE_NOT_EXISTS = new ErrorCode(1000001000, "数据库不存在");
    ErrorCode METADATABASE_NAME_DUPLICATE = new ErrorCode(1000001001, "数据库名称重复");
    
    // 表相关错误码 - 1000002xxx
    ErrorCode METATABLE_NOT_EXISTS = new ErrorCode(1000002000, "表不存在");
    ErrorCode METATABLE_NAME_DUPLICATE = new ErrorCode(1000002001, "表名称在同一数据库下重复");
    
    // 列相关错误码 - 1000003xxx
    ErrorCode METACOLUMN_NOT_EXISTS = new ErrorCode(1000003000, "列不存在");
    ErrorCode METACOLUMN_NAME_DUPLICATE = new ErrorCode(1000003001, "列名称在同一表下重复");
    
    // 业务操作错误码 - 1000004xxx
    ErrorCode METADATA_COLLECT_FAILED = new ErrorCode(1000004000, "元数据采集失败");
    ErrorCode METADATA_SYNC_GRAPH_FAILED = new ErrorCode(1000004001, "元数据同步图谱失败");
}
```

### 业务异常处理模式
```java
// Service层异常处理
private void validateDatabaseNameUnique(Long id, String name) {
    if (databaseMapper.selectByName(name) != null) {
        throw exception(METADATABASE_NAME_DUPLICATE);
    }
}

// 使用统一异常工具
if (database == null) {
    throw exception(METADATABASE_NOT_EXISTS);
}
```

## 📊 配置管理规范

### 应用配置标准结构
```yaml
# application.yaml - 主配置文件
spring:
  application:
    name: datamind-server-{service-name}
  profiles:
    active: local
  main:
    allow-circular-references: true # 三层架构循环依赖配置

# MyBatis Plus 标准配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: NONE # 智能模式，自动适配数据库类型
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  type-aliases-package: ${datamind.info.base-package}.server.*.dal.dataobject

# 业务配置标准
datamind:
  swagger:
    title: DataMind {Service} 服务
    description: 提供{业务功能}的完整解决方案
    version: ${datamind.info.version}
  tenant:
    enable: true # 多租户支持
```

### 环境配置分离
```yaml
# application-local.yaml - 本地开发环境
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          name: ${spring.application.name}
          url: ***************************/${spring.application.name}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: 123456

# 日志配置
logging:
  level:
    com.data.platform.datamind.server.{service}.dal.mysql: debug
```

## 🔐 安全规范约束

### 权限控制标准
```java
// Controller层权限注解
@PreAuthorize("@ss.hasPermission('meta:database:create')")
@PreAuthorize("@ss.hasPermission('meta:database:update')")
@PreAuthorize("@ss.hasPermission('meta:database:delete')")
@PreAuthorize("@ss.hasPermission('meta:database:query')")

// 权限命名规范：{模块}:{实体}:{操作}
// 示例：meta:database:create, meta:table:query
```

### 数据脱敏处理
```java
// 敏感字段处理
@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
private String password; // 密码字段只写不读

// DO对象中敏感字段加密存储
@TableField(typeHandler = EncryptTypeHandler.class)
private String password;
```

## 🧪 测试规范约束

### 单元测试标准
```java
@SpringBootTest
@Transactional
@Rollback
class MetaDatabaseServiceImplTest {

    @Resource
    private MetaDatabaseService databaseService;

    @Test
    void testCreateDatabase() {
        // Given
        MetaDatabaseCreateReqVO createReqVO = new MetaDatabaseCreateReqVO();
        createReqVO.setName("test_db");
        createReqVO.setType("MySQL");

        // When
        Long id = databaseService.createDatabase(createReqVO);

        // Then
        assertThat(id).isNotNull();
        MetaDatabaseDO database = databaseService.getDatabase(id);
        assertThat(database.getName()).isEqualTo("test_db");
    }
}
```

### 集成测试规范
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class MetaDatabaseControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void testCreateDatabaseApi() {
        // API集成测试逻辑
    }
}
```

## 📈 性能优化规范

### 查询优化标准
```java
// 分页查询优化
default PageResult<MetaDatabaseDO> selectPage(MetaDatabasePageReqVO reqVO) {
    return selectPage(reqVO, new LambdaQueryWrapperX<MetaDatabaseDO>()
            .likeIfPresent(MetaDatabaseDO::getName, reqVO.getName()) // 条件查询
            .eqIfPresent(MetaDatabaseDO::getType, reqVO.getType())
            .betweenIfPresent(MetaDatabaseDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(MetaDatabaseDO::getId)); // 排序优化
}

// 批量操作优化
@Override
@Transactional(rollbackFor = Exception.class)
public void batchInsert(List<MetaDatabaseDO> databases) {
    if (CollUtil.isEmpty(databases)) {
        return;
    }
    databaseMapper.insertBatch(databases); // 使用批量插入
}
```

### 缓存策略规范
```java
// Redis缓存注解使用
@Cacheable(value = "meta:database", key = "#id")
public MetaDatabaseDO getDatabase(Long id) {
    return databaseMapper.selectById(id);
}

@CacheEvict(value = "meta:database", key = "#id")
public void deleteDatabase(Long id) {
    databaseMapper.deleteById(id);
}
```

## 🔍 监控和日志规范

### 日志记录标准
```java
@Slf4j
@Service
public class MetaDatabaseServiceImpl implements MetaDatabaseService {

    @Override
    public Long createDatabase(MetaDatabaseCreateReqVO createReqVO) {
        log.info("[createDatabase] 开始创建数据库，参数：{}", createReqVO);

        try {
            // 业务逻辑
            Long id = doCreateDatabase(createReqVO);
            log.info("[createDatabase] 数据库创建成功，ID：{}", id);
            return id;
        } catch (Exception e) {
            log.error("[createDatabase] 数据库创建失败，参数：{}", createReqVO, e);
            throw e;
        }
    }
}
```

### 性能监控埋点
```java
// 关键业务方法添加性能监控
@Override
@Timed(name = "database.create", description = "数据库创建耗时")
public Long createDatabase(MetaDatabaseCreateReqVO createReqVO) {
    // 业务逻辑
}
```

## 📋 代码质量检查清单

### 开发阶段检查项
- [ ] **包结构规范**: 是否按照标准包结构组织代码
- [ ] **命名规范**: 类名、方法名、变量名是否符合规范
- [ ] **注解使用**: Controller、Service、DO等注解是否正确使用
- [ ] **异常处理**: 是否使用统一的异常处理机制
- [ ] **参数校验**: 接口参数是否添加校验注解
- [ ] **权限控制**: 是否添加适当的权限控制注解
- [ ] **事务管理**: 涉及数据修改的方法是否添加事务注解
- [ ] **日志记录**: 关键业务逻辑是否添加日志记录

### 代码审查检查项
- [ ] **业务逻辑**: 业务逻辑是否正确，边界条件是否考虑
- [ ] **性能考虑**: 是否存在N+1查询、大数据量处理等性能问题
- [ ] **安全检查**: 是否存在SQL注入、XSS等安全风险
- [ ] **代码复用**: 是否合理使用工具类和公共方法
- [ ] **测试覆盖**: 是否编写了充分的单元测试
- [ ] **文档完整**: 接口文档和代码注释是否完整

### 上线前检查项
- [ ] **配置检查**: 生产环境配置是否正确
- [ ] **依赖检查**: 第三方依赖是否存在安全漏洞
- [ ] **性能测试**: 是否通过性能测试
- [ ] **兼容性测试**: 是否通过多数据库兼容性测试
- [ ] **监控配置**: 监控和告警是否配置完整

## 🚀 最佳实践建议

### 开发流程建议
1. **需求分析**: 充分理解业务需求，明确功能边界
2. **设计先行**: 先设计数据模型和接口，再编写代码
3. **增量开发**: 采用增量开发模式，及时反馈和调整
4. **代码审查**: 所有代码必须经过同行审查
5. **测试驱动**: 编写代码的同时编写测试用例
6. **持续集成**: 使用CI/CD流水线自动化构建和部署

### 团队协作建议
1. **统一工具**: 使用统一的开发工具和代码格式化配置
2. **文档维护**: 及时更新接口文档和技术文档
3. **知识分享**: 定期进行技术分享和代码走读
4. **问题跟踪**: 使用统一的问题跟踪系统管理Bug和需求

## 📚 附录

### 常用工具类参考
```java
// 集合工具类
CollUtil.isEmpty(list) // 判断集合是否为空
CollUtil.isNotEmpty(list) // 判断集合是否不为空

// 字符串工具类
StrUtil.isBlank(str) // 判断字符串是否为空白
StrUtil.isNotBlank(str) // 判断字符串是否不为空白

// 对象工具类
ObjectUtil.isNull(obj) // 判断对象是否为null
ObjectUtil.isNotNull(obj) // 判断对象是否不为null

// 日期工具类
LocalDateTimeUtil.now() // 获取当前时间
LocalDateTimeUtil.format(dateTime, pattern) // 格式化时间
```

### 常用注解速查
```java
// 数据校验注解
@NotNull(message = "不能为空")
@NotEmpty(message = "不能为空")
@NotBlank(message = "不能为空白")
@Size(min = 1, max = 100, message = "长度必须在1-100之间")
@Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "只能包含字母、数字和下划线")

// Swagger文档注解
@Tag(name = "模块名称")
@Operation(summary = "接口描述")
@Parameter(name = "参数名", description = "参数描述", required = true)
@Schema(description = "字段描述", example = "示例值")

// MyBatis Plus注解
@TableName("表名")
@TableId
@TableField("字段名")
@TableLogic // 逻辑删除字段
```

### 错误码分配规则
```
业务模块错误码分配：
- 系统模块：1001xxx000 - 1001xxx999
- 用户模块：1002xxx000 - 1002xxx999
- 元数据模块：1000xxx000 - 1000xxx999
- 其他业务模块：依次递增

具体错误码规则：
- 数据库相关：xxx001xxx
- 表相关：xxx002xxx
- 列相关：xxx003xxx
- 业务操作：xxx004xxx
```

---

**注意**: 本规范文档基于DataMind Data-Meta服务的实际代码风格制定，所有团队成员必须严格遵守，确保代码质量和架构一致性。定期更新此文档以适应技术发展和业务需求变化。
