
package com.data.platform.datamind.server.datameta.vo;

import com.data.platform.datamind.server.datameta.entity.bo.LLMChoicesBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class LLMChatVO {

    private Long created;
    private String model;
    private String id;
    private String object;
    private List<LLMChoicesBO> choices;

}