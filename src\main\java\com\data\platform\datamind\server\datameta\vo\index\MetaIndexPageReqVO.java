package com.data.platform.datamind.server.datameta.vo.index;

import com.data.platform.datamind.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 索引元数据分页 Request VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.vo.index
 * @description 索引元数据分页请求VO
 * @email <EMAIL>
 * @since 1.8
 */
@Schema(description = "管理后台 - 索引元数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaIndexPageReqVO extends PageParam {

    @Schema(description = "表ID", example = "1")
    private Long tableId;

    @Schema(description = "数据库ID", example = "1")
    private Long dbId;

    @Schema(description = "索引名称", example = "idx_user_name")
    private String indexName;

    @Schema(description = "索引类型", example = "INDEX")
    private String indexType;

    @Schema(description = "是否唯一索引", example = "false")
    private Boolean isUnique;

    @Schema(description = "是否主键", example = "false")
    private Boolean isPrimary;

    @Schema(description = "列名", example = "user_name")
    private String columnName;

    @Schema(description = "表名", example = "user_info")
    private String tableName;
}
