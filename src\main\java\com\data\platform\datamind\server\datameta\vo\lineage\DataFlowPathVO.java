package com.data.platform.datamind.server.datameta.vo.lineage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据流路径 VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "数据流路径信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataFlowPathVO {

    @Schema(description = "路径ID", example = "path_001")
    private String pathId;

    @Schema(description = "源节点信息")
    private LineageNodeVO sourceNode;

    @Schema(description = "目标节点信息")
    private LineageNodeVO targetNode;

    @Schema(description = "路径节点列表（按顺序）")
    private List<LineageNodeVO> pathNodes;

    @Schema(description = "路径关系列表")
    private List<LineageRelationVO> pathRelations;

    @Schema(description = "路径长度", example = "3")
    private Integer pathLength;

    @Schema(description = "路径权重", example = "0.85")
    private Double pathWeight;

    @Schema(description = "路径类型", example = "DIRECT")
    private String pathType;

    @Schema(description = "路径状态", example = "ACTIVE")
    private String pathStatus;

    @Schema(description = "数据流方向", example = "DOWNSTREAM")
    private String flowDirection;

    @Schema(description = "数据流强度", example = "HIGH")
    private String flowStrength;

    @Schema(description = "路径描述", example = "用户信息表到订单表的数据流路径")
    private String pathDescription;

    @Schema(description = "路径创建时间", example = "2025-07-05 10:30:00")
    private String createTime;

    @Schema(description = "路径更新时间", example = "2025-07-05 15:45:00")
    private String updateTime;

    @Schema(description = "路径质量评分", example = "95")
    private Integer qualityScore;

    @Schema(description = "路径复杂度", example = "MEDIUM")
    private String complexity;

    @Schema(description = "是否为关键路径", example = "true")
    private Boolean isCriticalPath;

    @Schema(description = "路径标签")
    private List<String> pathTags;

    @Schema(description = "路径元数据")
    private Object pathMetadata;
}
