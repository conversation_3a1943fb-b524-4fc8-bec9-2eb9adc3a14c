package com.data.platform.datamind.server.datameta.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 图谱关系数据传输对象
 *
 * <AUTHOR> Team
 */
@Schema(description = "图谱关系DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GraphRelationshipDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关系ID", example = "1")
    private Long id;

    @Schema(description = "源节点ID", example = "1")
    private Long sourceNodeId;

    @Schema(description = "目标节点ID", example = "2")
    private Long targetNodeId;

    @Schema(description = "关系类型", example = "CONTAINS")
    private String type;

    @Schema(description = "关系名称", example = "包含")
    private String name;

    @Schema(description = "关系方向", example = "OUT")
    private String direction;

    @Schema(description = "关系属性")
    private Map<String, Object> properties;

    @Schema(description = "关系描述", example = "数据库包含表")
    private String description;

    @Schema(description = "关系权重", example = "1.0")
    private Double weight;

    @Schema(description = "关系状态", example = "ACTIVE")
    private String status;

    @Schema(description = "创建时间戳", example = "1625097600000")
    private Long createTime;

    @Schema(description = "更新时间戳", example = "1625097600000")
    private Long updateTime;

    @Schema(description = "扩展属性")
    private Map<String, Object> metadata;
}
