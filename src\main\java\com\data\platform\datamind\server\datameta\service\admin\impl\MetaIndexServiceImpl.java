package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaIndexConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.dal.mysql.MetaIndexMapper;
import com.data.platform.datamind.server.datameta.service.admin.MetaIndexService;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexPageReqVO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexUpdateReqVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 索引元数据 Service 实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin.impl
 * @description 索引元数据服务实现类
 * @email <EMAIL>
 * @since 1.8
 */
@Service
@Validated
public class MetaIndexServiceImpl implements MetaIndexService {

    @Resource
    private MetaIndexMapper metaIndexMapper;

    @Override
    public Long createIndex(MetaIndexCreateReqVO createReqVO) {
        // VO -> DO
        MetaIndexDO index = MetaIndexConvert.INSTANCE.convert(createReqVO);
        metaIndexMapper.insert(index);
        return index.getId();
    }

    @Override
    public void updateIndex(MetaIndexUpdateReqVO updateReqVO) {
        // VO -> DO
        MetaIndexDO index = MetaIndexConvert.INSTANCE.convert(updateReqVO);
        metaIndexMapper.updateById(index);
    }

    @Override
    public void deleteIndex(Long id) {
        metaIndexMapper.deleteById(id);
    }

    @Override
    public MetaIndexDO getIndex(Long id) {
        return metaIndexMapper.selectById(id);
    }

    @Override
    public List<MetaIndexDO> getIndexList(Collection<Long> ids) {
        return metaIndexMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MetaIndexDO> getIndexPage(MetaIndexPageReqVO pageReqVO) {
        return metaIndexMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MetaIndexDO> getIndexesByTableId(Long tableId) {
        return metaIndexMapper.selectListByTableId(tableId);
    }

    @Override
    public List<MetaIndexDO> getIndexesByDbId(Long dbId) {
        return metaIndexMapper.selectListByDbId(dbId);
    }

    @Override
    public List<MetaIndexDO> getIndexesByTableIdAndIndexName(Long tableId, String indexName) {
        return metaIndexMapper.selectListByIndexName(tableId, indexName);
    }

    @Override
    public List<MetaIndexDO> getPrimaryKeysByTableId(Long tableId) {
        return metaIndexMapper.selectPrimaryKeysByTableId(tableId);
    }

    @Override
    public List<MetaIndexDO> getUniqueIndexesByTableId(Long tableId) {
        return metaIndexMapper.selectUniqueIndexesByTableId(tableId);
    }

    @Override
    @Transactional
    public void batchInsert(List<MetaIndexDO> indexes) {
        if (indexes.isEmpty()) {
            return;
        }
        // 逐个插入，确保兼容性
        for (MetaIndexDO index : indexes) {
            metaIndexMapper.insert(index);
        }
    }

    @Override
    @Transactional
    public void batchUpdate(List<MetaIndexDO> indexes) {
        if (indexes.isEmpty()) {
            return;
        }
        // 逐个更新，确保兼容性
        for (MetaIndexDO index : indexes) {
            metaIndexMapper.updateById(index);
        }
    }

    @Override
    public void deleteIndexesByTableId(Long tableId) {
        metaIndexMapper.deleteByTableId(tableId);
    }

    @Override
    public void deleteIndexesByDbId(Long dbId) {
        metaIndexMapper.deleteByDbId(dbId);
    }

    @Override
    public List<MetaIndexDO> getAllIndexes() {
        return metaIndexMapper.selectList();
    }
}
