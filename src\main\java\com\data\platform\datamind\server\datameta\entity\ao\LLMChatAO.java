package com.data.platform.datamind.server.datameta.entity.ao;

import com.data.platform.datamind.server.datameta.entity.bo.LLMMessageBO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

/*    */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMChatAO {
    private String model;
    @JsonProperty("frequency_penalty")
    private String frequencyPenalty;
    @JsonProperty("presence_penalty")
    private String presencePenalty;

    public void setModel(String model) {
        this.model = model;
    }

    private boolean stream;
    private Float temperature;
    @JsonProperty("top_p")
    private Float topP;
    private List<LLMMessageBO> messages;

    @JsonProperty("frequency_penalty")
    public void setFrequencyPenalty(String frequencyPenalty) {
        this.frequencyPenalty = frequencyPenalty;
    }

    @JsonProperty("presence_penalty")
    public void setPresencePenalty(String presencePenalty) {
        this.presencePenalty = presencePenalty;
    }

    public void setStream(boolean stream) {
        this.stream = stream;
    }

    public void setTemperature(Float temperature) {
        this.temperature = temperature;
    }

    @JsonProperty("top_p")
    public void setTopP(Float topP) {
        this.topP = topP;
    }

    public void setMessages(List<LLMMessageBO> messages) {
        this.messages = messages;
    }
}
