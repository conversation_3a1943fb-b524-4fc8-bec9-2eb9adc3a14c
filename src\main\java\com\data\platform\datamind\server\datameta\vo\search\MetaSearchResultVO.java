package com.data.platform.datamind.server.datameta.vo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 元数据搜索结果VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "元数据搜索结果")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetaSearchResultVO {

    @Schema(description = "对象ID", example = "1")
    private Long id;

    @Schema(description = "对象类型", example = "TABLE")
    private String objectType; // DATABASE, TABLE, COLUMN

    @Schema(description = "对象名称", example = "user_info")
    private String name;

    @Schema(description = "显示名称（高亮后）", example = "<em>user</em>_info")
    private String displayName;

    @Schema(description = "对象描述", example = "用户信息表")
    private String description;

    @Schema(description = "显示描述（高亮后）", example = "<em>用户</em>信息表")
    private String displayDescription;

    @Schema(description = "对象注释", example = "存储用户基本信息")
    private String comment;

    @Schema(description = "显示注释（高亮后）", example = "存储<em>用户</em>基本信息")
    private String displayComment;

    @Schema(description = "相关度分数", example = "0.95")
    private Double relevanceScore;

    @Schema(description = "匹配字段列表", example = "[\"name\", \"description\"]")
    private List<String> matchedFields;

    @Schema(description = "匹配片段", example = "[\"user_info\", \"用户信息\"]")
    private List<String> matchedFragments;

    @Schema(description = "父对象信息")
    private ParentObjectInfo parentObject;

    @Schema(description = "子对象数量", example = "10")
    private Integer childCount;

    @Schema(description = "标签列表", example = "[\"重要\", \"敏感\"]")
    private List<String> tags;

    @Schema(description = "数据类型", example = "VARCHAR")
    private String dataType;

    @Schema(description = "数据长度", example = "255")
    private Integer dataLength;

    @Schema(description = "是否可为空", example = "false")
    private Boolean nullable;

    @Schema(description = "是否主键", example = "true")
    private Boolean primaryKey;

    @Schema(description = "创建时间", example = "2023-01-01T00:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-01-01T00:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "扩展属性")
    private Map<String, Object> properties;

    @Schema(description = "搜索上下文信息")
    private SearchContext searchContext;

    /**
     * 父对象信息
     */
    @Schema(description = "父对象信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParentObjectInfo {
        @Schema(description = "父对象ID", example = "1")
        private Long id;

        @Schema(description = "父对象类型", example = "DATABASE")
        private String type;

        @Schema(description = "父对象名称", example = "user_center")
        private String name;

        @Schema(description = "父对象路径", example = "user_center.user_info")
        private String path;
    }

    /**
     * 搜索上下文信息
     */
    @Schema(description = "搜索上下文信息")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchContext {
        @Schema(description = "搜索关键词", example = "user")
        private String keyword;

        @Schema(description = "搜索类型", example = "FULL_TEXT")
        private String searchType;

        @Schema(description = "匹配方式", example = "EXACT")
        private String matchType; // EXACT, FUZZY, PARTIAL

        @Schema(description = "搜索时间", example = "2023-01-01T00:00:00")
        private LocalDateTime searchTime;

        @Schema(description = "搜索耗时（毫秒）", example = "50")
        private Long searchDuration;
    }
}
