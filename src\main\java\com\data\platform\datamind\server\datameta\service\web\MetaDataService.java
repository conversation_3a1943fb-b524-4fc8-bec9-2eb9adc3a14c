package com.data.platform.datamind.server.datameta.service.web;

import java.sql.Connection;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import com.data.platform.datamind.server.datameta.entity.bo.ConnectionInfo;
import com.data.platform.datamind.server.datameta.entity.dto.MetaColInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaIndexInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaTableInfoDTO;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.MDC;

public interface MetaDataService {
    public static final String INSERT_ERROR = "INSERT INTO database_sync_error_log(access_address,db_name,instance_id,instance_name,db_port,err_message,dt) VALUES (?,?,?,?,?,?,?)";

    Logger getLogger();

    String buildConnectionInfo(ConnectionInfo paramConnectionInfo);

    List<MetaTableInfoDTO> getMetaTableInfoList(Connection paramConnection, ConnectionInfo paramConnectionInfo);

    List<MetaColInfoDTO> getMetaColInfoDTOList(Connection paramConnection, ConnectionInfo paramConnectionInfo);

    List<MetaIndexInfoDTO> getMetaIndexInfoDTOList(Connection paramConnection, ConnectionInfo paramConnectionInfo);

    default Connection getConnection(ConnectionInfo connectionInfo) {
        try {
            DriverManager.setLoginTimeout(3);
            String url = buildConnectionInfo(connectionInfo);
            getLogger().info("{}", url);
            return DriverManager.getConnection(url, connectionInfo.getUsername(), connectionInfo.getPassword());
        } catch (SQLException e) {
            getLogger().error("", e);
            throw new RuntimeException("" + connectionInfo.getHost() + ":" + connectionInfo.getPort(), e);
        }
    }

    Connection getDestConnection();

    default void saveTableInfo(List<MetaTableInfoDTO> tableList, ConnectionInfo connectionInfo, boolean cleanFlag) {
        if (tableList.isEmpty())
            return;
        Connection destConnection = getDestConnection();
        try {
            destConnection.setAutoCommit(false);
            if (cleanFlag) {
                PreparedStatement dps = destConnection.prepareStatement("delete from META_TABLE_INFO where INSTANCE_ID = ?");
                dps.setString(1, instanceIdMD5(connectionInfo));
                dps.execute();
                destConnection.commit();
            }
            PreparedStatement preparedStatement = destConnection.prepareStatement("INSERT INTO META_TABLE_INFO (DB_TYPE,TAB_NAME,TAB_OWNER,INSTANCE_ID,RECORD_NUM,PARTITION_NUM,TAB_COMMENT,ARCH_URL,ARCH_ID,UPDATE_TIME) VALUES (?,?,?,?,?,?,?,?,?,?);");
            List<List<MetaTableInfoDTO>> partition = ListUtils.partition(tableList, 1000);
            int i = 0;
            for (List<MetaTableInfoDTO> metaTableInfoDTOS : partition) {
                for (MetaTableInfoDTO dto : metaTableInfoDTOS) {
                    preparedStatement.setString(1, connectionInfo.getDbType());
                    preparedStatement.setString(2, dto.getTabName().toUpperCase());
                    preparedStatement.setString(3, dto.getTabOwner().toUpperCase());
                    preparedStatement.setString(4, instanceIdMD5(connectionInfo));
                    preparedStatement.setLong(5, dto.getRecordNum().longValue());
                    preparedStatement.setInt(6, (dto.getPartitionNum() == null) ? 0 : dto.getPartitionNum().intValue());
                    preparedStatement.setString(7, truncate(dto.getTabComment(), 1024));
                    preparedStatement.setString(8, connectionInfo.getArcheryHost());
                    preparedStatement.setString(9, connectionInfo.getInstanceId().toString());
                    preparedStatement.setDate(10, new Date(dto.getUpdateTime().getTime()));
                    preparedStatement.addBatch();
                }
                preparedStatement.executeBatch();
                destConnection.commit();
                getLogger().info(" {}/{}", Integer.valueOf(Math.min(++i * 1000, tableList.size())), Integer.valueOf(tableList.size()));
            }
        } catch (SQLException e) {
            try {
                destConnection.rollback();
            } catch (SQLException ex) {
                throw new RuntimeException(ex);
            }
            throw new RuntimeException(e);
        } finally {
            try {
                destConnection.setAutoCommit(true);
                destConnection.close();
            } catch (SQLException e) {
                getLogger().error("", e);
            }
        }
    }

    default void saveColInfo(List<MetaColInfoDTO> colList, String instanceId, boolean cleanFlag) {
        if (colList.isEmpty())
            return;
        Connection destConnection = getDestConnection();
        try {
            destConnection.setAutoCommit(false);
            if (cleanFlag) {
                PreparedStatement dps = destConnection.prepareStatement("delete from META_COL_INFO where INSTANCE_ID = ?");
                dps.setString(1, instanceId);
                dps.execute();
                destConnection.commit();
            }
            PreparedStatement preparedStatement = destConnection.prepareStatement("INSERT INTO META_COL_INFO (COL_NAME,INSTANCE_ID,TAB_NAME,TAB_OWNER,DATA_TYPE,COL_COMMENT,DISTINCT_NUM,UPDATE_TIME) VALUES (?,?,?,?,?,?,?,?);");
            List<List<MetaColInfoDTO>> partition = ListUtils.partition(colList, 1000);
            int colSize = colList.size();
            colList = null;
            int i = 0;
            for (List<MetaColInfoDTO> metaTableInfoDTOS : partition) {
                for (MetaColInfoDTO dto : metaTableInfoDTOS) {
                    preparedStatement.setString(1, dto.getColName().toUpperCase());
                    preparedStatement.setString(2, instanceId);
                    preparedStatement.setString(3, dto.getTabName().toUpperCase());
                    preparedStatement.setString(4, dto.getTabOwner().toUpperCase());
                    preparedStatement.setString(5, dto.getDataType());
                    preparedStatement.setString(6, truncate(dto.getColComment(), 1024));
                    if (dto.getDistinctNum() == null) {
                        preparedStatement.setNull(7, -5);
                    } else {
                        preparedStatement.setLong(7, dto.getDistinctNum().longValue());
                    }
                    preparedStatement.setDate(8, new Date(dto.getUpdateTime().getTime()));
                    preparedStatement.addBatch();
                }
                preparedStatement.executeBatch();
                destConnection.commit();
                getLogger().info(" {}/{}", Integer.valueOf(Math.min(++i * 1000, colSize)), Integer.valueOf(colSize));
            }
        } catch (SQLException e) {
            try {
                destConnection.rollback();
            } catch (SQLException ex) {
                throw new RuntimeException(ex);
            }
            throw new RuntimeException(e);
        } finally {
            try {
                destConnection.setAutoCommit(true);
                destConnection.close();
            } catch (SQLException e) {
                getLogger().error("", e);
            }
        }
    }

    static String truncate(String str, int len) {
        return (str == null) ? null : ((str.length() > len) ? str.substring(0, len) : str);
    }

    default void saveIndexInfo(List<MetaIndexInfoDTO> indexList, String instanceId, boolean cleanFlag) {
        if (indexList.isEmpty())
            return;
        Connection destConnection = getDestConnection();
        try {
            destConnection.setAutoCommit(false);
            if (cleanFlag) {
                String sql = "delete from META_INDEX_INFO where INSTANCE_ID = ?";
                PreparedStatement dps = destConnection.prepareStatement(sql);
                dps.setString(1, instanceId);
                dps.execute();
                destConnection.commit();
            }
            PreparedStatement preparedStatement = destConnection.prepareStatement("INSERT INTO META_INDEX_INFO ( INSTANCE_ID,COL_NAME, TAB_NAME, TAB_OWNER,INDEX_POSITION,INDEX_TYPE, UPDATE_TIME,INDEX_NAME,DISTINCT_NUM) VALUES   (?,?,?,?,?,?,?,?,?);");
            List<List<MetaIndexInfoDTO>> partition = ListUtils.partition(indexList, 1000);
            int i = 0;
            for (List<MetaIndexInfoDTO> metaTableInfoDTOS : partition) {
                for (MetaIndexInfoDTO dto : metaTableInfoDTOS) {
                    preparedStatement.setString(1, instanceId);
                    preparedStatement.setString(2, dto.getColName());
                    preparedStatement.setString(3, dto.getTabName().toUpperCase());
                    preparedStatement.setString(4, dto.getTabOwner().toUpperCase());
                    preparedStatement.setInt(5, (dto.getIndexPosition() == null) ? 1 : dto.getIndexPosition().intValue());
                    preparedStatement.setString(6, dto.getIndexType());
                    preparedStatement.setDate(7, new Date(dto.getUpdateTime().getTime()));
                    preparedStatement.setString(8, dto.getIndexName());
                    if (dto.getDistinctNum() == null) {
                        preparedStatement.setNull(9, -5);
                    } else {
                        preparedStatement.setLong(9, dto.getDistinctNum().longValue());
                    }
                    preparedStatement.addBatch();
                }
                preparedStatement.executeBatch();
                destConnection.commit();
                getLogger().info(" {}/{}", Integer.valueOf(Math.min(++i * 1000, indexList.size())), Integer.valueOf(indexList.size()));
            }
        } catch (SQLException e) {
            try {
                destConnection.rollback();
            } catch (SQLException ex) {
                throw new RuntimeException(ex);
            }
            throw new RuntimeException(e);
        } finally {
            try {
                destConnection.setAutoCommit(true);
                destConnection.close();
            } catch (SQLException e) {
                getLogger().error("", e);
            }
        }
    }

    default void syncAllTableMetaInfo(ConnectionInfo connectionInfo, boolean cleanFlag) {
        getLogger().info("{} ", connectionInfo);
        MDC.put("ARCH_URL", connectionInfo.getArcheryHost());
        MDC.put("ARCH_ID", connectionInfo.getInstanceId().toString());
        try (Connection connection = getConnection(connectionInfo)) {
            getLogger().info("{}", connectionInfo);
            List<MetaTableInfoDTO> tableList = getMetaTableInfoList(connection, connectionInfo);
            getLogger().info("{}", Integer.valueOf(tableList.size()));
            getLogger().info("");
            saveTableInfo(tableList, connectionInfo, cleanFlag);
            tableList = null;
            getLogger().info("");
            List<MetaColInfoDTO> colList = getMetaColInfoDTOList(connection, connectionInfo);
            getLogger().info("{}", Integer.valueOf(colList.size()));
            getLogger().info("");
            saveColInfo(colList, instanceIdMD5(connectionInfo), cleanFlag);
            colList = null;
            getLogger().info("");
            List<MetaIndexInfoDTO> indexList = getMetaIndexInfoDTOList(connection, connectionInfo);
            getLogger().info("{}", Integer.valueOf(indexList.size()));
            getLogger().info("");
            saveIndexInfo(indexList, instanceIdMD5(connectionInfo), cleanFlag);
            indexList = null;
            getLogger().info("");
        } catch (Exception e) {
            getLogger().error("", e);
            saveErrLog(connectionInfo, e.getMessage());
            throw new RuntimeException(e);
        } finally {
            MDC.clear();
        }
    }

    default String instanceIdMD5(ConnectionInfo connectionInfo) {
        return DigestUtils.md5Hex(connectionInfo.getInstanceId() + connectionInfo.getArcheryHost());
    }

    default void saveErrLog(ConnectionInfo connectionInfo, String errMsg) {
        try (Connection connection = getDestConnection()) {
            PreparedStatement ps = connection.prepareStatement("INSERT INTO database_sync_error_log(access_address,db_name,instance_id,instance_name,db_port,err_message,dt) VALUES (?,?,?,?,?,?,?)");
            ps.setString(1, connectionInfo.getArcheryHost());
            ps.setString(2, connectionInfo.getUsername());
            ps.setString(3, connectionInfo.getInstanceId().toString());
            ps.setString(4, connectionInfo.getHost());
            ps.setString(5, connectionInfo.getPort().toString());
            ps.setString(6, errMsg);
            ps.setDate(7, new Date(System.currentTimeMillis()));
            ps.execute();
            ps.close();
        } catch (Exception e) {
            getLogger().error("", e);
        }
    }
}
