package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaIndexInfoDTO;
import com.data.platform.datamind.server.datameta.service.admin.IndexManager;
import com.data.platform.datamind.server.datameta.service.admin.MetaIndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 索引管理器实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin.impl
 * @description 索引管理器实现，提供索引和约束的统一管理功能
 * @email <EMAIL>
 * @since 1.8
 */
@Service
@Slf4j
public class IndexManagerImpl implements IndexManager {

    @Resource
    private MetaIndexService metaIndexService;

    @Override
    @Transactional
    public void syncTableIndexes(MetaTableDO table, List<MetaIndexInfoDTO> indexInfoList) {
        if (indexInfoList == null || indexInfoList.isEmpty()) {
            return;
        }

        // 先清理该表的旧索引数据
        metaIndexService.deleteIndexesByTableId(table.getId());

        // 转换并插入新的索引数据
        List<MetaIndexDO> indexes = convertIndexInfoToIndexDO(table, indexInfoList);
        if (!indexes.isEmpty()) {
            metaIndexService.batchInsert(indexes);
        }

        log.info("同步表 {} 的索引信息完成，共 {} 个索引", table.getTableName(), indexes.size());
    }

    @Override
    public Map<String, Object> analyzeTableIndexes(Long tableId) {
        List<MetaIndexDO> indexes = metaIndexService.getIndexesByTableId(tableId);
        
        Map<String, Object> analysis = new HashMap<>();
        analysis.put("totalIndexes", indexes.size());
        analysis.put("primaryKeys", indexes.stream().filter(idx -> Boolean.TRUE.equals(idx.getIsPrimary())).count());
        analysis.put("uniqueIndexes", indexes.stream().filter(idx -> Boolean.TRUE.equals(idx.getIsUnique())).count());
        analysis.put("normalIndexes", indexes.stream().filter(idx -> !Boolean.TRUE.equals(idx.getIsPrimary()) && !Boolean.TRUE.equals(idx.getIsUnique())).count());
        
        // 按索引名分组统计
        Map<String, Long> indexNameCount = indexes.stream()
                .collect(Collectors.groupingBy(MetaIndexDO::getIndexName, Collectors.counting()));
        analysis.put("indexNames", indexNameCount.keySet());
        analysis.put("compositeIndexes", indexNameCount.entrySet().stream().filter(entry -> entry.getValue() > 1).count());
        
        return analysis;
    }

    @Override
    public List<MetaIndexDO> getTablePrimaryKeys(Long tableId) {
        return metaIndexService.getPrimaryKeysByTableId(tableId);
    }

    @Override
    public List<MetaIndexDO> getTableForeignKeys(Long tableId) {
        // 外键通常通过索引类型或特殊标识来识别
        return metaIndexService.getIndexesByTableId(tableId).stream()
                .filter(idx -> "FOREIGN".equalsIgnoreCase(idx.getIndexType()) || 
                              "FK".equalsIgnoreCase(idx.getIndexType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<MetaIndexDO> getTableUniqueConstraints(Long tableId) {
        return metaIndexService.getUniqueIndexesByTableId(tableId);
    }

    @Override
    public List<MetaIndexDO> getTableNormalIndexes(Long tableId) {
        return metaIndexService.getIndexesByTableId(tableId).stream()
                .filter(idx -> !Boolean.TRUE.equals(idx.getIsPrimary()) && 
                              !Boolean.TRUE.equals(idx.getIsUnique()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> checkIndexHealth(Long tableId) {
        List<MetaIndexDO> indexes = metaIndexService.getIndexesByTableId(tableId);
        Map<String, Object> health = new HashMap<>();
        
        // 检查是否有主键
        boolean hasPrimaryKey = indexes.stream().anyMatch(idx -> Boolean.TRUE.equals(idx.getIsPrimary()));
        health.put("hasPrimaryKey", hasPrimaryKey);
        
        // 检查重复索引
        Map<String, List<MetaIndexDO>> columnIndexMap = indexes.stream()
                .collect(Collectors.groupingBy(MetaIndexDO::getColumnName));
        List<String> duplicateColumns = columnIndexMap.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        health.put("duplicateIndexColumns", duplicateColumns);
        
        // 检查基数过低的索引
        List<String> lowCardinalityIndexes = indexes.stream()
                .filter(idx -> idx.getCardinality() != null && idx.getCardinality() < 10)
                .map(MetaIndexDO::getIndexName)
                .distinct()
                .collect(Collectors.toList());
        health.put("lowCardinalityIndexes", lowCardinalityIndexes);
        
        return health;
    }

    @Override
    public List<String> suggestIndexOptimization(Long tableId) {
        List<String> suggestions = new ArrayList<>();
        Map<String, Object> health = checkIndexHealth(tableId);
        
        if (!Boolean.TRUE.equals(health.get("hasPrimaryKey"))) {
            suggestions.add("建议为表添加主键以提高查询性能");
        }
        
        @SuppressWarnings("unchecked")
        List<String> duplicateColumns = (List<String>) health.get("duplicateIndexColumns");
        if (!duplicateColumns.isEmpty()) {
            suggestions.add("发现重复索引的列: " + String.join(", ", duplicateColumns) + "，建议合并或删除冗余索引");
        }
        
        @SuppressWarnings("unchecked")
        List<String> lowCardinalityIndexes = (List<String>) health.get("lowCardinalityIndexes");
        if (!lowCardinalityIndexes.isEmpty()) {
            suggestions.add("发现基数过低的索引: " + String.join(", ", lowCardinalityIndexes) + "，建议评估是否需要保留");
        }
        
        return suggestions;
    }

    @Override
    @Transactional
    public void batchSyncDatabaseIndexes(Long dbId, List<MetaIndexInfoDTO> indexInfoList) {
        if (indexInfoList == null || indexInfoList.isEmpty()) {
            return;
        }

        // 先清理该数据库的旧索引数据
        metaIndexService.deleteIndexesByDbId(dbId);

        // 按表分组处理索引信息
        Map<String, List<MetaIndexInfoDTO>> tableIndexMap = indexInfoList.stream()
                .collect(Collectors.groupingBy(MetaIndexInfoDTO::getTabName));

        int totalSynced = 0;
        for (Map.Entry<String, List<MetaIndexInfoDTO>> entry : tableIndexMap.entrySet()) {
            // 这里需要根据表名查找对应的MetaTableDO，暂时创建一个临时对象
            MetaTableDO tempTable = new MetaTableDO();
            tempTable.setDbId(dbId);
            tempTable.setTableName(entry.getKey());
            
            List<MetaIndexDO> indexes = convertIndexInfoToIndexDO(tempTable, entry.getValue());
            if (!indexes.isEmpty()) {
                metaIndexService.batchInsert(indexes);
                totalSynced += indexes.size();
            }
        }

        log.info("批量同步数据库 {} 的索引信息完成，共 {} 个索引", dbId, totalSynced);
    }

    @Override
    public void cleanTableIndexes(Long tableId) {
        metaIndexService.deleteIndexesByTableId(tableId);
        log.info("清理表 {} 的索引信息完成", tableId);
    }

    @Override
    public void cleanDatabaseIndexes(Long dbId) {
        metaIndexService.deleteIndexesByDbId(dbId);
        log.info("清理数据库 {} 的索引信息完成", dbId);
    }

    @Override
    public Map<String, Object> getIndexStatistics(Long dbId) {
        List<MetaIndexDO> indexes = metaIndexService.getIndexesByDbId(dbId);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalIndexes", indexes.size());
        statistics.put("totalTables", indexes.stream().map(MetaIndexDO::getTableId).distinct().count());
        statistics.put("primaryKeys", indexes.stream().filter(idx -> Boolean.TRUE.equals(idx.getIsPrimary())).count());
        statistics.put("uniqueIndexes", indexes.stream().filter(idx -> Boolean.TRUE.equals(idx.getIsUnique())).count());
        statistics.put("normalIndexes", indexes.stream().filter(idx -> !Boolean.TRUE.equals(idx.getIsPrimary()) && !Boolean.TRUE.equals(idx.getIsUnique())).count());
        
        // 索引类型分布
        Map<String, Long> typeDistribution = indexes.stream()
                .filter(idx -> idx.getIndexType() != null)
                .collect(Collectors.groupingBy(MetaIndexDO::getIndexType, Collectors.counting()));
        statistics.put("indexTypeDistribution", typeDistribution);
        
        return statistics;
    }

    @Override
    public boolean validateIndexConsistency(Long tableId) {
        List<MetaIndexDO> indexes = metaIndexService.getIndexesByTableId(tableId);
        
        // 检查主键一致性：每个表最多只能有一个主键索引组
        Set<String> primaryKeyIndexNames = indexes.stream()
                .filter(idx -> Boolean.TRUE.equals(idx.getIsPrimary()))
                .map(MetaIndexDO::getIndexName)
                .collect(Collectors.toSet());
        
        if (primaryKeyIndexNames.size() > 1) {
            log.warn("表 {} 存在多个主键索引: {}", tableId, primaryKeyIndexNames);
            return false;
        }
        
        // 检查索引位置一致性
        Map<String, List<MetaIndexDO>> indexGroups = indexes.stream()
                .collect(Collectors.groupingBy(MetaIndexDO::getIndexName));
        
        for (Map.Entry<String, List<MetaIndexDO>> entry : indexGroups.entrySet()) {
            List<MetaIndexDO> indexColumns = entry.getValue();
            indexColumns.sort(Comparator.comparing(MetaIndexDO::getColumnPosition));
            
            for (int i = 0; i < indexColumns.size(); i++) {
                if (indexColumns.get(i).getColumnPosition() != i + 1) {
                    log.warn("索引 {} 的列位置不连续", entry.getKey());
                    return false;
                }
            }
        }
        
        return true;
    }

    @Override
    @Transactional
    public void rebuildTableIndexes(Long tableId) {
        // 重建索引：先删除再重新创建（这里只是清理，实际重建需要连接数据库）
        cleanTableIndexes(tableId);
        log.info("重建表 {} 的索引信息完成", tableId);
    }

    /**
     * 将MetaIndexInfoDTO转换为MetaIndexDO
     */
    private List<MetaIndexDO> convertIndexInfoToIndexDO(MetaTableDO table, List<MetaIndexInfoDTO> indexInfoList) {
        return indexInfoList.stream().map(dto -> {
            MetaIndexDO index = new MetaIndexDO();
            index.setTableId(table.getId());
            index.setDbId(table.getDbId());
            index.setInstanceId(String.valueOf(dto.getInstanceId()));
            index.setIndexName(dto.getIndexName());
            index.setIndexType(dto.getIndexType());
            index.setColumnName(dto.getColName());
            index.setColumnPosition(dto.getIndexPosition());
            index.setCardinality(dto.getDistinctNum());
            index.setTableName(dto.getTabName());
            index.setTableOwner(dto.getTabOwner());
            
            // 根据索引类型判断是否为主键或唯一索引
            if ("PRIMARY".equalsIgnoreCase(dto.getIndexType())) {
                index.setIsPrimary(true);
                index.setIsUnique(true);
            } else if ("UNIQUE".equalsIgnoreCase(dto.getIndexType())) {
                index.setIsPrimary(false);
                index.setIsUnique(true);
            } else {
                index.setIsPrimary(false);
                index.setIsUnique(false);
            }
            
            return index;
        }).collect(Collectors.toList());
    }
}
