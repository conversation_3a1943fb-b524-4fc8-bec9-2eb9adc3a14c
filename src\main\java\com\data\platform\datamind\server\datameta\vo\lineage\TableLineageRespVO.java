package com.data.platform.datamind.server.datameta.vo.lineage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 表血缘关系响应 VO
 */
@Schema(description = "Web端 - 表血缘关系 Response VO")
@Data
public class TableLineageRespVO {

    @Schema(description = "目标表信息")
    private TableInfo targetTable;

    @Schema(description = "上游表列表（依赖的表）")
    private List<TableInfo> upstreamTables;

    @Schema(description = "下游表列表（被依赖的表）")
    private List<TableInfo> downstreamTables;

    @Schema(description = "血缘关系列表")
    private List<LineageRelation> relations;

    /**
     * 表信息
     */
    @Data
    @Schema(description = "表信息")
    public static class TableInfo {
        @Schema(description = "表ID", example = "1")
        private Long tableId;

        @Schema(description = "表名", example = "user_info")
        private String tableName;

        @Schema(description = "表注释", example = "用户信息表")
        private String tableComment;

        @Schema(description = "数据库ID", example = "1")
        private Long databaseId;

        @Schema(description = "数据库名", example = "user_center")
        private String databaseName;

        @Schema(description = "表行数", example = "10000")
        private Long tableRows;

        @Schema(description = "数据大小（字节）", example = "2048000")
        private Long dataLength;
    }

    /**
     * 血缘关系
     */
    @Data
    @Schema(description = "血缘关系")
    public static class LineageRelation {
        @Schema(description = "源表ID", example = "1")
        private Long sourceTableId;

        @Schema(description = "源表名", example = "user_info")
        private String sourceTableName;

        @Schema(description = "目标表ID", example = "2")
        private Long targetTableId;

        @Schema(description = "目标表名", example = "user_profile")
        private String targetTableName;

        @Schema(description = "关系类型", example = "FOREIGN_KEY")
        private RelationType relationType;

        @Schema(description = "关系描述", example = "user_info.id -> user_profile.user_id")
        private String relationDescription;

        /**
         * 关系类型枚举
         */
        public enum RelationType {
            /**
             * 外键关系
             */
            FOREIGN_KEY,
            /**
             * 视图依赖
             */
            VIEW_DEPENDENCY,
            /**
             * ETL依赖
             */
            ETL_DEPENDENCY,
            /**
             * 其他依赖
             */
            OTHER
        }
    }
}
