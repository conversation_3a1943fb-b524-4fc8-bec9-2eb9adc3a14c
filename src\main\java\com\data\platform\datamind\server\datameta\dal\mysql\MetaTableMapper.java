package com.data.platform.datamind.server.datameta.dal.mysql;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:05
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datainspection.dal.mysql
 * @description
 * @email <EMAIL>
 * @since 1.8
 */

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.framework.mybatis.core.mapper.BaseMapperX;
import com.data.platform.datamind.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTablePageReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表元数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MetaTableMapper extends BaseMapperX<MetaTableDO> {

    default PageResult<MetaTableDO> selectPage(MetaTablePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MetaTableDO>()
                .eqIfPresent(MetaTableDO::getDbId, reqVO.getDbId())
                .likeIfPresent(MetaTableDO::getTableName, reqVO.getTableName())
                .likeIfPresent(MetaTableDO::getTableComment, reqVO.getTableComment())
                .eqIfPresent(MetaTableDO::getEngine, reqVO.getEngine())
                .orderByDesc(MetaTableDO::getId));
    }

    default MetaTableDO selectByDbIdAndTableName(@Param("dbId") Long dbId, @Param("tableName") String tableName) {
        return selectOne(new LambdaQueryWrapperX<MetaTableDO>()
                .eq(MetaTableDO::getDbId, dbId)
                .eq(MetaTableDO::getTableName, tableName));
    }

    default List<MetaTableDO> selectListByDbId(Long dbId) {
        return selectList(new LambdaQueryWrapperX<MetaTableDO>()
                .eq(MetaTableDO::getDbId, dbId));
    }

    // 批量插入（MyBatis-Plus自带）
    // void insertBatch(@Param("entities") Collection<MetaTableDO> entities); // 实际使用 Mybatis-Plus 的 saveBatch

    // 批量更新（MyBatis-Plus自带）
    // void updateBatch(@Param("entities") Collection<MetaTableDO> entities); // 实际使用 Mybatis-Plus 的 updateBatchById
}