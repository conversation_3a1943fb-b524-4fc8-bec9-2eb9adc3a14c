
package com.data.platform.datamind.server.datameta.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ArcheryInstanceVO {
    private Integer id;
    @JSONField(name = "instance_name")
    private String instanceName;
    @J<PERSON><PERSON>ield(name = "db_type")
    private String dbType;

    @J<PERSON><PERSON><PERSON>(name = "db_name")
    private String dbName;
    private String host;
    private Integer port;
    private String user;
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String password;
    @J<PERSON><PERSON>ield(name = "service_name")
    private String serviceName;
    private List<String> schemaList;

}


/* Location:              E:\Switch-Files\2025年6月\glfiles-0623\meta-sync-server-1.0-SNAPSHOT.jar!\BOOT-INF\classes\com\ls\meta\sync\model\vo\ArcheryInstanceVO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */