package com.data.platform.datamind.server.datameta.service.admin;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:41
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service
 * @description
 * @email <EMAIL>
 * @since 1.8
 */

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabasePageReqVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseUpdateReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据库/数据源元数据 Service 接口
 *
 * <AUTHOR>
 */
public interface MetaDatabaseService {

    /**
     * 创建数据库/数据源元数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDatabase(@Valid MetaDatabaseCreateReqVO createReqVO);

    /**
     * 更新数据库/数据源元数据
     *
     * @param updateReqVO 更新信息
     */
    void updateDatabase(@Valid MetaDatabaseUpdateReqVO updateReqVO);

    /**
     * 删除数据库/数据源元数据
     *
     * @param id 编号
     */
    void deleteDatabase(Long id);

    /**
     * 获得数据库/数据源元数据
     *
     * @param id 编号
     * @return 数据库/数据源元数据
     */
    MetaDatabaseDO getDatabase(Long id);

    /**
     * 获得数据库/数据源元数据列表
     *
     * @param ids 编号列表
     * @return 数据库/数据源元数据列表
     */
    List<MetaDatabaseDO> getDatabaseList(List<Long> ids);

    /**
     * 获得数据库/数据源元数据分页
     *
     * @param pageReqVO 分页查询
     * @return 数据库/数据源元数据分页
     */
    PageResult<MetaDatabaseDO> getDatabasePage(MetaDatabasePageReqVO pageReqVO);

    /**
     * 获得所有数据库/数据源元数据列表
     *
     * @return 数据库/数据源元数据列表
     */
    List<MetaDatabaseDO> getAllDatabases();
}
