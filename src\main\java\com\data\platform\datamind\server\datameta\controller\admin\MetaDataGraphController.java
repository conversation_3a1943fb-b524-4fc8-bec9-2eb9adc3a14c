package com.data.platform.datamind.server.datameta.controller.admin;

import com.data.platform.datamind.framework.common.pojo.CommonResult;
import com.data.platform.datamind.server.datameta.service.admin.MetaDataGraphService;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

import static com.data.platform.datamind.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 元数据知识图谱 Controller
 *
 * <AUTHOR> Team
 */
@Tag(name = "管理后台 - 元数据知识图谱")
@RestController
@RequestMapping("/admin-api/data-meta/graph")
@Validated
@Slf4j
public class MetaDataGraphController {

    @Resource
    private MetaDataGraphService metaDataGraphService;

    @PostMapping("/sync")
    @Operation(summary = "手动触发图谱数据同步")
    @PreAuthorize("@ss.hasPermission('data-meta:graph:sync')")
    public CommonResult<Boolean> syncGraphData() {
        try {
            // 这里需要获取所有元数据并同步到图谱
            // 实际实现中应该从数据库查询最新的元数据
            log.info("Manual graph sync triggered");
            // TODO: 实现手动同步逻辑
            return success(true);
        } catch (Exception e) {
            log.error("Graph sync failed", e);
            return CommonResult.error("图谱同步失败: " + e.getMessage());
        }
    }

    @GetMapping("/table/{tableId}/graph")
    @Operation(summary = "获取表的关系图谱")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<Map<String, Object>> getTableGraph(@PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> graph = metaDataGraphService.getTableGraph(tableId);
        return success(graph);
    }

    @GetMapping("/column/{columnId}/lineage")
    @Operation(summary = "获取列的血缘关系")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<List<LineageNodeVO>> getColumnLineage(@PathVariable("columnId") @NotNull @Positive Long columnId) {
        List<LineageNodeVO> lineage = metaDataGraphService.getColumnLineage(columnId);
        return success(lineage);
    }

    @GetMapping("/table/{tableId}/upstream")
    @Operation(summary = "获取表的上游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<List<TableRelationshipVO>> getUpstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<TableRelationshipVO> upstream = metaDataGraphService.getUpstreamTables(tableId, depth);
        return success(upstream);
    }

    @GetMapping("/table/{tableId}/downstream")
    @Operation(summary = "获取表的下游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<List<TableRelationshipVO>> getDownstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<TableRelationshipVO> downstream = metaDataGraphService.getDownstreamTables(tableId, depth);
        return success(downstream);
    }

    @GetMapping("/database/{databaseId}/overview")
    @Operation(summary = "获取数据库的表关系概览")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<Map<String, Object>> getDatabaseTableOverview(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> overview = metaDataGraphService.getDatabaseTableOverview(databaseId);
        return success(overview);
    }

    @GetMapping("/table/search")
    @Operation(summary = "搜索相关表")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "limit", description = "结果限制", required = false)
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<List<Map<String, Object>>> searchTables(
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        List<Map<String, Object>> results = metaDataGraphService.searchTables(keyword, limit);
        return success(results);
    }

    @GetMapping("/table/{tableId}/impact")
    @Operation(summary = "获取表的影响分析")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<Map<String, Object>> getTableImpactAnalysis(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> impact = metaDataGraphService.getTableImpactAnalysis(tableId);
        return success(impact);
    }

    @GetMapping("/status")
    @Operation(summary = "获取图谱状态信息")
    @PreAuthorize("@ss.hasPermission('data-meta:graph:query')")
    public CommonResult<Map<String, Object>> getGraphStatus() {
        // TODO: 实现图谱状态查询
        Map<String, Object> status = Map.of(
                "connected", true,
                "nodeCount", 0,
                "relationshipCount", 0,
                "lastSyncTime", System.currentTimeMillis()
        );
        return success(status);
    }
}
