# Maven Developer Guide - DataMind Server Data Meta

## Project Overview
This is a Spring Boot-based data metadata management module (`datamind-server-data-meta`) that is part of a larger multi-module Maven project. The project uses CI-friendly versioning with `${revision}` property and includes various database drivers for metadata collection.

## Essential Maven Commands

### 🚀 Basic Build Commands

#### Clean and Compile
```bash
# Clean previous build artifacts
mvn clean

# Compile source code
mvn compile

# Clean and compile in one command
mvn clean compile
```

#### Package and Install
```bash
# Create JAR package
mvn package

# Clean and package
mvn clean package

# Install to local repository
mvn install

# Clean, package, and install
mvn clean install
```

#### Verify and Test
```bash
# Run tests and verify package
mvn verify

# Run tests only
mvn test

# Skip tests during build
mvn clean package -DskipTests

# Skip test compilation and execution
mvn clean package -Dmaven.test.skip=true
```

### 🔧 Development Commands

#### Running the Application
```bash
# Run Spring Boot application
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Run with JVM arguments
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xmx1024m -Xms512m"
```

#### Dependency Management
```bash
# Display dependency tree
mvn dependency:tree

# Display effective POM
mvn help:effective-pom

# Display active profiles
mvn help:active-profiles

# Analyze dependencies for conflicts
mvn dependency:analyze

# Download sources for dependencies
mvn dependency:sources

# Download JavaDoc for dependencies
mvn dependency:resolve -Dclassifier=javadoc
```

### 📊 Code Quality and Analysis

#### Testing Commands
```bash
# Run tests with coverage
mvn clean test

# Run integration tests
mvn clean verify

# Run specific test class
mvn test -Dtest=YourTestClass

# Run specific test method
mvn test -Dtest=YourTestClass#testMethod
```

#### Plugin-Specific Commands
```bash
# Generate site documentation
mvn site

# Run site locally (if maven-site-plugin configured)
mvn site:run

# Check for plugin updates
mvn versions:display-plugin-updates

# Check for dependency updates
mvn versions:display-dependency-updates
```

### 🏗️ Multi-Module Project Commands

Since this is part of a multi-module project, these commands are useful:

```bash
# Build from parent directory
cd .. && mvn clean install

# Build only this module and its dependencies
mvn clean install -pl datamind-server-data-meta -am

# Build this module and modules that depend on it
mvn clean install -pl datamind-server-data-meta -amd

# Resume build from this module after failure
mvn clean install -rf datamind-server-data-meta
```

### 🐳 Docker and Deployment

#### Building Docker Image
```bash
# Build JAR first, then Docker image
mvn clean package
docker build -t datamind-server-data-meta .

# Or use Maven to build Docker image (if docker-maven-plugin configured)
mvn clean package docker:build
```

### 🔍 Debugging and Troubleshooting

#### Verbose Output
```bash
# Debug mode with verbose output
mvn clean install -X

# Show version information
mvn --version

# Help for specific plugin
mvn help:describe -Dplugin=spring-boot

# Show effective settings
mvn help:effective-settings
```

#### Common Issues Resolution
```bash
# Force update snapshots
mvn clean install -U

# Offline mode (use only local repository)
mvn clean install -o

# Skip checksum validation
mvn clean install -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true
```

### ⚙️ Configuration Commands

#### Setting Properties
```bash
# Override revision property (CI-friendly versioning)
mvn clean package -Drevision=1.0.0-SNAPSHOT

# Set specific Spring profile
mvn spring-boot:run -Dspring.profiles.active=dev

# Override database configuration
mvn spring-boot:run -Dspring.datasource.url=************************************
```

### 📝 Documentation and Reporting

```bash
# Generate project information
mvn project-info-reports:index

# Generate dependency report
mvn project-info-reports:dependencies

# Generate plugin information
mvn project-info-reports:plugins
```

## Project-Specific Notes

### Dependencies
- **Spring Boot**: Main framework with auto-configuration
- **Nacos**: Service discovery and configuration management
- **Neo4j**: Graph database for metadata relationships
- **Multiple DB Drivers**: MySQL, PostgreSQL, Oracle, SQL Server for metadata collection
- **MapStruct**: Bean mapping
- **TestContainers**: Integration testing with containers

### Build Configuration
- **Final Name**: `${project.artifactId}` (datamind-server-data-meta)
- **Packaging**: JAR with Spring Boot repackaging
- **Parent**: Uses `${revision}` for CI-friendly versioning

### Common Development Workflow
1. `mvn clean compile` - Verify code compiles
2. `mvn test` - Run unit tests
3. `mvn clean package` - Create deployable JAR
4. `mvn spring-boot:run` - Test locally
5. `mvn clean install` - Install to local repo for other modules

## Maven Configuration Files

### .mvn/maven.config (Project-level Maven options)
```
-T3
-U
--fail-at-end
```

### .mvn/jvm.config (JVM options for Maven)
```
-Xmx2048m -Xms1024m -XX:MaxPermSize=512m -Djava.awt.headless=true
```

## Tips and Best Practices

1. **Use CI-friendly versioning**: This project uses `${revision}` property
2. **Parallel builds**: Use `-T` flag for faster builds on multi-core systems
3. **Offline development**: Use `-o` flag when working without internet
4. **Profile-specific builds**: Use `-P` flag to activate specific profiles
5. **Incremental builds**: Use `-pl` and `-am` flags for multi-module efficiency

## Advanced Maven Commands

### 🔄 Lifecycle and Phase Management

#### Custom Phase Execution
```bash
# Execute up to specific phase
mvn compile
mvn test-compile
mvn process-resources
mvn generate-sources

# Execute specific goals
mvn compiler:compile
mvn surefire:test
mvn jar:jar
```

#### Plugin Goal Execution
```bash
# Execute specific plugin goal
mvn spring-boot:build-info
mvn spring-boot:repackage

# Execute with specific configuration
mvn exec:java -Dexec.mainClass="com.data.platform.Application"
mvn exec:java@second-cli  # Execute specific execution ID
```

### 🛠️ Advanced Dependency Management

#### Dependency Analysis
```bash
# Find unused dependencies
mvn dependency:analyze

# Copy dependencies to target/dependency
mvn dependency:copy-dependencies

# Resolve all dependencies
mvn dependency:resolve

# Display dependency updates
mvn versions:display-dependency-updates

# Update dependencies to latest versions
mvn versions:use-latest-versions
```

#### Repository Management
```bash
# Install file to local repository
mvn install:install-file \
  -DgroupId=com.example \
  -DartifactId=custom-lib \
  -Dversion=1.0.0 \
  -Dpackaging=jar \
  -Dfile=path/to/custom-lib.jar

# Deploy to remote repository
mvn deploy:deploy-file \
  -DgroupId=com.example \
  -DartifactId=custom-lib \
  -Dversion=1.0.0 \
  -Dpackaging=jar \
  -Dfile=path/to/custom-lib.jar \
  -Durl=http://repo.example.com/maven2 \
  -DrepositoryId=example-repo
```

### 🚨 Troubleshooting Commands

#### Build Issues
```bash
# Force re-download of dependencies
mvn clean install -U

# Build with maximum verbosity
mvn clean install -X -e

# Build without transfer progress (cleaner output)
mvn clean install --no-transfer-progress

# Build with specific Maven version validation
mvn -Dmaven.plugin.validation=verbose clean install
```

#### Memory and Performance
```bash
# Increase Maven memory
export MAVEN_OPTS="-Xmx2048m -Xms1024m -XX:MaxPermSize=512m"
mvn clean install

# Parallel builds (use number of CPU cores)
mvn clean install -T 4

# Build with specific thread count
mvn clean install -T 1C  # 1 thread per CPU core
```

#### Dependency Conflicts
```bash
# Exclude specific transitive dependencies
mvn dependency:tree -Dverbose

# Find dependency conflicts
mvn dependency:analyze-duplicate

# Resolve version conflicts
mvn versions:resolve-ranges
```

### 📋 Project Information Commands

#### Project Analysis
```bash
# Show effective POM (resolved)
mvn help:effective-pom

# Show effective settings
mvn help:effective-settings

# Describe plugin details
mvn help:describe -Dplugin=spring-boot -Dfull=true

# List available profiles
mvn help:all-profiles

# Evaluate expressions
mvn help:evaluate -Dexpression=project.version -q -DforceStdout
```

### 🔐 Security and Validation

#### Plugin Validation
```bash
# Enable plugin validation
mvn -Dmaven.plugin.validation=verbose clean install

# Exclude plugins from validation
mvn -Dmaven.plugin.validation.excludes=plugin1-groupId:plugin1-artifactId clean install
```

#### Checksum Verification
```bash
# Verify checksums
mvn clean install -Dmaven.wagon.http.ssl.insecure=false

# Skip checksum verification (not recommended)
mvn clean install -Dmaven.wagon.http.ssl.insecure=true
```

## Environment-Specific Configurations

### Development Environment
```bash
# Development build with debug info
mvn clean compile -Dmaven.compiler.debug=true -Dmaven.compiler.debuglevel=lines,vars,source

# Run with development profile
mvn spring-boot:run -Dspring.profiles.active=dev -Dlogging.level.com.data.platform=DEBUG
```

### Production Environment
```bash
# Production build (optimized)
mvn clean package -Dmaven.test.skip=true -Dspring.profiles.active=prod

# Create production-ready JAR
mvn clean package -Pproduction
```

### CI/CD Environment
```bash
# CI build with version override
mvn clean deploy -Drevision=${BUILD_NUMBER}-SNAPSHOT

# Release build
mvn clean deploy -Drevision=${RELEASE_VERSION} -Dchangelist=
```

## Common Error Solutions

### Build Failures
```bash
# Clean corrupted local repository
rm -rf ~/.m2/repository/com/data/platform
mvn clean install

# Resolve plugin resolution issues
mvn clean install -Dmaven.plugin.validation=NONE

# Fix encoding issues
mvn clean install -Dproject.build.sourceEncoding=UTF-8
```

### Memory Issues
```bash
# Increase heap size
export MAVEN_OPTS="-Xmx4g -Xms1g"

# Increase PermGen (Java 7 and below)
export MAVEN_OPTS="-XX:MaxPermSize=512m"

# Use G1 garbage collector
export MAVEN_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

## Integration with IDEs

### IntelliJ IDEA
- Import as Maven project
- Use Maven tool window for command execution
- Configure Maven runner settings in Preferences

### Eclipse
- Import as Existing Maven Project
- Use Run Configurations for custom Maven goals
- Install m2e plugin for better integration

### VS Code
- Install Extension Pack for Java
- Use Command Palette: "Java: Run Maven Commands"
- Configure tasks.json for custom build tasks

## Useful Maven Properties

```bash
# Project information
mvn help:evaluate -Dexpression=project.groupId -q -DforceStdout
mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout
mvn help:evaluate -Dexpression=project.version -q -DforceStdout

# Build information
mvn help:evaluate -Dexpression=project.build.directory -q -DforceStdout
mvn help:evaluate -Dexpression=project.build.finalName -q -DforceStdout

# System properties
mvn help:evaluate -Dexpression=java.version -q -DforceStdout
mvn help:evaluate -Dexpression=maven.version -q -DforceStdout
```

For more detailed Maven documentation, visit: https://maven.apache.org/guides/
