package com.data.platform.datamind.server.datameta.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
/*    */ public class ArcheryConfig {
    @Value("${archery.username:}")
    private String username;

    public void setUsername(String username) {
        this.username = username;
    }

    @Value("${archery.password}")
    private String password;

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof ArcheryConfig)) return false;
        ArcheryConfig other = (ArcheryConfig) o;
        if (!other.canEqual(this)) return false;
        Object this$username = getUsername(), other$username = other.getUsername();
        if ((this$username == null) ? (other$username != null) : !this$username.equals(other$username)) return false;
        Object this$password = getPassword(), other$password = other.getPassword();
        return !((this$password == null) ? (other$password != null) : !this$password.equals(other$password));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ArcheryConfig;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $username = getUsername();
        result = result * 59 + (($username == null) ? 43 : $username.hashCode());
        Object $password = getPassword();
        return result * 59 + (($password == null) ? 43 : $password.hashCode());
    }

    public String toString() {
        return "ArcheryConfig(username=" + getUsername() + ", password=" + getPassword() + ")";
    }

    public String getUsername() {
        return this.username;
    }

    public String getPassword() {
        return this.password;
    }
}


/* Location:              E:\Switch-Files\2025年6月\glfiles-0623\meta-sync-server-1.0-SNAPSHOT.jar!\BOOT-INF\classes\com\ls\meta\sync\config\ArcheryConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */