package com.data.platform.datamind.server.datameta.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@TableName("database_basic_info")
public class DatabaseBasicInfoDTO {
    @TableId("ID")
    private Integer id;
    @TableField("MICRO_APP_ID")
    private Integer microAppId;

    @TableField("DB_TYPE")
    private String dbType;

    @TableField("ACCESS_ADDRESS")
    private String accessAddress;

    @TableField("INSTANCE_NAME")
    private String instanceName;

    @TableField("DB_NAME")
    private String dbName;

    @TableField("STATUS")
    private String status;

    @TableField("INSTANCE_ID")
    private String instanceId;

    @TableField("DB_PORT")
    private String dbPort;
    @TableField("DB_USER")
    private String dbUser;
    @TableField("INST_TYPE")
    private String instType;
    @TableField("apiflag")
    private String apiflag;
    @TableField("SOURCE")
    private String source;
    @TableField("SYNC_FLAG")
    private String syncFlag;
    @TableField("SYNC_MSG")
    private String syncMsg;
    @TableField("SYNC_TIME")
    private Date syncTime;

}
