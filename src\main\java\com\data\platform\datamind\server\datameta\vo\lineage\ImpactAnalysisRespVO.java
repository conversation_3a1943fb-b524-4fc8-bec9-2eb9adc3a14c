package com.data.platform.datamind.server.datameta.vo.lineage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 影响分析响应 VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "影响分析结果")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImpactAnalysisRespVO {

    @Schema(description = "分析ID", example = "impact_001")
    private String analysisId;

    @Schema(description = "目标对象信息")
    private TargetObject targetObject;

    @Schema(description = "变更类型", example = "DELETE")
    private String changeType;

    @Schema(description = "影响等级", example = "HIGH")
    private String impactLevel;

    @Schema(description = "风险等级", example = "CRITICAL")
    private String riskLevel;

    @Schema(description = "影响评分（0-100）", example = "85")
    private Integer impactScore;

    @Schema(description = "受影响的直接下游对象")
    private List<LineageNodeVO> directImpactedObjects;

    @Schema(description = "受影响的间接下游对象")
    private List<LineageNodeVO> indirectImpactedObjects;

    @Schema(description = "影响统计信息")
    private ImpactStatistics impactStatistics;

    @Schema(description = "影响链路分析")
    private List<DataFlowPathVO> impactChains;

    @Schema(description = "变更建议")
    private List<String> changeRecommendations;

    @Schema(description = "风险缓解措施")
    private List<String> riskMitigationActions;

    @Schema(description = "最佳变更时间窗口")
    private TimeWindow bestChangeWindow;

    @Schema(description = "影响分析详情")
    private Map<String, Object> analysisDetails;

    @Schema(description = "分析时间", example = "2025-07-05 10:30:00")
    private String analysisTime;

    @Schema(description = "分析耗时（毫秒）", example = "1250")
    private Long analysisDuration;

    /**
     * 目标对象信息
     */
    @Data
    @Schema(description = "目标对象信息")
    public static class TargetObject {
        @Schema(description = "对象ID", example = "1")
        private Long objectId;

        @Schema(description = "对象类型", example = "TABLE")
        private String objectType;

        @Schema(description = "对象名称", example = "user_info")
        private String objectName;

        @Schema(description = "对象描述", example = "用户信息表")
        private String objectDescription;

        @Schema(description = "数据库ID", example = "1")
        private Long databaseId;

        @Schema(description = "数据库名称", example = "user_center")
        private String databaseName;
    }

    /**
     * 影响统计信息
     */
    @Data
    @Schema(description = "影响统计信息")
    public static class ImpactStatistics {
        @Schema(description = "直接影响对象数量", example = "5")
        private Integer directImpactCount;

        @Schema(description = "间接影响对象数量", example = "12")
        private Integer indirectImpactCount;

        @Schema(description = "总影响对象数量", example = "17")
        private Integer totalImpactCount;

        @Schema(description = "影响深度", example = "3")
        private Integer impactDepth;

        @Schema(description = "影响广度", example = "8")
        private Integer impactBreadth;

        @Schema(description = "关键路径数量", example = "2")
        private Integer criticalPathCount;

        @Schema(description = "受影响的数据库数量", example = "3")
        private Integer impactedDatabaseCount;

        @Schema(description = "受影响的表数量", example = "15")
        private Integer impactedTableCount;

        @Schema(description = "受影响的列数量", example = "45")
        private Integer impactedColumnCount;
    }

    /**
     * 时间窗口信息
     */
    @Data
    @Schema(description = "时间窗口信息")
    public static class TimeWindow {
        @Schema(description = "建议开始时间", example = "2025-07-06 02:00:00")
        private String suggestedStartTime;

        @Schema(description = "建议结束时间", example = "2025-07-06 04:00:00")
        private String suggestedEndTime;

        @Schema(description = "时间窗口类型", example = "LOW_TRAFFIC")
        private String windowType;

        @Schema(description = "时间窗口描述", example = "低流量时段，适合进行变更操作")
        private String windowDescription;

        @Schema(description = "预计影响时长（分钟）", example = "30")
        private Integer estimatedImpactDuration;
    }
}
