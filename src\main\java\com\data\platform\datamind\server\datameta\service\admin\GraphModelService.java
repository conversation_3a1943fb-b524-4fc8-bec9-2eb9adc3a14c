package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.entity.dto.GraphNodeDTO;
import com.data.platform.datamind.server.datameta.entity.dto.GraphRelationshipDTO;

import java.util.List;
import java.util.Map;

/**
 * 图谱模型服务接口
 * 负责将关系型元数据转换为图谱模型
 *
 * <AUTHOR> Team
 */
public interface GraphModelService {

    /**
     * 构建数据库节点
     *
     * @param database 数据库DO对象
     * @return 图谱节点DTO
     */
    GraphNodeDTO buildDatabaseNode(MetaDatabaseDO database);

    /**
     * 构建表节点
     *
     * @param table 表DO对象
     * @return 图谱节点DTO
     */
    GraphNodeDTO buildTableNode(MetaTableDO table);

    /**
     * 构建列节点
     *
     * @param column 列DO对象
     * @return 图谱节点DTO
     */
    GraphNodeDTO buildColumnNode(MetaColumnDO column);

    /**
     * 构建数据库到表的关系
     *
     * @param databaseId 数据库ID
     * @param tableId 表ID
     * @return 图谱关系DTO
     */
    GraphRelationshipDTO buildDatabaseTableRelationship(Long databaseId, Long tableId);

    /**
     * 构建表到列的关系
     *
     * @param tableId 表ID
     * @param columnId 列ID
     * @return 图谱关系DTO
     */
    GraphRelationshipDTO buildTableColumnRelationship(Long tableId, Long columnId);

    /**
     * 构建表间关系（外键关系）
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @param relationshipType 关系类型
     * @param properties 关系属性
     * @return 图谱关系DTO
     */
    GraphRelationshipDTO buildTableRelationship(Long sourceTableId, Long targetTableId, 
                                               String relationshipType, Map<String, Object> properties);

    /**
     * 批量构建数据库节点
     *
     * @param databases 数据库列表
     * @return 图谱节点列表
     */
    List<GraphNodeDTO> buildDatabaseNodes(List<MetaDatabaseDO> databases);

    /**
     * 批量构建表节点
     *
     * @param tables 表列表
     * @return 图谱节点列表
     */
    List<GraphNodeDTO> buildTableNodes(List<MetaTableDO> tables);

    /**
     * 批量构建列节点
     *
     * @param columns 列列表
     * @return 图谱节点列表
     */
    List<GraphNodeDTO> buildColumnNodes(List<MetaColumnDO> columns);

    /**
     * 构建完整的元数据图谱模型
     *
     * @param databases 数据库列表
     * @param tables 表列表
     * @param columns 列列表
     * @return 图谱模型（包含节点和关系）
     */
    Map<String, Object> buildCompleteGraphModel(List<MetaDatabaseDO> databases, 
                                               List<MetaTableDO> tables, 
                                               List<MetaColumnDO> columns);

    /**
     * 构建数据库的图谱模型
     *
     * @param databaseId 数据库ID
     * @return 数据库图谱模型
     */
    Map<String, Object> buildDatabaseGraphModel(Long databaseId);

    /**
     * 构建表的图谱模型
     *
     * @param tableId 表ID
     * @return 表图谱模型
     */
    Map<String, Object> buildTableGraphModel(Long tableId);

    /**
     * 验证图谱模型的完整性
     *
     * @param graphModel 图谱模型
     * @return 验证结果
     */
    Map<String, Object> validateGraphModel(Map<String, Object> graphModel);

    /**
     * 优化图谱模型结构
     *
     * @param graphModel 原始图谱模型
     * @return 优化后的图谱模型
     */
    Map<String, Object> optimizeGraphModel(Map<String, Object> graphModel);

    /**
     * 生成图谱统计信息
     *
     * @param graphModel 图谱模型
     * @return 统计信息
     */
    Map<String, Object> generateGraphStats(Map<String, Object> graphModel);

    /**
     * 导出图谱模型为JSON格式
     *
     * @param graphModel 图谱模型
     * @return JSON字符串
     */
    String exportGraphModelToJson(Map<String, Object> graphModel);

    /**
     * 从JSON导入图谱模型
     *
     * @param jsonString JSON字符串
     * @return 图谱模型
     */
    Map<String, Object> importGraphModelFromJson(String jsonString);
}
