package com.data.platform.datamind.server.datameta.dal.dataobject;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.data.platform.datamind.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.io.Serializable;

/**
 * 列元数据;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-6-23
 */
@TableName("S_META_COLUMN")
@KeySequence("meta_database_seq") // 用于 Oracle、PostgreSQL 等的序列
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetaColumnDO extends BaseDO implements Serializable, Cloneable {
    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 列名
     */
    private String columnName;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 关联 meta_table.id
     */
    private Long tableId;
    /**
     * 列数据类型（如varchar, int, datetime）
     */
    private String columnType;
    /**
     * 原始数据类型（如int(11), varchar(255)）
     */
    private String dataType;
    /**
     * 列注释
     */
    private String columnComment;
    /**
     * 键类型（如pri为主键, mul为索引, uni为唯一）
     */
    private String columnKey;
    /**
     * 是否允许为空 (0:否, 1:是)
     */
    private Boolean isNullable;
    /**
     * 默认值
     */
    private String columnDefault;
    /**
     * 额外信息（如auto_increment）
     */
    private String extra;
    /**
     * 是否主键 (0:否, 1:是)
     */
    private Boolean isPrimaryKey;
    /**
     * 是否外键 (0:否, 1:是)
     */
    private Boolean isForeignKey;
    /**
     * 外键关联的表id (关联meta_table.id)
     */
    private Long fkTableId;
    /**
     * 外键关联的列id (关联meta_column.id)
     */
    private Long fkColumnId;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 表用户
     */
    private String tableOwner;
    /**
     *
     */
    private Long distinctNum;
    /** 创建者 */
//    private String creator ;
//    /** 创建时间 */
//    private Date createTime ;
//    /** 更新者 */
//    private String updater ;
//    /** 更新时间 */
//    private Date updateTime ;
//    /** 逻辑删除 (0:未删除, 1:已删除) */
//    private Boolean deleted ;

}