package com.data.platform.datamind.server.datameta.vo.column;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:51
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo.column
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 列元数据 Base VO
 */
@Data
public class MetaColumnBaseVO {

    @Schema(description = "关联 meta_table.id", requiredMode = Schema.RequiredMode.REQUIRED, example = "101")
    @NotNull(message = "表ID不能为空")
    private Long tableId;

    @Schema(description = "列名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "user_id")
    @NotEmpty(message = "列名称不能为空")
    private String columnName;

    @Schema(description = "列数据类型（如VARCHAR, INT）", requiredMode = Schema.RequiredMode.REQUIRED, example = "VARCHAR")
    @NotEmpty(message = "列数据类型不能为空")
    private String columnType;

    @Schema(description = "原始数据类型（如int(11), varchar(255)）", example = "varchar(64)")
    private String dataType;

    @Schema(description = "列注释/业务描述", example = "用户ID")
    private String columnComment;

    @Schema(description = "键类型（PRI, MUL, UNI）", example = "PRI")
    private String columnKey;

    @Schema(description = "是否允许为空 (0:否, 1:是)", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    @NotNull(message = "是否允许为空不能为空")
    private Boolean isNullable;

    @Schema(description = "默认值", example = "NULL")
    private String columnDefault;

    @Schema(description = "额外信息（如auto_increment）", example = "auto_increment")
    private String extra;

    @Schema(description = "是否主键 (0:否, 1:是)", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否主键不能为空")
    private Boolean isPrimaryKey;

    @Schema(description = "是否外键 (0:否, 1:是)", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    @NotNull(message = "是否外键不能为空")
    private Boolean isForeignKey;

    @Schema(description = "外键关联的表ID", example = "100")
    private Long fkTableId;

    @Schema(description = "外键关联的列ID", example = "101")
    private Long fkColumnId;
}
