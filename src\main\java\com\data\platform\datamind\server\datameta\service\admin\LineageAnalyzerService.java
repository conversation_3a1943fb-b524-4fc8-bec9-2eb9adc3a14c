package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;

import java.util.List;
import java.util.Map;

/**
 * 血缘分析服务接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/6
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 血缘分析服务，提供数据血缘分析功能
 * @since 1.8
 */
public interface LineageAnalyzerService {

    /**
     * 分析表的血缘关系
     *
     * @param tableId 表ID
     * @return 表血缘关系信息
     */
    TableLineageRespVO analyzeTableLineage(Long tableId);

    /**
     * 分析表的上游血缘关系
     *
     * @param tableId 表ID
     * @param depth 分析深度
     * @return 上游血缘节点列表
     */
    List<LineageNodeVO> analyzeUpstreamLineage(Long tableId, Integer depth);

    /**
     * 分析表的下游血缘关系
     *
     * @param tableId 表ID
     * @param depth 分析深度
     * @return 下游血缘节点列表
     */
    List<LineageNodeVO> analyzeDownstreamLineage(Long tableId, Integer depth);

    /**
     * 分析列的血缘关系
     *
     * @param columnId 列ID
     * @param depth 分析深度
     * @return 列血缘节点列表
     */
    List<LineageNodeVO> analyzeColumnLineage(Long columnId, Integer depth);

    /**
     * 分析两个表之间的血缘路径
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 血缘路径
     */
    List<LineageNodeVO> analyzeLineagePath(Long sourceTableId, Long targetTableId);

    /**
     * 分析数据库的完整血缘关系
     *
     * @param databaseId 数据库ID
     * @return 数据库血缘关系图
     */
    Map<String, Object> analyzeDatabaseLineage(Long databaseId);

    /**
     * 获取表的直接依赖关系
     *
     * @param tableId 表ID
     * @return 直接依赖的表列表
     */
    List<LineageNodeVO> getDirectDependencies(Long tableId);

    /**
     * 获取表的直接被依赖关系
     *
     * @param tableId 表ID
     * @return 直接被依赖的表列表
     */
    List<LineageNodeVO> getDirectDependents(Long tableId);

    /**
     * 分析表的影响范围
     *
     * @param tableId 表ID
     * @return 影响范围分析结果
     */
    Map<String, Object> analyzeImpactScope(Long tableId);

    /**
     * 分析列的影响范围
     *
     * @param columnId 列ID
     * @return 影响范围分析结果
     */
    Map<String, Object> analyzeColumnImpactScope(Long columnId);

    /**
     * 构建血缘关系图
     *
     * @param tableIds 表ID列表
     * @return 血缘关系图数据
     */
    Map<String, Object> buildLineageGraph(List<Long> tableIds);

    /**
     * 搜索血缘关系
     *
     * @param keyword 搜索关键词
     * @param searchType 搜索类型（TABLE/COLUMN）
     * @return 搜索结果
     */
    List<LineageNodeVO> searchLineage(String keyword, String searchType);

    /**
     * 获取血缘统计信息
     *
     * @param databaseId 数据库ID（可选）
     * @return 血缘统计信息
     */
    Map<String, Object> getLineageStatistics(Long databaseId);

    /**
     * 验证血缘关系的完整性
     *
     * @param tableId 表ID
     * @return 验证结果
     */
    Map<String, Object> validateLineageIntegrity(Long tableId);

    /**
     * 刷新表的血缘关系
     *
     * @param tableId 表ID
     */
    void refreshTableLineage(Long tableId);

    /**
     * 批量刷新血缘关系
     *
     * @param tableIds 表ID列表
     */
    void batchRefreshLineage(List<Long> tableIds);

    /**
     * 导出血缘关系数据
     *
     * @param tableId 表ID
     * @param format 导出格式（JSON/CSV/XML）
     * @return 导出数据
     */
    String exportLineageData(Long tableId, String format);

    /**
     * 获取血缘关系变更历史
     *
     * @param tableId 表ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 变更历史
     */
    List<Map<String, Object>> getLineageChangeHistory(Long tableId, Long startTime, Long endTime);

    /**
     * 分析血缘关系的复杂度
     *
     * @param tableId 表ID
     * @return 复杂度分析结果
     */
    Map<String, Object> analyzeLineageComplexity(Long tableId);

    /**
     * 获取血缘关系的可视化数据
     *
     * @param tableId 表ID
     * @param depth 可视化深度
     * @return 可视化数据
     */
    Map<String, Object> getLineageVisualizationData(Long tableId, Integer depth);
}
