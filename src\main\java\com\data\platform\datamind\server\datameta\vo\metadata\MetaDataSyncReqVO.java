package com.data.platform.datamind.server.datameta.vo.metadata;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 元数据同步请求 VO
 */
@Schema(description = "Web端 - 元数据同步 Request VO")
@Data
public class MetaDataSyncReqVO {

    @Schema(description = "数据库配置ID列表（为空则同步所有）", example = "[1, 2, 3]")
    private List<Long> databaseIds;

    @Schema(description = "是否强制全量同步", example = "false")
    private Boolean forceFullSync = false;

    @Schema(description = "是否同步到图谱", example = "true")
    private Boolean syncToGraph = true;

    @Schema(description = "同步类型", example = "INCREMENTAL")
    private SyncType syncType = SyncType.INCREMENTAL;

    /**
     * 同步类型枚举
     */
    public enum SyncType {
        /**
         * 增量同步
         */
        INCREMENTAL,
        /**
         * 全量同步
         */
        FULL
    }
}
