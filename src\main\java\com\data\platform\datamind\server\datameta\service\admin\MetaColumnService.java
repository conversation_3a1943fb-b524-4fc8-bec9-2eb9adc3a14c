package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnPageReqVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnUpdateReqVO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 列元数据 Service 接口
 *
 * <AUTHOR>
 */
public interface MetaColumnService {

    Long createColumn(@Valid MetaColumnCreateReqVO createReqVO);

    void updateColumn(@Valid MetaColumnUpdateReqVO updateReqVO);

    void deleteColumn(Long id);

    MetaColumnDO getColumn(Long id);

    List<MetaColumnDO> getColumnList(Collection<Long> ids);

    PageResult<MetaColumnDO> getColumnPage(MetaColumnPageReqVO pageReqVO);

    List<MetaColumnDO> getColumnsByTableId(Long tableId);

    MetaColumnDO getColumnByTableIdAndColumnName(Long tableId, String columnName);

    Long getColumnIdByTableIdAndColumnName(Long tableId, String columnName);

    /**
     * 批量插入列元数据
     * @param columns 列列表
     */
    void batchInsert(List<MetaColumnDO> columns);

    /**
     * 批量更新列元数据
     * @param columns 列列表
     */
    void batchUpdate(List<MetaColumnDO> columns);

    /**
     * 根据表ID和列名删除旧的列数据 (用于同步时清理)
     * @param tableId 表ID
     * @param columnNames 要删除的列名列表
     */
    void deleteColumnsByTableIdAndColumnNames(Long tableId, List<String> columnNames);

    /**
     * 获取所有列数据（不分页）
     * @return 所有列DO列表
     */
    List<MetaColumnDO> getAllColumns();

    /**
     * 根据数据库ID获取所有列（用于图谱同步）
     * @param dbId 数据库ID
     * @return 对应数据库下的所有列
     */
    List<MetaColumnDO> getColumnsByDbId(Long dbId);
}
