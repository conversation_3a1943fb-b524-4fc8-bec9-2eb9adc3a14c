package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;

import java.util.List;

/**
 * 元数据知识图谱 Service 接口
 * 负责将关系型元数据同步到 Neo4j 图谱，并提供图谱查询能力
 *
 */
public interface MetaDataGraphService {

    /**
     * 全量同步所有元数据到 Neo4j 图谱
     *
     * @param databases 所有数据库配置DO列表
     * @param tables 所有表DO列表
     * @param columns 所有列DO列表
     */
    void syncAllMetaDataToGraph(List<MetaDatabaseDO> databases,
                                List<MetaTableDO> tables,
                                List<MetaColumnDO> columns);

    /**
     * 获取表的关系图谱
     *
     * @param tableId 表ID
     * @return 表关系图谱数据
     */
    Map<String, Object> getTableGraph(Long tableId);

    /**
     * 获取列的血缘关系
     *
     * @param columnId 列ID
     * @return 列血缘关系
     */
    List<com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO> getColumnLineage(Long columnId);

    /**
     * 获取表的上游依赖
     *
     * @param tableId 表ID
     * @param depth 查询深度
     * @return 上游依赖表列表
     */
    List<com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO> getUpstreamTables(Long tableId, int depth);

    /**
     * 获取表的下游依赖
     *
     * @param tableId 表ID
     * @param depth 查询深度
     * @return 下游依赖表列表
     */
    List<com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO> getDownstreamTables(Long tableId, int depth);

    /**
     * 获取数据库的表关系概览
     *
     * @param databaseId 数据库ID
     * @return 表关系概览
     */
    Map<String, Object> getDatabaseTableOverview(Long databaseId);

    /**
     * 搜索相关表
     *
     * @param keyword 搜索关键词
     * @param limit 结果限制
     * @return 搜索结果
     */
    List<Map<String, Object>> searchTables(String keyword, int limit);

    /**
     * 获取表的影响分析
     *
     * @param tableId 表ID
     * @return 影响分析结果
     */
    Map<String, Object> getTableImpactAnalysis(Long tableId);
}
