package com.data.platform.datamind.server.datameta.infrans.enumeration;

import com.data.platform.datamind.framework.common.exception.ErrorCode;
import com.data.platform.datamind.framework.common.exception.enums.GlobalErrorCodeConstants;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:29
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.infrans.enumeration
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
public interface MetaDataErrorCodeConstants extends GlobalErrorCodeConstants {

    ErrorCode METADATABASE_NOT_EXISTS = new ErrorCode(1000001000, "数据库不存在");
    ErrorCode METADATABASE_NAME_DUPLICATE = new ErrorCode(1000001001, "数据库名称重复");
    ErrorCode METATABLE_NOT_EXISTS = new ErrorCode(1000002000, "表不存在");
    ErrorCode METATABLE_NAME_DUPLICATE = new ErrorCode(1000002001, "表名称在同一数据库下重复");
    ErrorCode METACOLUMN_NOT_EXISTS = new ErrorCode(1000003000, "列不存在");
    ErrorCode METACOLUMN_NAME_DUPLICATE = new ErrorCode(1000003001, "列名称在同一表下重复");

    ErrorCode METADATA_COLLECT_FAILED = new ErrorCode(1000004000, "元数据采集失败");
    ErrorCode METADATA_SYNC_GRAPH_FAILED = new ErrorCode(1000004001, "元数据同步图谱失败");



}
