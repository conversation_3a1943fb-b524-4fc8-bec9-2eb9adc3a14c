package com.data.platform.datamind.server.datameta.dal.dataobject;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.data.platform.datamind.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:13
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.dal.dataobject
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
@TableName("S_META_DATABASE")
@KeySequence("meta_database_seq") // 用于 Oracle、PostgreSQL 等的序列
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetaDatabaseDO extends BaseDO implements Serializable, Cloneable {
    /**
     * 唯一标识,自增
     */
    @TableId
    private Long id;
    /**
     * 微应用id
     */
    private Integer microAppId;
    /**
     * 数据库类型
     */
    private String type;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     *
     */
    private String instType;
    /**
     * 访问地址url
     */
    private String accessAddress;
    /**
     * 实例名称
     */
    private String instanceName;
    /**
     * 状态
     */
    private String status;
    /**
     * 数据库名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     *
     */
    private String host;
    /**
     *
     */
    private String port;
    /**
     *
     */
    private String username;
    /**
     *
     */
    private String password;
    /**
     *
     */
    private String apiflag;
    /**
     *
     */
    private String instanceMd5;
    /**
     * 00 虚拟库 01 archery
     */
    private String source;
    /**
     * 同步标志 00 同步中 01 同步完成 02 同步失败
     */
    private String syncFlag;
    /**
     * 同步失败消息
     */
    private String syncMsg;
    /**
     * 同步完成
     */
    private Date syncTime;
    /**
     * 数据库所属区域
     */
    private String regionFlag;
    /** 创建者 */
//    private String creator ;
//    /** 创建时间 */
//    private Date createTime ;
//    /** 更新者 */
//    private String updater ;
//    /** 更新时间 */
//    private Date updateTime ;
//    /** 逻辑删除 (0:未删除, 1:已删除) */
//    private Boolean deleted ;
}
