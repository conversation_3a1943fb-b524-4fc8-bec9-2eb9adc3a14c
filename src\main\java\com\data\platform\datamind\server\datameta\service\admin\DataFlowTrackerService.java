package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.vo.lineage.DataFlowPathVO;
import com.data.platform.datamind.server.datameta.vo.lineage.DataFlowNodeVO;

import java.util.List;
import java.util.Map;

/**
 * 数据流追踪服务接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/6
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 数据流追踪服务，提供数据流向分析功能
 * @since 1.8
 */
public interface DataFlowTrackerService {

    /**
     * 追踪表级数据流
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 数据流路径
     */
    DataFlowPathVO trackTableDataFlow(Long sourceTableId, Long targetTableId);

    /**
     * 追踪列级数据流
     *
     * @param sourceColumnId 源列ID
     * @param targetColumnId 目标列ID
     * @return 数据流路径
     */
    DataFlowPathVO trackColumnDataFlow(Long sourceColumnId, Long targetColumnId);

    /**
     * 分析表的数据流入
     *
     * @param tableId 表ID
     * @param depth 分析深度
     * @return 数据流入节点列表
     */
    List<DataFlowNodeVO> analyzeDataInflow(Long tableId, Integer depth);

    /**
     * 分析表的数据流出
     *
     * @param tableId 表ID
     * @param depth 分析深度
     * @return 数据流出节点列表
     */
    List<DataFlowNodeVO> analyzeDataOutflow(Long tableId, Integer depth);

    /**
     * 分析列的数据流入
     *
     * @param columnId 列ID
     * @param depth 分析深度
     * @return 数据流入节点列表
     */
    List<DataFlowNodeVO> analyzeColumnDataInflow(Long columnId, Integer depth);

    /**
     * 分析列的数据流出
     *
     * @param columnId 列ID
     * @param depth 分析深度
     * @return 数据流出节点列表
     */
    List<DataFlowNodeVO> analyzeColumnDataOutflow(Long columnId, Integer depth);

    /**
     * 构建数据流图
     *
     * @param rootTableId 根表ID
     * @param maxDepth 最大深度
     * @return 数据流图
     */
    Map<String, Object> buildDataFlowGraph(Long rootTableId, Integer maxDepth);

    /**
     * 分析数据流的性能影响
     *
     * @param tableId 表ID
     * @return 性能影响分析结果
     */
    Map<String, Object> analyzeDataFlowPerformance(Long tableId);

    /**
     * 检测数据流中的循环依赖
     *
     * @param tableId 表ID
     * @return 循环依赖检测结果
     */
    List<List<Long>> detectDataFlowCycles(Long tableId);

    /**
     * 分析数据流的质量
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 数据流质量分析结果
     */
    Map<String, Object> analyzeDataFlowQuality(Long sourceTableId, Long targetTableId);

    /**
     * 获取数据流的统计信息
     *
     * @param tableId 表ID
     * @return 数据流统计信息
     */
    Map<String, Object> getDataFlowStatistics(Long tableId);

    /**
     * 追踪数据变更的传播路径
     *
     * @param sourceTableId 源表ID
     * @param changeType 变更类型
     * @return 变更传播路径
     */
    List<DataFlowNodeVO> trackDataChangePropagation(Long sourceTableId, String changeType);

    /**
     * 分析数据流的实时性
     *
     * @param tableId 表ID
     * @return 实时性分析结果
     */
    Map<String, Object> analyzeDataFlowLatency(Long tableId);

    /**
     * 获取数据流的瓶颈分析
     *
     * @param tableId 表ID
     * @return 瓶颈分析结果
     */
    Map<String, Object> analyzeDataFlowBottlenecks(Long tableId);

    /**
     * 模拟数据流变更的影响
     *
     * @param tableId 表ID
     * @param changeScenario 变更场景
     * @return 影响模拟结果
     */
    Map<String, Object> simulateDataFlowImpact(Long tableId, Map<String, Object> changeScenario);

    /**
     * 优化数据流路径
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 优化建议
     */
    List<String> optimizeDataFlowPath(Long sourceTableId, Long targetTableId);

    /**
     * 监控数据流的健康状态
     *
     * @param tableId 表ID
     * @return 健康状态报告
     */
    Map<String, Object> monitorDataFlowHealth(Long tableId);

    /**
     * 生成数据流报告
     *
     * @param tableId 表ID
     * @param reportType 报告类型
     * @return 数据流报告
     */
    Map<String, Object> generateDataFlowReport(Long tableId, String reportType);

    /**
     * 比较两个数据流路径
     *
     * @param path1 路径1
     * @param path2 路径2
     * @return 比较结果
     */
    Map<String, Object> compareDataFlowPaths(DataFlowPathVO path1, DataFlowPathVO path2);

    /**
     * 验证数据流的完整性
     *
     * @param tableId 表ID
     * @return 完整性验证结果
     */
    Map<String, Object> validateDataFlowIntegrity(Long tableId);

    /**
     * 获取数据流的历史记录
     *
     * @param tableId 表ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 历史记录
     */
    List<Map<String, Object>> getDataFlowHistory(Long tableId, Long startTime, Long endTime);
}
