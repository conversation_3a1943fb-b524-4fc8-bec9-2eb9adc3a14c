package com.data.platform.datamind.server.datameta.dal.dataobject;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.data.platform.datamind.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.io.Serializable;

/**
 * 记录表的元数据;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-6-23
 */
@TableName("S_META_TABLE")
@KeySequence("meta_database_seq") // 用于 Oracle、PostgreSQL 等的序列
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetaTableDO extends BaseDO implements Serializable, Cloneable {
    /**
     * 唯一标识,自增
     */
    @TableId
    private Long id;
    /**
     *
     */
    private String dbType;
    /**
     * 数据库元数据id
     */
    private Long dbId;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 表注释/业务描述
     */
    private String tableComment;
    /**
     * 表用户
     */
    private String tableOwner;
    /**
     * 表行数
     */
    private Long tableRows;
    /**
     * 表数据大小（字节）
     */
    private Long dataLength;
    /**
     *
     */
    private Integer partitionNum;
    /**
     * 存储引擎
     */
    private String engine;
    /**
     * 字符集
     */
    private String charset;
    /** 创建者 */
//    private String creator ;
//    /** 创建时间 */
//    private Date createTime ;
//    /** 更新者 */
//    private String updater ;
//    /** 更新时间 */
//    private Date updateTime ;
//    /** 逻辑删除 (0:未删除, 1:已删除) */
//    private Boolean deleted ;
    /**
     * archery url
     */
    private String archUrl;
    /**
     * archery id
     */
    private Integer archId;

}
