package com.data.platform.datamind.server.datameta.dal.dataobject;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.data.platform.datamind.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.io.Serializable;

/**
 * 索引元数据 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.dal.dataobject
 * @description 记录表的索引元数据信息
 * @email <EMAIL>
 * @since 1.8
 */
@TableName("S_META_INDEX")
@KeySequence("meta_index_seq") // 用于 Oracle、PostgreSQL 等的序列
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetaIndexDO extends BaseDO implements Serializable, Cloneable {
    
    /**
     * 唯一标识,自增
     */
    @TableId
    private Long id;
    
    /**
     * 关联 meta_table.id
     */
    private Long tableId;
    
    /**
     * 关联 meta_database.id
     */
    private Long dbId;
    
    /**
     * 实例id
     */
    private String instanceId;
    
    /**
     * 索引名称
     */
    private String indexName;
    
    /**
     * 索引类型（PRIMARY, UNIQUE, INDEX, FULLTEXT等）
     */
    private String indexType;
    
    /**
     * 列名
     */
    private String columnName;
    
    /**
     * 列在索引中的位置
     */
    private Integer columnPosition;
    
    /**
     * 是否唯一索引 (0:否, 1:是)
     */
    private Boolean isUnique;
    
    /**
     * 是否主键 (0:否, 1:是)
     */
    private Boolean isPrimary;
    
    /**
     * 索引注释
     */
    private String indexComment;
    
    /**
     * 索引基数（唯一值数量）
     */
    private Long cardinality;
    
    /**
     * 索引长度
     */
    private Integer indexLength;
    
    /**
     * 排序方式（ASC, DESC）
     */
    private String sortOrder;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 表所有者/模式
     */
    private String tableOwner;
}
