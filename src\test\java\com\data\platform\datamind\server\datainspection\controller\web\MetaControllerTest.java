package com.data.platform.datamind.server.datainspection.controller.web;

import com.data.platform.datamind.server.datameta.controller.web.MetaController;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncReqVO;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncRespVO;
import com.data.platform.datamind.server.datameta.vo.quality.DataQualityReportRespVO;
import com.data.platform.datamind.server.datameta.service.web.MetaDataEnhancedService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * MetaController 单元测试
 */
@WebMvcTest(MetaController.class)
class MetaControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MetaDataEnhancedService metaDataEnhancedService;

    @Autowired
    private ObjectMapper objectMapper;

    private MetaDataSyncRespVO mockSyncRespVO;
    private TableLineageRespVO mockLineageRespVO;
    private DataQualityReportRespVO mockQualityReportRespVO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockSyncRespVO = new MetaDataSyncRespVO();
        mockSyncRespVO.setSyncTaskId("sync_test_001");
        mockSyncRespVO.setStatus(MetaDataSyncRespVO.SyncStatus.SUCCESS);
        mockSyncRespVO.setStartTime(LocalDateTime.now().minusMinutes(5));
        mockSyncRespVO.setEndTime(LocalDateTime.now());
        mockSyncRespVO.setDuration(300000L);
        mockSyncRespVO.setDatabaseCount(1);
        mockSyncRespVO.setTableCount(10);
        mockSyncRespVO.setColumnCount(100);

        mockLineageRespVO = new TableLineageRespVO();
        TableLineageRespVO.TableInfo targetTable = new TableLineageRespVO.TableInfo();
        targetTable.setTableId(1L);
        targetTable.setTableName("user_info");
        targetTable.setTableComment("用户信息表");
        targetTable.setDatabaseId(1L);
        targetTable.setDatabaseName("test_db");
        mockLineageRespVO.setTargetTable(targetTable);
        mockLineageRespVO.setUpstreamTables(Collections.emptyList());
        mockLineageRespVO.setDownstreamTables(Collections.emptyList());
        mockLineageRespVO.setRelations(Collections.emptyList());

        mockQualityReportRespVO = new DataQualityReportRespVO();
        mockQualityReportRespVO.setReportTime(LocalDateTime.now());
        mockQualityReportRespVO.setOverallScore(85.5);
        mockQualityReportRespVO.setQualityLevel(DataQualityReportRespVO.QualityLevel.GOOD);
        
        DataQualityReportRespVO.DatabaseQuality dbQuality = new DataQualityReportRespVO.DatabaseQuality();
        dbQuality.setDatabaseId(1L);
        dbQuality.setDatabaseName("test_db");
        dbQuality.setQualityScore(85.5);
        dbQuality.setQualityLevel(DataQualityReportRespVO.QualityLevel.GOOD);
        dbQuality.setTableCount(10);
        dbQuality.setColumnCount(100);
        dbQuality.setIssueCount(5);
        mockQualityReportRespVO.setDatabaseQualities(Arrays.asList(dbQuality));

        DataQualityReportRespVO.QualityIssueSummary issueSummary = new DataQualityReportRespVO.QualityIssueSummary();
        issueSummary.setTotalIssues(5);
        issueSummary.setHighPriorityIssues(1);
        issueSummary.setMediumPriorityIssues(2);
        issueSummary.setLowPriorityIssues(2);
        mockQualityReportRespVO.setIssueSummary(issueSummary);
        mockQualityReportRespVO.setQualityTrends(Collections.emptyList());
    }

    @Test
    void testSyncMetaData_Success() throws Exception {
        // 准备测试数据
        MetaDataSyncReqVO reqVO = new MetaDataSyncReqVO();
        reqVO.setSyncType(MetaDataSyncReqVO.SyncType.INCREMENTAL);
        reqVO.setSyncToGraph(true);

        when(metaDataEnhancedService.syncMetaData(any(MetaDataSyncReqVO.class)))
                .thenReturn(mockSyncRespVO);

        // 执行测试
        mockMvc.perform(post("/meta/metadata/sync")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.syncTaskId").value("sync_test_001"))
                .andExpect(jsonPath("$.data.status").value("SUCCESS"))
                .andExpect(jsonPath("$.data.databaseCount").value(1))
                .andExpect(jsonPath("$.data.tableCount").value(10))
                .andExpect(jsonPath("$.data.columnCount").value(100));
    }

    @Test
    void testSyncMetaData_WithSpecificDatabases() throws Exception {
        // 准备测试数据
        MetaDataSyncReqVO reqVO = new MetaDataSyncReqVO();
        reqVO.setDatabaseIds(Arrays.asList(1L, 2L));
        reqVO.setSyncType(MetaDataSyncReqVO.SyncType.FULL);
        reqVO.setSyncToGraph(false);

        when(metaDataEnhancedService.syncMetaData(any(MetaDataSyncReqVO.class)))
                .thenReturn(mockSyncRespVO);

        // 执行测试
        mockMvc.perform(post("/meta/metadata/sync")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.syncTaskId").value("sync_test_001"));
    }

    @Test
    void testGetTableLineage_Success() throws Exception {
        // 准备测试数据
        Long tableId = 1L;

        when(metaDataEnhancedService.getTableLineage(tableId))
                .thenReturn(mockLineageRespVO);

        // 执行测试
        mockMvc.perform(get("/meta/lineage/{tableId}", tableId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.targetTable.tableId").value(1))
                .andExpect(jsonPath("$.data.targetTable.tableName").value("user_info"))
                .andExpect(jsonPath("$.data.targetTable.tableComment").value("用户信息表"))
                .andExpect(jsonPath("$.data.targetTable.databaseName").value("test_db"));
    }

    @Test
    void testGetTableLineage_InvalidTableId() throws Exception {
        // 准备测试数据
        Long tableId = 999L;

        when(metaDataEnhancedService.getTableLineage(tableId))
                .thenThrow(new IllegalArgumentException("Table not found with id: 999"));

        // 执行测试
        mockMvc.perform(get("/meta/lineage/{tableId}", tableId))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void testGetDataQualityReport_Success() throws Exception {
        when(metaDataEnhancedService.getDataQualityReport())
                .thenReturn(mockQualityReportRespVO);

        // 执行测试
        mockMvc.perform(get("/meta/quality/report"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.overallScore").value(85.5))
                .andExpect(jsonPath("$.data.qualityLevel").value("GOOD"))
                .andExpect(jsonPath("$.data.databaseQualities").isArray())
                .andExpect(jsonPath("$.data.databaseQualities[0].databaseName").value("test_db"))
                .andExpect(jsonPath("$.data.databaseQualities[0].qualityScore").value(85.5))
                .andExpect(jsonPath("$.data.databaseQualities[0].tableCount").value(10))
                .andExpect(jsonPath("$.data.databaseQualities[0].columnCount").value(100))
                .andExpect(jsonPath("$.data.issueSummary.totalIssues").value(5))
                .andExpect(jsonPath("$.data.issueSummary.highPriorityIssues").value(1));
    }

    @Test
    void testGetDataQualityReportByDatabaseId_Success() throws Exception {
        // 准备测试数据
        Long databaseId = 1L;

        when(metaDataEnhancedService.getDataQualityReport(databaseId))
                .thenReturn(mockQualityReportRespVO);

        // 执行测试
        mockMvc.perform(get("/meta/quality/report/{databaseId}", databaseId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.overallScore").value(85.5))
                .andExpect(jsonPath("$.data.qualityLevel").value("GOOD"))
                .andExpect(jsonPath("$.data.databaseQualities").isArray())
                .andExpect(jsonPath("$.data.databaseQualities[0].databaseId").value(1));
    }

    @Test
    void testGetDataQualityReportByDatabaseId_DatabaseNotFound() throws Exception {
        // 准备测试数据
        Long databaseId = 999L;

        when(metaDataEnhancedService.getDataQualityReport(databaseId))
                .thenThrow(new IllegalArgumentException("Database not found with id: 999"));

        // 执行测试
        mockMvc.perform(get("/meta/quality/report/{databaseId}", databaseId))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void testSyncMetaData_InvalidRequest() throws Exception {
        // 准备无效的请求数据
        String invalidJson = "{}";

        // 执行测试
        mockMvc.perform(post("/meta/metadata/sync")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(invalidJson))
                .andExpect(status().isOk()); // 由于所有字段都有默认值，所以请求仍然有效
    }

    @Test
    void testSyncMetaData_EmptyDatabaseIds() throws Exception {
        // 准备测试数据
        MetaDataSyncReqVO reqVO = new MetaDataSyncReqVO();
        reqVO.setDatabaseIds(Collections.emptyList());
        reqVO.setSyncType(MetaDataSyncReqVO.SyncType.INCREMENTAL);

        when(metaDataEnhancedService.syncMetaData(any(MetaDataSyncReqVO.class)))
                .thenReturn(mockSyncRespVO);

        // 执行测试
        mockMvc.perform(post("/meta/metadata/sync")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    void testGetTableLineage_ZeroTableId() throws Exception {
        // 准备测试数据
        Long tableId = 0L;

        when(metaDataEnhancedService.getTableLineage(tableId))
                .thenThrow(new IllegalArgumentException("Table not found with id: 0"));

        // 执行测试
        mockMvc.perform(get("/meta/lineage/{tableId}", tableId))
                .andExpect(status().isInternalServerError());
    }
}
