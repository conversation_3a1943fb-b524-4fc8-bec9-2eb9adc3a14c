package com.data.platform.datamind.server.datameta.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class MetaTableInfoDTO {

    private String tabName;
    private String tabOwner;
    private Integer instanceId;
    private Long recordNum;
    private Integer partitionNum;
    private String tabComment;
    private Date updateTime;

}
