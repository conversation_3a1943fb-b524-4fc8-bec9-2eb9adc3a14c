package com.data.platform.datamind.server.datameta.convert;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexRespVO;
import com.data.platform.datamind.server.datameta.vo.index.MetaIndexUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 索引元数据 Convert
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.convert
 * @description 索引元数据转换器
 * @email <EMAIL>
 * @since 1.8
 */
@Mapper
public interface MetaIndexConvert {

    MetaIndexConvert INSTANCE = Mappers.getMapper(MetaIndexConvert.class);

    MetaIndexDO convert(MetaIndexCreateReqVO bean);

    MetaIndexDO convert(MetaIndexUpdateReqVO bean);

    MetaIndexRespVO convert(MetaIndexDO bean);

    List<MetaIndexRespVO> convertList(List<MetaIndexDO> list);

    PageResult<MetaIndexRespVO> convertPage(PageResult<MetaIndexDO> page);

    // 批量转换方法
    List<MetaIndexDO> convertList(List<MetaIndexCreateReqVO> list);

    List<MetaIndexDO> convertUpdateList(List<MetaIndexUpdateReqVO> list);
}
