<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.data.platform</groupId>
        <artifactId>datamind</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>datamind-server-data-meta</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        数据元数据管理模块
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>com.data.platform</groupId>
            <artifactId>datamind-module-system-server</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.data.platform</groupId>
            <artifactId>datamind-module-infra-server</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 会员中心。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-member-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 数据报表。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-report-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 工作流。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-bpm-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 支付服务。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-pay-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 微信公众号模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-mp-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 商城相关模块。默认注释，保证编译速度-->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-product-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-promotion-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-trade-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-statistics-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- CRM 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-crm-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- ERP 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-erp-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- AI 大模型相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-ai-server</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- IoT 物联网相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.data.platform</groupId>-->
<!--            <artifactId>datamind-module-iot-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.data.platform</groupId>
            <artifactId>datamind-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.data.platform</groupId>
            <artifactId>datamind-spring-boot-starter-rpc</artifactId>
            <!-- 目的：datamind-server 单体启动，禁用 openfeign -->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-openfeign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-neo4j</artifactId>
        </dependency>

        <!-- MapStruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Database Drivers for Metadata Collection -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Testing Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mysql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>neo4j</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
