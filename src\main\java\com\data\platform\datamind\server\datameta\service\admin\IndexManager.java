package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaIndexInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * 索引管理器接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 索引管理器，提供索引和约束的统一管理功能
 * @email <EMAIL>
 * @since 1.8
 */
public interface IndexManager {

    /**
     * 同步表的索引信息
     * @param table 表信息
     * @param indexInfoList 索引信息列表
     */
    void syncTableIndexes(MetaTableDO table, List<MetaIndexInfoDTO> indexInfoList);

    /**
     * 分析表的索引结构
     * @param tableId 表ID
     * @return 索引分析结果
     */
    Map<String, Object> analyzeTableIndexes(Long tableId);

    /**
     * 获取表的主键信息
     * @param tableId 表ID
     * @return 主键列表
     */
    List<MetaIndexDO> getTablePrimaryKeys(Long tableId);

    /**
     * 获取表的外键信息
     * @param tableId 表ID
     * @return 外键列表
     */
    List<MetaIndexDO> getTableForeignKeys(Long tableId);

    /**
     * 获取表的唯一约束
     * @param tableId 表ID
     * @return 唯一约束列表
     */
    List<MetaIndexDO> getTableUniqueConstraints(Long tableId);

    /**
     * 获取表的普通索引
     * @param tableId 表ID
     * @return 普通索引列表
     */
    List<MetaIndexDO> getTableNormalIndexes(Long tableId);

    /**
     * 检查索引健康状况
     * @param tableId 表ID
     * @return 健康检查结果
     */
    Map<String, Object> checkIndexHealth(Long tableId);

    /**
     * 建议索引优化
     * @param tableId 表ID
     * @return 优化建议
     */
    List<String> suggestIndexOptimization(Long tableId);

    /**
     * 批量同步数据库的索引信息
     * @param dbId 数据库ID
     * @param indexInfoList 索引信息列表
     */
    void batchSyncDatabaseIndexes(Long dbId, List<MetaIndexInfoDTO> indexInfoList);

    /**
     * 清理表的索引信息
     * @param tableId 表ID
     */
    void cleanTableIndexes(Long tableId);

    /**
     * 清理数据库的索引信息
     * @param dbId 数据库ID
     */
    void cleanDatabaseIndexes(Long dbId);

    /**
     * 获取索引统计信息
     * @param dbId 数据库ID
     * @return 统计信息
     */
    Map<String, Object> getIndexStatistics(Long dbId);

    /**
     * 验证索引一致性
     * @param tableId 表ID
     * @return 验证结果
     */
    boolean validateIndexConsistency(Long tableId);

    /**
     * 重建表的索引信息
     * @param tableId 表ID
     */
    void rebuildTableIndexes(Long tableId);
}
