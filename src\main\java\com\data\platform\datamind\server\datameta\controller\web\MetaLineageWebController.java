package com.data.platform.datamind.server.datameta.controller.web;

import com.data.platform.datamind.server.datameta.lineage.DataFlowTracker;
import com.data.platform.datamind.server.datameta.lineage.ImpactAnalyzer;
import com.data.platform.datamind.server.datameta.lineage.LineageAnalyzer;
import com.data.platform.datamind.server.datameta.lineage.LineageVisualizer;
import com.data.platform.datamind.server.datameta.vo.lineage.DataFlowPathVO;
import com.data.platform.datamind.server.datameta.vo.lineage.ImpactAnalysisRespVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * Web端 - 数据血缘分析控制器
 *
 * <AUTHOR> Team
 */
@Tag(name = "Web端 - 数据血缘分析")
@RestController
@RequestMapping("/web-api/data-meta/lineage")
@Validated
@Slf4j
public class MetaLineageWebController {

    @Resource
    private LineageAnalyzer lineageAnalyzer;

    @Resource
    private DataFlowTracker dataFlowTracker;

    @Resource
    private LineageVisualizer lineageVisualizer;

    @Resource
    private ImpactAnalyzer impactAnalyzer;

    // ==================== 血缘查询接口 ====================

    @GetMapping("/table/{tableId}")
    @Operation(summary = "获取表的血缘关系")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "分析深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<TableLineageRespVO> getTableLineage(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        TableLineageRespVO lineage = lineageAnalyzer.analyzeTableLineage(tableId, depth);
        return success(lineage);
    }

    @GetMapping("/column/{columnId}")
    @Operation(summary = "获取列的血缘关系")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @Parameter(name = "depth", description = "分析深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getColumnLineage(
            @PathVariable("columnId") @NotNull @Positive Long columnId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> lineage = lineageAnalyzer.analyzeColumnLineage(columnId, depth);
        return success(lineage);
    }

    @GetMapping("/upstream/table/{tableId}")
    @Operation(summary = "获取表的上游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getUpstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> upstream = lineageAnalyzer.getUpstreamTables(tableId, depth);
        return success(upstream);
    }

    @GetMapping("/downstream/table/{tableId}")
    @Operation(summary = "获取表的下游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getDownstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> downstream = lineageAnalyzer.getDownstreamTables(tableId, depth);
        return success(downstream);
    }

    @GetMapping("/path/table")
    @Operation(summary = "分析表级血缘路径")
    @Parameter(name = "sourceTableId", description = "源表ID", required = true)
    @Parameter(name = "targetTableId", description = "目标表ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> analyzeTableLineagePath(
            @RequestParam("sourceTableId") @NotNull @Positive Long sourceTableId,
            @RequestParam("targetTableId") @NotNull @Positive Long targetTableId) {
        List<LineageNodeVO> path = lineageAnalyzer.analyzeLineagePath(sourceTableId, targetTableId);
        return success(path);
    }

    @GetMapping("/statistics/table/{tableId}")
    @Operation(summary = "获取表的血缘统计信息")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getTableLineageStatistics(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> statistics = lineageAnalyzer.getLineageStatistics(tableId);
        return success(statistics);
    }

    @GetMapping("/statistics/database/{databaseId}")
    @Operation(summary = "获取数据库的血缘统计信息")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getDatabaseLineageStatistics(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> statistics = lineageAnalyzer.getDatabaseLineageStatistics(databaseId);
        return success(statistics);
    }

    // ==================== 数据流追踪接口 ====================

    @GetMapping("/dataflow/table")
    @Operation(summary = "追踪表级数据流")
    @Parameter(name = "sourceTableId", description = "源表ID", required = true)
    @Parameter(name = "targetTableId", description = "目标表ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<DataFlowPathVO> trackTableDataFlow(
            @RequestParam("sourceTableId") @NotNull @Positive Long sourceTableId,
            @RequestParam("targetTableId") @NotNull @Positive Long targetTableId) {
        DataFlowPathVO dataFlow = dataFlowTracker.trackTableDataFlow(sourceTableId, targetTableId);
        return success(dataFlow);
    }

    @GetMapping("/dataflow/inflow/table/{tableId}")
    @Operation(summary = "获取表的数据流入路径")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "追踪深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<DataFlowPathVO>> getDataInflowPaths(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<DataFlowPathVO> inflowPaths = dataFlowTracker.getDataInflowPaths(tableId, depth);
        return success(inflowPaths);
    }

    @GetMapping("/dataflow/outflow/table/{tableId}")
    @Operation(summary = "获取表的数据流出路径")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "追踪深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<DataFlowPathVO>> getDataOutflowPaths(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<DataFlowPathVO> outflowPaths = dataFlowTracker.getDataOutflowPaths(tableId, depth);
        return success(outflowPaths);
    }

    @GetMapping("/dataflow/statistics/table/{tableId}")
    @Operation(summary = "获取表的数据流统计信息")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getDataFlowStatistics(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> statistics = dataFlowTracker.getDataFlowStatistics(tableId);
        return success(statistics);
    }

    // ==================== 可视化接口 ====================

    @GetMapping("/visualization/table/{tableId}")
    @Operation(summary = "生成表血缘可视化数据")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "可视化深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateTableLineageVisualization(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "2") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateTableLineageVisualization(tableId, depth);
        return success(visualization);
    }

    @GetMapping("/visualization/column/{columnId}")
    @Operation(summary = "生成列血缘可视化数据")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @Parameter(name = "depth", description = "可视化深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateColumnLineageVisualization(
            @PathVariable("columnId") @NotNull @Positive Long columnId,
            @RequestParam(value = "depth", defaultValue = "2") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateColumnLineageVisualization(columnId, depth);
        return success(visualization);
    }

    @GetMapping("/visualization/database/{databaseId}")
    @Operation(summary = "生成数据库血缘全景图")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateDatabaseLineageOverview(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> overview = lineageVisualizer.generateDatabaseLineageOverview(databaseId);
        return success(overview);
    }

    @GetMapping("/visualization/path/table")
    @Operation(summary = "生成表血缘路径可视化数据")
    @Parameter(name = "sourceTableId", description = "源表ID", required = true)
    @Parameter(name = "targetTableId", description = "目标表ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateLineagePathVisualization(
            @RequestParam("sourceTableId") @NotNull @Positive Long sourceTableId,
            @RequestParam("targetTableId") @NotNull @Positive Long targetTableId) {
        Map<String, Object> visualization = lineageVisualizer.generateLineagePathVisualization(sourceTableId, targetTableId);
        return success(visualization);
    }

    @GetMapping("/visualization/dataflow/{tableId}")
    @Operation(summary = "生成数据流可视化数据")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "数据流深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateDataFlowVisualization(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "2") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateDataFlowVisualization(tableId, depth);
        return success(visualization);
    }

    @GetMapping("/visualization/heatmap/database/{databaseId}")
    @Operation(summary = "生成数据库血缘热力图")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateLineageHeatmap(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> heatmap = lineageVisualizer.generateLineageHeatmap(databaseId);
        return success(heatmap);
    }

    @GetMapping("/visualization/statistics/database/{databaseId}")
    @Operation(summary = "生成血缘统计图表数据")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateLineageStatisticsChart(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> chart = lineageVisualizer.generateLineageStatisticsChart(databaseId);
        return success(chart);
    }

    // ==================== 影响分析接口 ====================

    @GetMapping("/impact/preview/table/{tableId}")
    @Operation(summary = "预览表变更的影响")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "changeType", description = "变更类型", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<ImpactAnalysisRespVO> previewTableImpact(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam("changeType") @NotNull String changeType) {
        ImpactAnalysisRespVO impact = impactAnalyzer.analyzeTableImpact(tableId, changeType);
        return success(impact);
    }

    @GetMapping("/impact/score/table/{tableId}")
    @Operation(summary = "获取表的影响范围评分")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Integer> getTableImpactScore(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Integer score = impactAnalyzer.calculateImpactScore(tableId);
        return success(score);
    }

    @GetMapping("/impact/visualization/{tableId}")
    @Operation(summary = "生成影响分析可视化数据")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "影响分析深度", required = false)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateImpactAnalysisVisualization(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateImpactAnalysisVisualization(tableId, depth);
        return success(visualization);
    }

    // ==================== 质量和报告接口 ====================

    @GetMapping("/quality/table/{tableId}")
    @Operation(summary = "获取表血缘质量评分")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getTableLineageQuality(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> quality = lineageAnalyzer.getLineageQualityScore(tableId);
        return success(quality);
    }

    @GetMapping("/report/database/{databaseId}")
    @Operation(summary = "生成数据库血缘报告")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateLineageReport(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> report = lineageVisualizer.generateLineageReport(databaseId);
        return success(report);
    }

    @GetMapping("/options")
    @Operation(summary = "获取可视化配置选项")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getVisualizationOptions() {
        Map<String, Object> options = lineageVisualizer.getVisualizationOptions();
        return success(options);
    }
}
