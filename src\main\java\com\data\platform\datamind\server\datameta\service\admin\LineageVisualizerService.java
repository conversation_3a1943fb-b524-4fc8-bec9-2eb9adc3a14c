package com.data.platform.datamind.server.datameta.service.admin;

import java.util.List;
import java.util.Map;

/**
 * 血缘可视化服务接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/6
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.service.admin
 * @description 血缘可视化服务，提供血缘关系可视化功能
 * @since 1.8
 */
public interface LineageVisualizerService {

    /**
     * 生成表血缘可视化数据
     *
     * @param tableId 表ID
     * @param depth 可视化深度
     * @return 可视化数据
     */
    Map<String, Object> generateTableLineageVisualization(Long tableId, Integer depth);

    /**
     * 生成列血缘可视化数据
     *
     * @param columnId 列ID
     * @param depth 可视化深度
     * @return 可视化数据
     */
    Map<String, Object> generateColumnLineageVisualization(Long columnId, Integer depth);

    /**
     * 生成数据库血缘可视化数据
     *
     * @param databaseId 数据库ID
     * @return 可视化数据
     */
    Map<String, Object> generateDatabaseLineageVisualization(Long databaseId);

    /**
     * 生成血缘关系图的节点数据
     *
     * @param tableIds 表ID列表
     * @return 节点数据
     */
    List<Map<String, Object>> generateLineageNodes(List<Long> tableIds);

    /**
     * 生成血缘关系图的边数据
     *
     * @param tableIds 表ID列表
     * @return 边数据
     */
    List<Map<String, Object>> generateLineageEdges(List<Long> tableIds);

    /**
     * 生成血缘关系的层次结构数据
     *
     * @param rootTableId 根表ID
     * @param maxDepth 最大深度
     * @return 层次结构数据
     */
    Map<String, Object> generateLineageHierarchy(Long rootTableId, Integer maxDepth);

    /**
     * 生成血缘关系的力导向图数据
     *
     * @param tableIds 表ID列表
     * @return 力导向图数据
     */
    Map<String, Object> generateForceDirectedGraph(List<Long> tableIds);

    /**
     * 生成血缘关系的树形图数据
     *
     * @param rootTableId 根表ID
     * @param direction 方向（upstream/downstream/both）
     * @param depth 深度
     * @return 树形图数据
     */
    Map<String, Object> generateLineageTree(Long rootTableId, String direction, Integer depth);

    /**
     * 生成血缘关系的桑基图数据
     *
     * @param sourceTableIds 源表ID列表
     * @param targetTableIds 目标表ID列表
     * @return 桑基图数据
     */
    Map<String, Object> generateSankeyDiagram(List<Long> sourceTableIds, List<Long> targetTableIds);

    /**
     * 生成血缘关系的网络图数据
     *
     * @param centerTableId 中心表ID
     * @param radius 半径（层数）
     * @return 网络图数据
     */
    Map<String, Object> generateNetworkGraph(Long centerTableId, Integer radius);

    /**
     * 生成血缘路径的可视化数据
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 路径可视化数据
     */
    Map<String, Object> generatePathVisualization(Long sourceTableId, Long targetTableId);

    /**
     * 生成血缘关系的时间轴数据
     *
     * @param tableId 表ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间轴数据
     */
    Map<String, Object> generateLineageTimeline(Long tableId, Long startTime, Long endTime);

    /**
     * 生成血缘关系的热力图数据
     *
     * @param databaseId 数据库ID
     * @return 热力图数据
     */
    Map<String, Object> generateLineageHeatmap(Long databaseId);

    /**
     * 生成血缘关系的统计图表数据
     *
     * @param databaseId 数据库ID
     * @param chartType 图表类型
     * @return 统计图表数据
     */
    Map<String, Object> generateLineageStatisticsChart(Long databaseId, String chartType);

    /**
     * 自定义血缘可视化布局
     *
     * @param tableIds 表ID列表
     * @param layoutType 布局类型
     * @param layoutParams 布局参数
     * @return 自定义布局数据
     */
    Map<String, Object> customizeLineageLayout(List<Long> tableIds, String layoutType, Map<String, Object> layoutParams);

    /**
     * 生成血缘关系的3D可视化数据
     *
     * @param databaseId 数据库ID
     * @return 3D可视化数据
     */
    Map<String, Object> generate3DLineageVisualization(Long databaseId);

    /**
     * 生成血缘关系的交互式图表配置
     *
     * @param tableId 表ID
     * @param interactionType 交互类型
     * @return 交互式图表配置
     */
    Map<String, Object> generateInteractiveChartConfig(Long tableId, String interactionType);

    /**
     * 导出血缘可视化数据
     *
     * @param tableId 表ID
     * @param format 导出格式（SVG/PNG/PDF/JSON）
     * @return 导出数据
     */
    String exportVisualizationData(Long tableId, String format);

    /**
     * 生成血缘关系的缩略图
     *
     * @param tableId 表ID
     * @param thumbnailSize 缩略图尺寸
     * @return 缩略图数据
     */
    Map<String, Object> generateLineageThumbnail(Long tableId, String thumbnailSize);

    /**
     * 生成血缘关系的动画数据
     *
     * @param tableId 表ID
     * @param animationType 动画类型
     * @return 动画数据
     */
    Map<String, Object> generateLineageAnimation(Long tableId, String animationType);

    /**
     * 优化可视化性能
     *
     * @param visualizationData 可视化数据
     * @param optimizationLevel 优化级别
     * @return 优化后的可视化数据
     */
    Map<String, Object> optimizeVisualizationPerformance(Map<String, Object> visualizationData, String optimizationLevel);

    /**
     * 验证可视化数据的完整性
     *
     * @param visualizationData 可视化数据
     * @return 验证结果
     */
    Map<String, Object> validateVisualizationData(Map<String, Object> visualizationData);

    /**
     * 生成可视化数据的元信息
     *
     * @param visualizationData 可视化数据
     * @return 元信息
     */
    Map<String, Object> generateVisualizationMetadata(Map<String, Object> visualizationData);

    /**
     * 合并多个可视化数据
     *
     * @param visualizationDataList 可视化数据列表
     * @return 合并后的可视化数据
     */
    Map<String, Object> mergeVisualizationData(List<Map<String, Object>> visualizationDataList);

    /**
     * 过滤可视化数据
     *
     * @param visualizationData 可视化数据
     * @param filterCriteria 过滤条件
     * @return 过滤后的可视化数据
     */
    Map<String, Object> filterVisualizationData(Map<String, Object> visualizationData, Map<String, Object> filterCriteria);
}
