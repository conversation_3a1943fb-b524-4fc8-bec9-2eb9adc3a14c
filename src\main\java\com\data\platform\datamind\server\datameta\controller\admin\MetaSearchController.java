package com.data.platform.datamind.server.datameta.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.service.admin.MetaSearchService;
import com.data.platform.datamind.server.datameta.vo.search.MetaAdvancedSearchReqVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaSearchReqVO;
import com.data.platform.datamind.server.datameta.vo.search.MetaSearchResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 元数据搜索控制器
 *
 * <AUTHOR> Team
 */
@Tag(name = "管理后台 - 元数据搜索")
@RestController
@RequestMapping("/admin-api/data-meta/search")
@Validated
@Slf4j
public class MetaSearchController {

    @Resource
    private MetaSearchService metaSearchService;

    // ==================== 基础搜索接口 ====================

    @PostMapping("/full-text")
    @Operation(summary = "全文搜索元数据")
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<PageResult<MetaSearchResultVO>> fullTextSearch(@Valid @RequestBody MetaSearchReqVO reqVO) {
        PageResult<MetaSearchResultVO> result = metaSearchService.fullTextSearch(reqVO);
        return success(result);
    }

    @PostMapping("/advanced")
    @Operation(summary = "高级搜索元数据")
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<PageResult<MetaSearchResultVO>> advancedSearch(@Valid @RequestBody MetaAdvancedSearchReqVO reqVO) {
        PageResult<MetaSearchResultVO> result = metaSearchService.advancedSearch(reqVO);
        return success(result);
    }

    @GetMapping("/databases")
    @Operation(summary = "搜索数据库")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "sortBy", description = "排序字段", required = false)
    @Parameter(name = "sortOrder", description = "排序方向", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<MetaSearchResultVO>> searchDatabases(
            @RequestParam("keyword") @NotBlank String keyword,
            @RequestParam(value = "sortBy", defaultValue = "relevance") String sortBy,
            @RequestParam(value = "sortOrder", defaultValue = "DESC") String sortOrder) {
        List<MetaSearchResultVO> results = metaSearchService.searchDatabases(keyword, sortBy, sortOrder);
        return success(results);
    }

    @GetMapping("/tables")
    @Operation(summary = "搜索表")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "databaseId", description = "数据库ID", required = false)
    @Parameter(name = "sortBy", description = "排序字段", required = false)
    @Parameter(name = "sortOrder", description = "排序方向", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<MetaSearchResultVO>> searchTables(
            @RequestParam("keyword") @NotBlank String keyword,
            @RequestParam(value = "databaseId", required = false) Long databaseId,
            @RequestParam(value = "sortBy", defaultValue = "relevance") String sortBy,
            @RequestParam(value = "sortOrder", defaultValue = "DESC") String sortOrder) {
        List<MetaSearchResultVO> results = metaSearchService.searchTables(keyword, databaseId, sortBy, sortOrder);
        return success(results);
    }

    @GetMapping("/columns")
    @Operation(summary = "搜索列")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "tableId", description = "表ID", required = false)
    @Parameter(name = "databaseId", description = "数据库ID", required = false)
    @Parameter(name = "sortBy", description = "排序字段", required = false)
    @Parameter(name = "sortOrder", description = "排序方向", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<MetaSearchResultVO>> searchColumns(
            @RequestParam("keyword") @NotBlank String keyword,
            @RequestParam(value = "tableId", required = false) Long tableId,
            @RequestParam(value = "databaseId", required = false) Long databaseId,
            @RequestParam(value = "sortBy", defaultValue = "relevance") String sortBy,
            @RequestParam(value = "sortOrder", defaultValue = "DESC") String sortOrder) {
        List<MetaSearchResultVO> results = metaSearchService.searchColumns(keyword, tableId, databaseId, sortBy, sortOrder);
        return success(results);
    }

    // ==================== 专项搜索接口 ====================

    @PostMapping("/by-tags")
    @Operation(summary = "按标签搜索")
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<MetaSearchResultVO>> searchByTags(
            @RequestParam("tags") List<String> tags,
            @RequestParam("objectType") String objectType) {
        List<MetaSearchResultVO> results = metaSearchService.searchByTags(tags, objectType);
        return success(results);
    }

    @PostMapping("/by-data-type")
    @Operation(summary = "按数据类型搜索列")
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<MetaSearchResultVO>> searchColumnsByDataType(
            @RequestParam("dataTypes") List<String> dataTypes,
            @RequestParam(value = "databaseId", required = false) Long databaseId) {
        List<MetaSearchResultVO> results = metaSearchService.searchColumnsByDataType(dataTypes, databaseId);
        return success(results);
    }

    @GetMapping("/fuzzy")
    @Operation(summary = "模糊搜索")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "objectTypes", description = "对象类型列表", required = true)
    @Parameter(name = "fuzzyLevel", description = "模糊匹配级别", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<MetaSearchResultVO>> fuzzySearch(
            @RequestParam("keyword") @NotBlank String keyword,
            @RequestParam("objectTypes") List<String> objectTypes,
            @RequestParam(value = "fuzzyLevel", defaultValue = "3") Integer fuzzyLevel) {
        List<MetaSearchResultVO> results = metaSearchService.fuzzySearch(keyword, objectTypes, fuzzyLevel);
        return success(results);
    }

    @GetMapping("/similarity")
    @Operation(summary = "相似性搜索")
    @Parameter(name = "referenceObjectId", description = "参考对象ID", required = true)
    @Parameter(name = "objectType", description = "对象类型", required = true)
    @Parameter(name = "similarityThreshold", description = "相似度阈值", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<MetaSearchResultVO>> similaritySearch(
            @RequestParam("referenceObjectId") @NotNull @Positive Long referenceObjectId,
            @RequestParam("objectType") @NotBlank String objectType,
            @RequestParam(value = "similarityThreshold", defaultValue = "0.7") Double similarityThreshold) {
        List<MetaSearchResultVO> results = metaSearchService.similaritySearch(referenceObjectId, objectType, similarityThreshold);
        return success(results);
    }

    // ==================== 搜索辅助接口 ====================

    @GetMapping("/suggestions")
    @Operation(summary = "获取搜索建议")
    @Parameter(name = "prefix", description = "输入前缀", required = true)
    @Parameter(name = "objectType", description = "对象类型", required = true)
    @Parameter(name = "limit", description = "建议数量限制", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<String>> getSearchSuggestions(
            @RequestParam("prefix") @NotBlank String prefix,
            @RequestParam("objectType") @NotBlank String objectType,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<String> suggestions = metaSearchService.getSearchSuggestions(prefix, objectType, limit);
        return success(suggestions);
    }

    @GetMapping("/popular-keywords")
    @Operation(summary = "获取热门搜索关键词")
    @Parameter(name = "objectType", description = "对象类型", required = false)
    @Parameter(name = "limit", description = "数量限制", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<String>> getPopularSearchKeywords(
            @RequestParam(value = "objectType", required = false) String objectType,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<String> keywords = metaSearchService.getPopularSearchKeywords(objectType, limit);
        return success(keywords);
    }

    @GetMapping("/history/{userId}")
    @Operation(summary = "获取搜索历史")
    @Parameter(name = "userId", description = "用户ID", required = true)
    @Parameter(name = "limit", description = "数量限制", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<List<String>> getSearchHistory(
            @PathVariable("userId") @NotNull @Positive Long userId,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        List<String> history = metaSearchService.getSearchHistory(userId, limit);
        return success(history);
    }

    @PostMapping("/history/{userId}")
    @Operation(summary = "保存搜索历史")
    @Parameter(name = "userId", description = "用户ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<Void> saveSearchHistory(
            @PathVariable("userId") @NotNull @Positive Long userId,
            @RequestParam("keyword") @NotBlank String keyword,
            @RequestParam("objectType") @NotBlank String objectType) {
        metaSearchService.saveSearchHistory(userId, keyword, objectType);
        return success(null);
    }

    @DeleteMapping("/history/{userId}")
    @Operation(summary = "清除搜索历史")
    @Parameter(name = "userId", description = "用户ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:search:manage')")
    public CommonResult<Void> clearSearchHistory(@PathVariable("userId") @NotNull @Positive Long userId) {
        metaSearchService.clearSearchHistory(userId);
        return success(null);
    }

    // ==================== 搜索管理接口 ====================

    @GetMapping("/statistics")
    @Operation(summary = "获取搜索统计信息")
    @Parameter(name = "startTime", description = "开始时间", required = false)
    @Parameter(name = "endTime", description = "结束时间", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:manage')")
    public CommonResult<Map<String, Object>> getSearchStatistics(
            @RequestParam(value = "startTime", required = false) Long startTime,
            @RequestParam(value = "endTime", required = false) Long endTime) {
        Map<String, Object> statistics = metaSearchService.getSearchStatistics(startTime, endTime);
        return success(statistics);
    }

    @PostMapping("/index/rebuild")
    @Operation(summary = "重建搜索索引")
    @Parameter(name = "objectType", description = "对象类型", required = false)
    @PreAuthorize("@ss.hasPermission('meta:search:manage')")
    public CommonResult<Map<String, Object>> rebuildSearchIndex(
            @RequestParam(value = "objectType", required = false) String objectType) {
        Map<String, Object> result = metaSearchService.rebuildSearchIndex(objectType);
        return success(result);
    }

    @PostMapping("/optimize")
    @Operation(summary = "优化搜索性能")
    @PreAuthorize("@ss.hasPermission('meta:search:manage')")
    public CommonResult<Map<String, Object>> optimizeSearchPerformance() {
        Map<String, Object> result = metaSearchService.optimizeSearchPerformance();
        return success(result);
    }

    @GetMapping("/index/validate")
    @Operation(summary = "验证搜索索引")
    @PreAuthorize("@ss.hasPermission('meta:search:manage')")
    public CommonResult<Map<String, Object>> validateSearchIndex() {
        Map<String, Object> result = metaSearchService.validateSearchIndex();
        return success(result);
    }

    @GetMapping("/filters/{objectType}")
    @Operation(summary = "获取搜索过滤器配置")
    @Parameter(name = "objectType", description = "对象类型", required = true)
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<Map<String, Object>> getSearchFilters(@PathVariable("objectType") @NotBlank String objectType) {
        Map<String, Object> filters = metaSearchService.getSearchFilters(objectType);
        return success(filters);
    }

    // ==================== 导出和批量操作接口 ====================

    @PostMapping("/export")
    @Operation(summary = "导出搜索结果")
    @PreAuthorize("@ss.hasPermission('meta:search:export')")
    public CommonResult<String> exportSearchResults(
            @Valid @RequestBody MetaSearchReqVO reqVO,
            @RequestParam(value = "format", defaultValue = "CSV") String format) {
        String exportData = metaSearchService.exportSearchResults(reqVO, format);
        return success(exportData);
    }

    @PostMapping("/batch")
    @Operation(summary = "批量搜索")
    @PreAuthorize("@ss.hasPermission('meta:search:query')")
    public CommonResult<Map<String, List<MetaSearchResultVO>>> batchSearch(
            @RequestParam("keywords") List<String> keywords,
            @RequestParam("objectType") @NotBlank String objectType) {
        Map<String, List<MetaSearchResultVO>> results = metaSearchService.batchSearch(keywords, objectType);
        return success(results);
    }
}
