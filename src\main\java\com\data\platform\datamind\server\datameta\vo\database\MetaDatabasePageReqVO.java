package com.data.platform.datamind.server.datameta.vo.database;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 15:33
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.entity.vo
 * @description
 * @email <EMAIL>
 * @since 1.8
 */
import com.data.platform.datamind.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.data.platform.datamind.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 数据库/数据源元数据分页 Request VO
 */
@Schema(description = "管理后台 - 数据库/数据源元数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MetaDatabasePageReqVO extends PageParam {

    @Schema(description = "数据库或数据源名称", example = "my_database")
    private String name;

    @Schema(description = "数据库类型", example = "MySQL")
    private String type;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
