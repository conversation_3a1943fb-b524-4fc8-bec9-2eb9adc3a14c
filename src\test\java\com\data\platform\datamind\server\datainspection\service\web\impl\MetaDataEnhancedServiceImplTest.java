package com.data.platform.datamind.server.datameta.service.web.impl;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncReqVO;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncRespVO;
import com.data.platform.datamind.server.datameta.vo.quality.DataQualityReportRespVO;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import com.data.platform.datamind.server.datameta.service.admin.MetaDataCollectService;
import com.data.platform.datamind.server.datameta.service.admin.MetaDataGraphService;
import com.data.platform.datamind.server.datameta.service.admin.MetaDatabaseService;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MetaDataEnhancedServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class MetaDataEnhancedServiceImplTest {

    @Mock
    private MetaDatabaseService databaseService;

    @Mock
    private MetaTableService tableService;

    @Mock
    private MetaColumnService columnService;

    @Mock
    private MetaDataCollectService dataCollectService;

    @Mock
    private MetaDataGraphService dataGraphService;

    @InjectMocks
    private MetaDataEnhancedServiceImpl metaDataEnhancedService;

    private MetaDatabaseDO mockDatabase;
    private MetaTableDO mockTable;
    private MetaColumnDO mockColumn;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockDatabase = new MetaDatabaseDO();
        mockDatabase.setId(1L);
        mockDatabase.setName("test_database");
        mockDatabase.setType("MySQL");
        mockDatabase.setDescription("测试数据库");

        mockTable = new MetaTableDO();
        mockTable.setId(1L);
        mockTable.setDbId(1L);
        mockTable.setTableName("user_info");
        mockTable.setTableComment("用户信息表");
        mockTable.setTableRows(1000L);
        mockTable.setDataLength(2048000L);

        mockColumn = new MetaColumnDO();
        mockColumn.setId(1L);
        mockColumn.setTableId(1L);
        mockColumn.setColumnName("user_id");
        mockColumn.setColumnComment("用户ID");
        mockColumn.setColumnType("BIGINT");
        mockColumn.setDataType("bigint(20)");
        mockColumn.setIsPrimaryKey(true);
        mockColumn.setIsForeignKey(false);
        mockColumn.setIsNullable(false);
    }

    @Test
    void testSyncMetaData_Success() {
        // 准备测试数据
        MetaDataSyncReqVO reqVO = new MetaDataSyncReqVO();
        reqVO.setSyncType(MetaDataSyncReqVO.SyncType.INCREMENTAL);
        reqVO.setSyncToGraph(true);

        when(databaseService.getAllDatabases()).thenReturn(Arrays.asList(mockDatabase));
        when(tableService.getTablesByDbId(1L)).thenReturn(Arrays.asList(mockTable));
        when(columnService.getColumnsByDbId(1L)).thenReturn(Arrays.asList(mockColumn));
        when(tableService.getAllTables()).thenReturn(Arrays.asList(mockTable));
        when(columnService.getAllColumns()).thenReturn(Arrays.asList(mockColumn));

        // 执行测试
        MetaDataSyncRespVO result = metaDataEnhancedService.syncMetaData(reqVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(MetaDataSyncRespVO.SyncStatus.SUCCESS, result.getStatus());
        assertEquals(1, result.getDatabaseCount());
        assertEquals(1, result.getTableCount());
        assertEquals(1, result.getColumnCount());
        assertNotNull(result.getSyncTaskId());
        assertNotNull(result.getStartTime());
        assertNotNull(result.getEndTime());
        assertTrue(result.getDuration() >= 0);

        // 验证调用
        verify(databaseService).getAllDatabases();
        verify(tableService).getTablesByDbId(1L);
        verify(columnService).getColumnsByDbId(1L);
        verify(dataGraphService).syncAllMetaDataToGraph(anyList(), anyList(), anyList());
    }

    @Test
    void testSyncMetaData_WithSpecificDatabases() {
        // 准备测试数据
        MetaDataSyncReqVO reqVO = new MetaDataSyncReqVO();
        reqVO.setDatabaseIds(Arrays.asList(1L));
        reqVO.setSyncToGraph(false);

        when(databaseService.getDatabaseList(Arrays.asList(1L))).thenReturn(Arrays.asList(mockDatabase));
        when(tableService.getTablesByDbId(1L)).thenReturn(Arrays.asList(mockTable));
        when(columnService.getColumnsByDbId(1L)).thenReturn(Arrays.asList(mockColumn));

        // 执行测试
        MetaDataSyncRespVO result = metaDataEnhancedService.syncMetaData(reqVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(MetaDataSyncRespVO.SyncStatus.SUCCESS, result.getStatus());
        assertEquals(1, result.getDatabaseCount());

        // 验证不会同步到图谱
        verify(dataGraphService, never()).syncAllMetaDataToGraph(anyList(), anyList(), anyList());
    }

    @Test
    void testSyncMetaData_EmptyDatabases() {
        // 准备测试数据
        MetaDataSyncReqVO reqVO = new MetaDataSyncReqVO();

        when(databaseService.getAllDatabases()).thenReturn(Collections.emptyList());

        // 执行测试
        MetaDataSyncRespVO result = metaDataEnhancedService.syncMetaData(reqVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(MetaDataSyncRespVO.SyncStatus.SUCCESS, result.getStatus());
        assertEquals(0, result.getDatabaseCount());
        assertEquals(0, result.getTableCount());
        assertEquals(0, result.getColumnCount());
    }

    @Test
    void testGetTableLineage_Success() {
        // 准备测试数据
        Long tableId = 1L;
        
        MetaColumnDO fkColumn = new MetaColumnDO();
        fkColumn.setId(2L);
        fkColumn.setTableId(1L);
        fkColumn.setColumnName("department_id");
        fkColumn.setIsForeignKey(true);
        fkColumn.setFkTableId(2L);
        fkColumn.setFkColumnId(3L);

        MetaTableDO fkTable = new MetaTableDO();
        fkTable.setId(2L);
        fkTable.setDbId(1L);
        fkTable.setTableName("department");
        fkTable.setTableComment("部门表");

        MetaColumnDO referencedColumn = new MetaColumnDO();
        referencedColumn.setId(3L);
        referencedColumn.setColumnName("id");

        when(tableService.getTable(1L)).thenReturn(mockTable);
        when(databaseService.getDatabase(1L)).thenReturn(mockDatabase);
        when(columnService.getColumnsByTableId(1L)).thenReturn(Arrays.asList(mockColumn, fkColumn));
        when(tableService.getTable(2L)).thenReturn(fkTable);
        when(columnService.getColumn(3L)).thenReturn(referencedColumn);
        when(columnService.getAllColumns()).thenReturn(Arrays.asList(mockColumn, fkColumn, referencedColumn));

        // 执行测试
        TableLineageRespVO result = metaDataEnhancedService.getTableLineage(tableId);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getTargetTable());
        assertEquals("user_info", result.getTargetTable().getTableName());
        assertEquals(1, result.getUpstreamTables().size());
        assertEquals("department", result.getUpstreamTables().get(0).getTableName());
        assertEquals(1, result.getRelations().size());
        assertEquals(TableLineageRespVO.LineageRelation.RelationType.FOREIGN_KEY, 
                result.getRelations().get(0).getRelationType());
    }

    @Test
    void testGetTableLineage_TableNotFound() {
        // 准备测试数据
        Long tableId = 999L;

        when(tableService.getTable(999L)).thenReturn(null);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            metaDataEnhancedService.getTableLineage(tableId);
        });

        assertEquals("Table not found with id: 999", exception.getMessage());
    }

    @Test
    void testGetDataQualityReport_Success() {
        // 准备测试数据
        when(databaseService.getAllDatabases()).thenReturn(Arrays.asList(mockDatabase));
        when(tableService.getTablesByDbId(1L)).thenReturn(Arrays.asList(mockTable));
        when(columnService.getColumnsByDbId(1L)).thenReturn(Arrays.asList(mockColumn));

        // 执行测试
        DataQualityReportRespVO result = metaDataEnhancedService.getDataQualityReport();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getReportTime());
        assertTrue(result.getOverallScore() >= 0 && result.getOverallScore() <= 100);
        assertNotNull(result.getQualityLevel());
        assertEquals(1, result.getDatabaseQualities().size());
        assertNotNull(result.getIssueSummary());
        assertNotNull(result.getQualityTrends());
        assertEquals(7, result.getQualityTrends().size()); // 最近7天
    }

    @Test
    void testGetDataQualityReport_WithDatabaseId() {
        // 准备测试数据
        Long databaseId = 1L;

        when(databaseService.getDatabase(1L)).thenReturn(mockDatabase);
        when(tableService.getTablesByDbId(1L)).thenReturn(Arrays.asList(mockTable));
        when(columnService.getColumnsByDbId(1L)).thenReturn(Arrays.asList(mockColumn));

        // 执行测试
        DataQualityReportRespVO result = metaDataEnhancedService.getDataQualityReport(databaseId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getDatabaseQualities().size());
        assertEquals("test_database", result.getDatabaseQualities().get(0).getDatabaseName());
    }

    @Test
    void testGetDataQualityReport_DatabaseNotFound() {
        // 准备测试数据
        Long databaseId = 999L;

        when(databaseService.getDatabase(999L)).thenReturn(null);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            metaDataEnhancedService.getDataQualityReport(databaseId);
        });

        assertEquals("Database not found with id: 999", exception.getMessage());
    }

    @Test
    void testCalculateQualityMetrics() {
        // 准备测试数据 - 表没有注释
        MetaTableDO tableWithoutComment = new MetaTableDO();
        tableWithoutComment.setId(2L);
        tableWithoutComment.setTableName("test_table");
        tableWithoutComment.setTableComment(null);

        // 列没有注释
        MetaColumnDO columnWithoutComment = new MetaColumnDO();
        columnWithoutComment.setId(2L);
        columnWithoutComment.setColumnName("test_column");
        columnWithoutComment.setColumnComment("");

        when(databaseService.getDatabase(1L)).thenReturn(mockDatabase);
        when(tableService.getTablesByDbId(1L)).thenReturn(Arrays.asList(mockTable, tableWithoutComment));
        when(columnService.getColumnsByDbId(1L)).thenReturn(Arrays.asList(mockColumn, columnWithoutComment));

        // 执行测试
        DataQualityReportRespVO result = metaDataEnhancedService.getDataQualityReport(1L);

        // 验证结果
        assertNotNull(result);
        DataQualityReportRespVO.DatabaseQuality dbQuality = result.getDatabaseQualities().get(0);
        
        // 验证质量指标
        assertNotNull(dbQuality.getQualityMetrics());
        assertTrue(dbQuality.getQualityMetrics().containsKey("tableCommentCoverage"));
        assertTrue(dbQuality.getQualityMetrics().containsKey("columnCommentCoverage"));
        assertTrue(dbQuality.getQualityMetrics().containsKey("namingConsistency"));
        assertTrue(dbQuality.getQualityMetrics().containsKey("dataTypeConsistency"));
        
        // 表注释覆盖率应该是50%（2个表中1个有注释）
        assertEquals(50.0, (Double) dbQuality.getQualityMetrics().get("tableCommentCoverage"));
        
        // 列注释覆盖率应该是50%（2个列中1个有注释）
        assertEquals(50.0, (Double) dbQuality.getQualityMetrics().get("columnCommentCoverage"));
    }
}
