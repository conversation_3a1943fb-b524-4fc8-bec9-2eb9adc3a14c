package com.data.platform.datamind.server.datameta.service.web.impl;


import com.data.platform.datamind.server.datameta.entity.bo.ConnectionInfo;
import com.data.platform.datamind.server.datameta.entity.dto.MetaColInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaIndexInfoDTO;
import com.data.platform.datamind.server.datameta.entity.dto.MetaTableInfoDTO;
import com.data.platform.datamind.server.datameta.service.web.MetaDataService;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class MysqlMetaDataService implements MetaDataService {
  private static final Logger log = LoggerFactory.getLogger(MysqlMetaDataService.class);
  
  private static final String SCHEMA_SQL = "  TABLE_SCHEMA not in ('mysql','information_schema','performance_schema','sys')";
  
  private static final String TABLE_QUERY = "select  TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE ='BASE TABLE' AND   TABLE_SCHEMA not in ('mysql','information_schema','performance_schema','sys')";
  
  private static final String COL_QUERY = "SELECT c.TABLE_SCHEMA,c.TABLE_NAME,c.COLUMN_NAME,c.DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS  c WHERE  c.TABLE_SCHEMA not in ('mysql','information_schema','performance_schema','sys')";
  
  private static final String INDEX_QUERY = "SELECT TABLE_SCHEMA,TABLE_NAME,COLUMN_NAME,INDEX_NAME,SEQ_IN_INDEX,INDEX_TYPE,cardinality as DISTINCT_NUM FROM INFORMATION_SCHEMA.STATISTICS  WHERE   TABLE_SCHEMA not in ('mysql','information_schema','performance_schema','sys')";
  
  private final DataSource dataSource;
  
  public MysqlMetaDataService(DataSource dataSource) {
    this.dataSource = dataSource;
  }
  
  public Connection getDestConnection() {
    try {
      return this.dataSource.getConnection();
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
  }
  
  public Logger getLogger() {
    return log;
  }
  
  public String buildConnectionInfo(ConnectionInfo connectionInfo) {
    return String.format("********************************************************************************", new Object[] { connectionInfo.getHost(), connectionInfo.getPort() });
  }
  
  public List<MetaTableInfoDTO> getMetaTableInfoList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaTableInfoDTO> list = new ArrayList<>();
    try (PreparedStatement ps = connection.prepareStatement("select  TABLE_SCHEMA,TABLE_NAME,TABLE_ROWS,TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE ='BASE TABLE' AND   TABLE_SCHEMA not in ('mysql','information_schema','performance_schema','sys')")) {
      ResultSet rs = ps.executeQuery();
      while (rs.next()) {
        MetaTableInfoDTO metaTableInfoDTO = new MetaTableInfoDTO();
        metaTableInfoDTO.setInstanceId(connectionInfo.getInstanceId());
        metaTableInfoDTO.setTabOwner(rs.getString("TABLE_SCHEMA"));
        metaTableInfoDTO.setTabName(rs.getString("TABLE_NAME"));
        metaTableInfoDTO.setRecordNum(Long.valueOf(rs.getLong("TABLE_ROWS")));
        metaTableInfoDTO.setUpdateTime(new Date());
        list.add(metaTableInfoDTO);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return list;
  }
  
  public List<MetaColInfoDTO> getMetaColInfoDTOList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaColInfoDTO> list = new ArrayList<>();
    try (PreparedStatement ps = connection.prepareStatement("SELECT c.TABLE_SCHEMA,c.TABLE_NAME,c.COLUMN_NAME,c.DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS  c WHERE  c.TABLE_SCHEMA not in ('mysql','information_schema','performance_schema','sys')")) {
      ps.setFetchSize(1000);
      ResultSet rs = ps.executeQuery();
      while (rs.next()) {
        MetaColInfoDTO dto = new MetaColInfoDTO();
        dto.setInstanceId(connectionInfo.getInstanceId());
        dto.setTabOwner(rs.getString("TABLE_SCHEMA"));
        dto.setTabName(rs.getString("TABLE_NAME"));
        dto.setColName(rs.getString("COLUMN_NAME"));
        dto.setDataType(rs.getString("DATA_TYPE"));
        dto.setUpdateTime(new Date());
        list.add(dto);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return list;
  }
  
  public List<MetaIndexInfoDTO> getMetaIndexInfoDTOList(Connection connection, ConnectionInfo connectionInfo) {
    List<MetaIndexInfoDTO> list = new ArrayList<>();
    try (PreparedStatement ps = connection.prepareStatement("SELECT TABLE_SCHEMA,TABLE_NAME,COLUMN_NAME,INDEX_NAME,SEQ_IN_INDEX,INDEX_TYPE,cardinality as DISTINCT_NUM FROM INFORMATION_SCHEMA.STATISTICS  WHERE   TABLE_SCHEMA not in ('mysql','information_schema','performance_schema','sys')")) {
      ResultSet rs = ps.executeQuery();
      while (rs.next()) {
        MetaIndexInfoDTO dto = new MetaIndexInfoDTO();
        dto.setInstanceId(connectionInfo.getInstanceId());
        dto.setTabOwner(rs.getString("TABLE_SCHEMA"));
        dto.setTabName(rs.getString("TABLE_NAME"));
        dto.setIndexName(rs.getString("INDEX_NAME"));
        dto.setColName(rs.getString("COLUMN_NAME"));
        dto.setIndexPosition(Integer.valueOf(rs.getInt("SEQ_IN_INDEX")));
        dto.setIndexType(rs.getString("INDEX_TYPE"));
        dto.setDistinctNum(Long.valueOf(rs.getLong("DISTINCT_NUM")));
        dto.setUpdateTime(new Date());
        list.add(dto);
      } 
    } catch (SQLException e) {
      throw new RuntimeException(e);
    } 
    return list;
  }
}
