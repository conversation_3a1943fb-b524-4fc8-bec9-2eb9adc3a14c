package com.data.platform.datamind.server.datameta.entity.ao;

import lombok.*;

import java.util.List;

@Data
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncAO {
    private String host;

    private List<Integer> ids;

}


/* Location:              E:\Switch-Files\2025年6月\glfiles-0623\meta-sync-server-1.0-SNAPSHOT.jar!\BOOT-INF\classes\com\ls\meta\sync\model\ao\SyncAO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */