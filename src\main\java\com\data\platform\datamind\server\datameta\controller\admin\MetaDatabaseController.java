package com.data.platform.datamind.server.datameta.controller.admin;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/6/23 18:18
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datainspection.controller.admin
 * @description
 * @email <EMAIL>
 * @since 1.8
 */

import com.data.platform.datamind.framework.common.pojo.CommonResult;
import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaDatabaseConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.entity.vo.database.*;
import com.data.platform.datamind.server.datameta.service.admin.MetaDataCollectService;
import com.data.platform.datamind.server.datameta.service.admin.MetaDatabaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.sql.SQLException;
import java.util.List;

import static com.data.platform.datamind.framework.common.pojo.CommonResult.success;
//import static com.data.platform.datamind.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


/**
 * 数据库/数据源元数据 Controller
 *
 */
@Tag(name = "管理后台 - 数据库元数据管理")
@RestController
@RequestMapping("/admin-api/data-meta/database")
@Validated
public class MetaDatabaseController {

    @Resource
    private MetaDatabaseService databaseService;
    @Resource
    private MetaDataCollectService dataCollectService; // 注入采集服务

    @PostMapping("/create")
    @Operation(summary = "创建数据库/数据源元数据")
    @PreAuthorize("@ss.hasPermission('meta:database:create')")
    public CommonResult<Long> createDatabase(@Valid @RequestBody MetaDatabaseCreateReqVO createReqVO) {
        Long id = databaseService.createDatabase(createReqVO);
        return success(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新数据库/数据源元数据")
    @PreAuthorize("@ss.hasPermission('meta:database:update')")
    public CommonResult<Boolean> updateDatabase(@Valid @RequestBody MetaDatabaseUpdateReqVO updateReqVO) {
        databaseService.updateDatabase(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除数据库/数据源元数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('meta:database:delete')")
    public CommonResult<Boolean> deleteDatabase(@RequestParam("id") Long id) {
        databaseService.deleteDatabase(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得数据库/数据源元数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('meta:database:query')")
    public CommonResult<MetaDatabaseRespVO> getDatabase(@RequestParam("id") Long id) {
        MetaDatabaseDO database = databaseService.getDatabase(id);
        return success(MetaDatabaseConvert.INSTANCE.convert(database));
    }

    @GetMapping("/page")
    @Operation(summary = "获得数据库/数据源元数据分页")
    @PreAuthorize("@ss.hasPermission('meta:database:query')")
    public CommonResult<PageResult<MetaDatabaseRespVO>> getDatabasePage(@Valid MetaDatabasePageReqVO pageReqVO) {
        PageResult<MetaDatabaseDO> pageResult = databaseService.getDatabasePage(pageReqVO);
        return success(MetaDatabaseConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获得所有数据库/数据源元数据列表（精简）")
    @PreAuthorize("@ss.hasPermission('meta:database:query')")
    public CommonResult<List<MetaDatabaseRespVO>> getAllDatabasesSimple() {
        List<MetaDatabaseDO> list = databaseService.getAllDatabases();
        return success(MetaDatabaseConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/collect")
    @Operation(summary = "采集数据库元数据并同步到图谱")
    @PreAuthorize("@ss.hasPermission('meta:database:collect')") // 新增权限
    public CommonResult<Boolean> collectMetaData(@Valid @RequestBody MetaDataCollectReqVO reqVO) {
        try {
            dataCollectService.collectAndSyncMetaData(reqVO);
            return success(true);
        } catch (SQLException e) {
            // 记录日志，并返回更友好的错误信息
            return CommonResult.error(500, "元数据采集失败：" + e.getMessage());
        }
    }
}
