
package com.data.platform.datamind.server.datameta.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@TableName("SQL_INFO_RUNNING")
public class SqlInfoRunningDTO {
    @TableField("SQL_INFO_RUNNING_ID")
    private Integer sqlInfoRunningID;
    @TableField("DB_ID")
    private Integer dbID;
    @TableField("DB_TYPE")
    private String dbType;

    @TableField("DB_USERNAME")
    private String dbUsername;

    @TableField("ORACLE_SQL_ID")
    private String oracleSqlID;

    @TableField("STATEMENT_CONTENT")
    private String statementContent;

    @TableField("AI_REVIEW_DETAILS")
    private String aiReviewDetails;

    @TableField("AI_SCORE")
    private Integer aiScore;

    @TableField("AI_REVIEW_RESULT")
    /* 13 */ private String aiReviewResult;

    @TableField("MANUAL_REVIEW_RESULT")
    private String manualReviewResult;
    @TableField("MANUAL_REVIEW_NOTES")
    private String manualReviewNotes;
    @TableField("AI_REVIEW_DETAILS_SIMPLE")
    private String aiReviewDetailsSimple;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("LAST_AI_RE_TIME")
    private Date lastAiReTime;
    @TableField("AI_REVIEW_STAT")
    private String aiReviewStat;
    @TableField("SQL_RUNTIME")
    private Long sqlRuntime;
    @TableField("SQL_DESC")
    private String sqlDesc;
    @TableField("JOB_START_TIME_CHAR")
    private String jobStartTimeChar;

}
