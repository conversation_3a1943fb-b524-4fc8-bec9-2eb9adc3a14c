
package com.data.platform.datamind.server.datameta.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class MetaColInfoDTO {

    private String colName;
    private Integer instanceId;
    private String tabName;
    private String tabOwner;
    private String dataType;
    private Long distinctNum;
    private String colComment;
    private Date updateTime;

}


/* Location:              E:\Switch-Files\2025年6月\glfiles-0623\meta-sync-server-1.0-SNAPSHOT.jar!\BOOT-INF\classes\com\ls\meta\sync\model\dto\MetaColInfoDTO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */