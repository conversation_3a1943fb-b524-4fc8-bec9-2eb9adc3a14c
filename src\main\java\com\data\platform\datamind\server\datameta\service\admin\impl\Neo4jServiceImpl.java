package com.data.platform.datamind.server.datameta.service.admin.impl;

import com.data.platform.datamind.server.datameta.service.admin.Neo4jService;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;

/**
 * Neo4j 基础服务实现类
 * 提供Neo4j数据库的基础操作功能
 *
 * <AUTHOR> Team
 */
@Service
@Validated
@Slf4j
public class Neo4jServiceImpl implements Neo4jService {

    @Resource
    private Neo4jClient neo4jClient;

    @Resource
    private Neo4jTemplate neo4jTemplate;

    @Override
    public List<Map<String, Object>> executeCypher(String cypher, Map<String, Object> parameters) {
        try {
            log.debug("Executing Cypher query: {}", cypher);
            return neo4jClient.query(cypher)
                    .bindAll(parameters != null ? parameters : Collections.emptyMap())
                    .fetch()
                    .all();
        } catch (Exception e) {
            log.error("Error executing Cypher query: {}", cypher, e);
            throw new RuntimeException("Neo4j查询执行失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long executeUpdate(String cypher, Map<String, Object> parameters) {
        try {
            log.debug("Executing Cypher update: {}", cypher);
            return neo4jClient.query(cypher)
                    .bindAll(parameters != null ? parameters : Collections.emptyMap())
                    .run()
                    .counters()
                    .nodesCreated() + neo4jClient.query(cypher)
                    .bindAll(parameters != null ? parameters : Collections.emptyMap())
                    .run()
                    .counters()
                    .relationshipsCreated();
        } catch (Exception e) {
            log.error("Error executing Cypher update: {}", cypher, e);
            throw new RuntimeException("Neo4j更新操作执行失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNode(String label, Map<String, Object> properties) {
        try {
            StringBuilder cypher = new StringBuilder("CREATE (n:");
            cypher.append(label).append(" {");
            
            if (properties != null && !properties.isEmpty()) {
                List<String> propList = new ArrayList<>();
                for (String key : properties.keySet()) {
                    propList.add(key + ": $" + key);
                }
                cypher.append(String.join(", ", propList));
            }
            
            cypher.append("}) RETURN id(n) as nodeId");
            
            List<Map<String, Object>> result = executeCypher(cypher.toString(), properties);
            if (!result.isEmpty()) {
                return (Long) result.get(0).get("nodeId");
            }
            return null;
        } catch (Exception e) {
            log.error("Error creating node with label: {}", label, e);
            throw new RuntimeException("创建节点失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRelationship(Long sourceNodeId, Long targetNodeId, String relationshipType, Map<String, Object> properties) {
        try {
            StringBuilder cypher = new StringBuilder("MATCH (a), (b) WHERE id(a) = $sourceId AND id(b) = $targetId ");
            cypher.append("CREATE (a)-[r:").append(relationshipType);
            
            if (properties != null && !properties.isEmpty()) {
                cypher.append(" {");
                List<String> propList = new ArrayList<>();
                for (String key : properties.keySet()) {
                    propList.add(key + ": $" + key);
                }
                cypher.append(String.join(", ", propList));
                cypher.append("}");
            }
            
            cypher.append("]->(b) RETURN id(r) as relId");
            
            Map<String, Object> params = new HashMap<>();
            params.put("sourceId", sourceNodeId);
            params.put("targetId", targetNodeId);
            if (properties != null) {
                params.putAll(properties);
            }
            
            List<Map<String, Object>> result = executeCypher(cypher.toString(), params);
            if (!result.isEmpty()) {
                return (Long) result.get(0).get("relId");
            }
            return null;
        } catch (Exception e) {
            log.error("Error creating relationship: {}", relationshipType, e);
            throw new RuntimeException("创建关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteNode(Long nodeId) {
        try {
            String cypher = "MATCH (n) WHERE id(n) = $nodeId DETACH DELETE n";
            Map<String, Object> params = Collections.singletonMap("nodeId", nodeId);
            executeUpdate(cypher, params);
        } catch (Exception e) {
            log.error("Error deleting node: {}", nodeId, e);
            throw new RuntimeException("删除节点失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRelationship(Long relationshipId) {
        try {
            String cypher = "MATCH ()-[r]-() WHERE id(r) = $relId DELETE r";
            Map<String, Object> params = Collections.singletonMap("relId", relationshipId);
            executeUpdate(cypher, params);
        } catch (Exception e) {
            log.error("Error deleting relationship: {}", relationshipId, e);
            throw new RuntimeException("删除关系失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> findNodes(String label, Map<String, Object> properties) {
        try {
            StringBuilder cypher = new StringBuilder("MATCH (n:");
            cypher.append(label).append(")");
            
            if (properties != null && !properties.isEmpty()) {
                cypher.append(" WHERE ");
                List<String> conditions = new ArrayList<>();
                for (String key : properties.keySet()) {
                    conditions.add("n." + key + " = $" + key);
                }
                cypher.append(String.join(" AND ", conditions));
            }
            
            cypher.append(" RETURN n, id(n) as nodeId");
            
            return executeCypher(cypher.toString(), properties);
        } catch (Exception e) {
            log.error("Error finding nodes with label: {}", label, e);
            throw new RuntimeException("查询节点失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> findRelationships(String relationshipType, Map<String, Object> properties) {
        try {
            StringBuilder cypher = new StringBuilder("MATCH ()-[r:");
            cypher.append(relationshipType).append("]-()");
            
            if (properties != null && !properties.isEmpty()) {
                cypher.append(" WHERE ");
                List<String> conditions = new ArrayList<>();
                for (String key : properties.keySet()) {
                    conditions.add("r." + key + " = $" + key);
                }
                cypher.append(String.join(" AND ", conditions));
            }
            
            cypher.append(" RETURN r, id(r) as relId");
            
            return executeCypher(cypher.toString(), properties);
        } catch (Exception e) {
            log.error("Error finding relationships: {}", relationshipType, e);
            throw new RuntimeException("查询关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearDatabase() {
        try {
            String cypher = "MATCH (n) DETACH DELETE n";
            executeUpdate(cypher, Collections.emptyMap());
            log.info("Database cleared successfully");
        } catch (Exception e) {
            log.error("Error clearing database", e);
            throw new RuntimeException("清空数据库失败", e);
        }
    }

    @Override
    public Map<String, Object> getDatabaseStats() {
        try {
            String cypher = "MATCH (n) RETURN count(n) as nodeCount";
            List<Map<String, Object>> nodeResult = executeCypher(cypher, Collections.emptyMap());
            
            cypher = "MATCH ()-[r]-() RETURN count(r) as relCount";
            List<Map<String, Object>> relResult = executeCypher(cypher, Collections.emptyMap());
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("nodeCount", nodeResult.isEmpty() ? 0 : nodeResult.get(0).get("nodeCount"));
            stats.put("relationshipCount", relResult.isEmpty() ? 0 : relResult.get(0).get("relCount"));
            
            return stats;
        } catch (Exception e) {
            log.error("Error getting database stats", e);
            throw new RuntimeException("获取数据库统计信息失败", e);
        }
    }

    @Override
    public boolean checkConnection() {
        try {
            String cypher = "RETURN 1 as test";
            List<Map<String, Object>> result = executeCypher(cypher, Collections.emptyMap());
            return !result.isEmpty();
        } catch (Exception e) {
            log.error("Neo4j connection check failed", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateNodes(String label, List<Map<String, Object>> nodesList) {
        if (nodesList == null || nodesList.isEmpty()) {
            return 0;
        }
        
        try {
            String cypher = "UNWIND $nodes as node CREATE (n:" + label + ") SET n = node RETURN count(n) as created";
            Map<String, Object> params = Collections.singletonMap("nodes", nodesList);
            
            List<Map<String, Object>> result = executeCypher(cypher, params);
            return result.isEmpty() ? 0 : ((Number) result.get(0).get("created")).intValue();
        } catch (Exception e) {
            log.error("Error batch creating nodes", e);
            throw new RuntimeException("批量创建节点失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateRelationships(String relationshipType, List<Map<String, Object>> relationshipsList) {
        if (relationshipsList == null || relationshipsList.isEmpty()) {
            return 0;
        }
        
        try {
            String cypher = "UNWIND $rels as rel " +
                    "MATCH (a), (b) WHERE id(a) = rel.sourceId AND id(b) = rel.targetId " +
                    "CREATE (a)-[r:" + relationshipType + "]->(b) " +
                    "SET r = rel.properties " +
                    "RETURN count(r) as created";
            
            Map<String, Object> params = Collections.singletonMap("rels", relationshipsList);
            
            List<Map<String, Object>> result = executeCypher(cypher, params);
            return result.isEmpty() ? 0 : ((Number) result.get(0).get("created")).intValue();
        } catch (Exception e) {
            log.error("Error batch creating relationships", e);
            throw new RuntimeException("批量创建关系失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, Object>> executeTransaction(List<String> cypherStatements) {
        if (cypherStatements == null || cypherStatements.isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            List<Map<String, Object>> results = new ArrayList<>();
            for (String cypher : cypherStatements) {
                List<Map<String, Object>> result = executeCypher(cypher, Collections.emptyMap());
                results.addAll(result);
            }
            return results;
        } catch (Exception e) {
            log.error("Error executing transaction", e);
            throw new RuntimeException("执行事务失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> findPath(Long sourceNodeId, Long targetNodeId, int maxDepth) {
        try {
            String cypher = "MATCH path = shortestPath((a)-[*1.." + maxDepth + "]-(b)) " +
                    "WHERE id(a) = $sourceId AND id(b) = $targetId " +
                    "RETURN path, length(path) as pathLength";
            
            Map<String, Object> params = new HashMap<>();
            params.put("sourceId", sourceNodeId);
            params.put("targetId", targetNodeId);
            
            return executeCypher(cypher, params);
        } catch (Exception e) {
            log.error("Error finding path between nodes: {} -> {}", sourceNodeId, targetNodeId, e);
            throw new RuntimeException("查找路径失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> findNeighbors(Long nodeId, String direction, List<String> relationshipTypes) {
        try {
            StringBuilder cypher = new StringBuilder("MATCH (n)");
            
            if ("IN".equalsIgnoreCase(direction)) {
                cypher.append("<-[r");
            } else if ("OUT".equalsIgnoreCase(direction)) {
                cypher.append("-[r");
            } else {
                cypher.append("-[r");
            }
            
            if (relationshipTypes != null && !relationshipTypes.isEmpty()) {
                cypher.append(":").append(String.join("|", relationshipTypes));
            }
            
            if ("IN".equalsIgnoreCase(direction)) {
                cypher.append("]-(m) WHERE id(n) = $nodeId");
            } else if ("OUT".equalsIgnoreCase(direction)) {
                cypher.append("]->(m) WHERE id(n) = $nodeId");
            } else {
                cypher.append("]-(m) WHERE id(n) = $nodeId");
            }
            
            cypher.append(" RETURN m, r, id(m) as neighborId, id(r) as relId");
            
            Map<String, Object> params = Collections.singletonMap("nodeId", nodeId);
            
            return executeCypher(cypher.toString(), params);
        } catch (Exception e) {
            log.error("Error finding neighbors for node: {}", nodeId, e);
            throw new RuntimeException("查找邻居节点失败", e);
        }
    }
}
