package com.data.platform.datamind.server.datameta.controller.admin;

import com.data.platform.datamind.framework.common.pojo.CommonResult;
import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaIndexConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaIndexDO;
import com.data.platform.datamind.server.datameta.service.admin.MetaIndexService;
import com.data.platform.datamind.server.datameta.vo.index.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static com.data.platform.datamind.framework.common.pojo.CommonResult.success;

/**
 * 索引元数据管理 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.controller.admin
 * @description 索引元数据管理控制器，提供索引信息的CRUD操作
 * @email <EMAIL>
 * @since 1.8
 */
@Tag(name = "管理后台 - 索引元数据管理")
@RestController
@RequestMapping("/admin-api/data-meta/index")
@Validated
public class MetaIndexController {

    @Resource
    private MetaIndexService indexService;

    @PostMapping("/create")
    @Operation(summary = "创建索引元数据")
    @PreAuthorize("@ss.hasPermission('meta:index:create')")
    public CommonResult<Long> createIndex(@Valid @RequestBody MetaIndexCreateReqVO createReqVO) {
        Long id = indexService.createIndex(createReqVO);
        return success(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新索引元数据")
    @PreAuthorize("@ss.hasPermission('meta:index:update')")
    public CommonResult<Boolean> updateIndex(@Valid @RequestBody MetaIndexUpdateReqVO updateReqVO) {
        indexService.updateIndex(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除索引元数据")
    @Parameter(name = "id", description = "索引ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:index:delete')")
    public CommonResult<Boolean> deleteIndex(@RequestParam("id") Long id) {
        indexService.deleteIndex(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得索引元数据")
    @Parameter(name = "id", description = "索引ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:index:query')")
    public CommonResult<MetaIndexRespVO> getIndex(@RequestParam("id") Long id) {
        MetaIndexDO index = indexService.getIndex(id);
        return success(MetaIndexConvert.INSTANCE.convert(index));
    }

    @GetMapping("/list")
    @Operation(summary = "获得索引元数据列表")
    @Parameter(name = "ids", description = "索引ID列表", required = true, example = "1,2")
    @PreAuthorize("@ss.hasPermission('meta:index:query')")
    public CommonResult<List<MetaIndexRespVO>> getIndexList(@RequestParam("ids") Collection<Long> ids) {
        List<MetaIndexDO> list = indexService.getIndexList(ids);
        return success(MetaIndexConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得索引元数据分页")
    @PreAuthorize("@ss.hasPermission('meta:index:query')")
    public CommonResult<PageResult<MetaIndexRespVO>> getIndexPage(@Valid MetaIndexPageReqVO pageVO) {
        PageResult<MetaIndexDO> pageResult = indexService.getIndexPage(pageVO);
        return success(MetaIndexConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-by-table")
    @Operation(summary = "根据表ID获取索引列表")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:index:query')")
    public CommonResult<List<MetaIndexRespVO>> getIndexesByTableId(@RequestParam("tableId") Long tableId) {
        List<MetaIndexDO> list = indexService.getIndexesByTableId(tableId);
        return success(MetaIndexConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-by-db")
    @Operation(summary = "根据数据库ID获取索引列表")
    @Parameter(name = "dbId", description = "数据库ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:index:query')")
    public CommonResult<List<MetaIndexRespVO>> getIndexesByDbId(@RequestParam("dbId") Long dbId) {
        List<MetaIndexDO> list = indexService.getIndexesByDbId(dbId);
        return success(MetaIndexConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/primary-keys")
    @Operation(summary = "获取表的主键索引")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:index:query')")
    public CommonResult<List<MetaIndexRespVO>> getPrimaryKeysByTableId(@RequestParam("tableId") Long tableId) {
        List<MetaIndexDO> list = indexService.getPrimaryKeysByTableId(tableId);
        return success(MetaIndexConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/unique-indexes")
    @Operation(summary = "获取表的唯一索引")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:index:query')")
    public CommonResult<List<MetaIndexRespVO>> getUniqueIndexesByTableId(@RequestParam("tableId") Long tableId) {
        List<MetaIndexDO> list = indexService.getUniqueIndexesByTableId(tableId);
        return success(MetaIndexConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建索引元数据")
    @PreAuthorize("@ss.hasPermission('meta:index:create')")
    public CommonResult<Boolean> batchCreateIndexes(@Valid @RequestBody List<MetaIndexCreateReqVO> createReqVOs) {
        List<MetaIndexDO> indexes = MetaIndexConvert.INSTANCE.convertList(createReqVOs);
        indexService.batchInsert(indexes);
        return success(true);
    }

    @PutMapping("/batch-update")
    @Operation(summary = "批量更新索引元数据")
    @PreAuthorize("@ss.hasPermission('meta:index:update')")
    public CommonResult<Boolean> batchUpdateIndexes(@Valid @RequestBody List<MetaIndexUpdateReqVO> updateReqVOs) {
        List<MetaIndexDO> indexes = MetaIndexConvert.INSTANCE.convertUpdateList(updateReqVOs);
        indexService.batchUpdate(indexes);
        return success(true);
    }

    @DeleteMapping("/delete-by-table")
    @Operation(summary = "根据表ID删除索引")
    @Parameter(name = "tableId", description = "表ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('meta:index:delete')")
    public CommonResult<Boolean> deleteIndexesByTableId(@RequestParam("tableId") Long tableId) {
        indexService.deleteIndexesByTableId(tableId);
        return success(true);
    }
}
