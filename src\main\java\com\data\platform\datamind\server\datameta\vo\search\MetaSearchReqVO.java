package com.data.platform.datamind.server.datameta.vo.search;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 元数据搜索请求VO
 *
 * <AUTHOR> Team
 */
@Schema(description = "元数据搜索请求")
@Data
@EqualsAndHashCode(callSuper = true)
public class MetaSearchReqVO extends PageParam {

    @Schema(description = "搜索关键词", requiredMode = Schema.RequiredMode.REQUIRED, example = "user")
    @NotBlank(message = "搜索关键词不能为空")
    @Size(max = 100, message = "搜索关键词长度不能超过100个字符")
    private String keyword;

    @Schema(description = "搜索类型", example = "ALL")
    private String searchType = "ALL"; // ALL, DATABASE, TABLE, COLUMN

    @Schema(description = "数据库ID列表", example = "[1, 2, 3]")
    private List<Long> databaseIds;

    @Schema(description = "表ID列表", example = "[1, 2, 3]")
    private List<Long> tableIds;

    @Schema(description = "数据类型过滤", example = "[\"VARCHAR\", \"INT\"]")
    private List<String> dataTypes;

    @Schema(description = "标签过滤", example = "[\"重要\", \"敏感\"]")
    private List<String> tags;

    @Schema(description = "排序字段", example = "relevance")
    private String sortBy = "relevance"; // relevance, name, createTime, updateTime

    @Schema(description = "排序方向", example = "DESC")
    private String sortOrder = "DESC"; // ASC, DESC

    @Schema(description = "是否启用模糊搜索", example = "true")
    private Boolean fuzzySearch = false;

    @Schema(description = "模糊搜索级别", example = "3")
    private Integer fuzzyLevel = 3; // 1-5

    @Schema(description = "是否包含描述搜索", example = "true")
    private Boolean includeDescription = true;

    @Schema(description = "是否包含注释搜索", example = "true")
    private Boolean includeComment = true;

    @Schema(description = "最小相关度分数", example = "0.5")
    private Double minRelevanceScore = 0.0;

    @Schema(description = "搜索范围", example = "ALL")
    private String searchScope = "ALL"; // ALL, NAME_ONLY, DESCRIPTION_ONLY, COMMENT_ONLY

    @Schema(description = "是否高亮显示", example = "true")
    private Boolean highlight = true;

    @Schema(description = "创建时间范围-开始", example = "2023-01-01 00:00:00")
    private String createTimeStart;

    @Schema(description = "创建时间范围-结束", example = "2023-12-31 23:59:59")
    private String createTimeEnd;

    @Schema(description = "更新时间范围-开始", example = "2023-01-01 00:00:00")
    private String updateTimeStart;

    @Schema(description = "更新时间范围-结束", example = "2023-12-31 23:59:59")
    private String updateTimeEnd;
}
